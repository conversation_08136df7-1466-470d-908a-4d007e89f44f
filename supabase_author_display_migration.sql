-- Migration: Add author_display column to blog_posts table
-- Run this in Supabase Dashboard > SQL Editor

-- First, check if the column already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'blog_posts' 
        AND column_name = 'author_display'
    ) THEN
        -- Add the author_display column to blog_posts table
        ALTER TABLE blog_posts 
        ADD COLUMN author_display TEXT DEFAULT 'real_name' 
        CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team'));
        
        -- Update existing posts to have the default value
        UPDATE blog_posts 
        SET author_display = 'real_name' 
        WHERE author_display IS NULL;
        
        RAISE NOTICE 'Column author_display added successfully';
    ELSE
        RAISE NOTICE 'Column author_display already exists';
    END IF;
END $$;

-- Verify the column was added
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
AND column_name = 'author_display';
