-- Enhanced Blog Social Features with Anonymous Support
-- Run this AFTER running blog_social_features.sql first
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Update blog_reactions to support anonymous reactions
ALTER TABLE blog_reactions 
ALTER COLUMN user_id DROP NOT NULL,
ADD COLUMN IF NOT EXISTS anonymous_id TEXT,
ADD COLUMN IF NOT EXISTS ip_address INET;

-- Create unique constraint for anonymous reactions
DROP INDEX IF EXISTS unique_user_reaction;
CREATE UNIQUE INDEX unique_user_reaction ON blog_reactions(post_id, user_id, reaction_type) WHERE user_id IS NOT NULL;
CREATE UNIQUE INDEX unique_anonymous_reaction ON blog_reactions(post_id, anonymous_id, reaction_type) WHERE anonymous_id IS NOT NULL;

-- 2. Update blog_comments to support email-based commenting
ALTER TABLE blog_comments
ALTER COLUMN user_id DROP NOT NULL,
ADD COLUMN IF NOT EXISTS guest_email TEXT NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS guest_name TEXT,
ADD COLUMN IF NOT EXISTS is_anonymous BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_token TEXT,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS wants_notifications BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS wants_promotions BOOLEAN DEFAULT FALSE;

-- 3. Create a table for guest comment notifications
CREATE TABLE IF NOT EXISTS guest_comment_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL,
  comment_id UUID NOT NULL REFERENCES blog_comments(id) ON DELETE CASCADE,
  verification_token TEXT NOT NULL,
  signup_reminder_sent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Update RLS policies for anonymous support

-- Drop existing policies for reactions
DROP POLICY IF EXISTS "Anyone can view reactions" ON blog_reactions;
DROP POLICY IF EXISTS "Users can manage their own reactions" ON blog_reactions;

-- New policies for reactions (allow anonymous)
CREATE POLICY "Anyone can view reactions" ON blog_reactions FOR SELECT USING (true);
CREATE POLICY "Users can manage their own reactions" ON blog_reactions 
  FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Anonymous users can create reactions" ON blog_reactions 
  FOR INSERT WITH CHECK (user_id IS NULL AND anonymous_id IS NOT NULL);
CREATE POLICY "Anonymous users can delete their own reactions" ON blog_reactions 
  FOR DELETE USING (user_id IS NULL AND anonymous_id IS NOT NULL);

-- Drop existing policies for comments
DROP POLICY IF EXISTS "Anyone can view comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can create comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can delete their own comments" ON blog_comments;

-- New policies for comments (allow guest comments)
CREATE POLICY "Anyone can view verified comments" ON blog_comments 
  FOR SELECT USING (is_verified = true OR user_id IS NOT NULL);
CREATE POLICY "Users can create comments" ON blog_comments 
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Guest users can create comments" ON blog_comments 
  FOR INSERT WITH CHECK (user_id IS NULL AND guest_email IS NOT NULL);
CREATE POLICY "Users can update their own comments" ON blog_comments 
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own comments" ON blog_comments 
  FOR DELETE USING (auth.uid() = user_id);

-- RLS for guest notifications
ALTER TABLE guest_comment_notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Service can manage notifications" ON guest_comment_notifications FOR ALL USING (true);

-- 5. Function to generate anonymous ID based on IP and user agent
CREATE OR REPLACE FUNCTION generate_anonymous_id(ip_addr INET, user_agent TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN encode(digest(ip_addr::text || user_agent || 'blog_salt', 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql;

-- 6. Function to send signup reminder emails (placeholder)
CREATE OR REPLACE FUNCTION send_signup_reminder(guest_email TEXT, comment_content TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- This would integrate with your email service
  -- For now, just log that a reminder should be sent
  INSERT INTO guest_comment_notifications (email, comment_id, verification_token)
  SELECT guest_email, id, gen_random_uuid()::text
  FROM blog_comments 
  WHERE guest_email = send_signup_reminder.guest_email 
  AND content = comment_content
  LIMIT 1;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 7. Update the count update function to handle anonymous data
CREATE OR REPLACE FUNCTION update_blog_post_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update reaction count (including anonymous)
  UPDATE blog_posts 
  SET reaction_count = (
    SELECT COUNT(*) FROM blog_reactions WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update comment count (only verified comments)
  UPDATE blog_posts 
  SET comment_count = (
    SELECT COUNT(*) FROM blog_comments 
    WHERE post_id = COALESCE(NEW.post_id, OLD.post_id) 
    AND (is_verified = true OR user_id IS NOT NULL)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update save count (authenticated users only)
  UPDATE blog_posts 
  SET save_count = (
    SELECT COUNT(*) FROM blog_saves WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update share count
  UPDATE blog_posts 
  SET share_count = (
    SELECT COUNT(*) FROM blog_shares WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blog_reactions_anonymous_id ON blog_reactions(anonymous_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_guest_email ON blog_comments(guest_email);
CREATE INDEX IF NOT EXISTS idx_blog_comments_verification ON blog_comments(verification_token);
CREATE INDEX IF NOT EXISTS idx_guest_notifications_email ON guest_comment_notifications(email);
