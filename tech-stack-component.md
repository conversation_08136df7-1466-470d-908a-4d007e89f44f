# TechStack Component Documentation

## Overview
The `TechStack` component is a reusable React component that displays a categorized list of technologies with their respective icons. It's designed to showcase technical skills in an organized and visually appealing way.

## Features
- Displays technologies in categorized sections
- Shows technology icons with hover effects
- Responsive grid layout
- Fallback image handling for missing icons
- Customizable through props

## Props

| Prop | Type | Description | Default |
|------|------|-------------|----------|
| `className` | string | Additional CSS classes for styling | `undefined` |

## Data Structure

The component expects an array of `TechCategory` objects with the following structure:

```typescript
interface TechItem {
  name: string;        // Name of the technology
  icon: string;        // URL to the technology's icon
  color?: string;      // Optional color for the icon
}

interface TechCategory {
  title: string;       // Category title (e.g., "Frontend", "Backend")
  items: TechItem[];   // Array of technologies in this category
}
```

## Usage Example

```tsx
import TechStack from '@/components/TechStack';

function MyComponent() {
  return (
    <div>
      <h2>My Tech Stack</h2>
      <TechStack className="my-4" />
    </div>
  );
}
```

## Styling

The component uses Tailwind CSS for styling. You can override the default styles by passing a custom `className` prop.

## Dependencies

- React
- Tailwind CSS
- Lucide React (for icons)

## Implementation Details

### Component Structure
- The component maps through each category and renders a section for it
- Each technology is displayed as a card with an icon and name
- The grid is responsive, showing more columns on larger screens

### Error Handling
- The component includes an `onError` handler for images that fail to load
- If an icon fails to load, it falls back to a placeholder SVG

## Accessibility

- Each technology is wrapped in a button for better keyboard navigation
- Images include alt text for screen readers
- Proper ARIA attributes are used where applicable

## Example Data

Here's an example of the data structure used by the component:

```typescript
const categories = [
  {
    title: "Frontend",
    items: [
      { 
        name: "React", 
        icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg",
        color: "#61DAFB"
      },
      // More frontend technologies...
    ]
  },
  // More categories...
];
```

## Browser Support

The component is compatible with all modern browsers that support:
- CSS Grid
- Flexbox
- ES6+ JavaScript

## Performance Considerations

- Icons are loaded from a CDN (jsDelivr)
- Images are lazy-loaded by default
- The component uses React.memo for performance optimization

## License

This component is part of the Millennial Business Innovations project and is available under the project's license.
