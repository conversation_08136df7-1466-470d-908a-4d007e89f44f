-- Fix <PERSON>'s Owner Role
-- Run this in Supabase Dashboard > SQL Editor
-- This will fix the blog role issue where <PERSON> shows as viewer instead of owner

-- 1. First, drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- 2. Add the new constraint with all roles including owner
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('owner', 'super_admin', 'admin', 'editor', 'viewer'));

-- 3. Update <PERSON>'s role to owner (if profile exists)
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- 4. If <PERSON>'s profile doesn't exist, create it
INSERT INTO profiles (id, email, full_name, role)
SELECT 
    auth.users.id,
    auth.users.email,
    COALESCE(auth.users.raw_user_meta_data->>'full_name', auth.users.email),
    'owner'
FROM auth.users
WHERE auth.users.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM profiles WHERE profiles.email = '<EMAIL>'
);

-- 5. Verify the update worked
SELECT
    'Stephen Profile Check' as check_type,
    id,
    email,
    full_name,
    role,
    created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 6. Show all user roles for verification
SELECT 
    'All Users' as check_type,
    email,
    full_name,
    role,
    created_at
FROM profiles 
ORDER BY 
    CASE role 
        WHEN 'owner' THEN 1
        WHEN 'super_admin' THEN 2
        WHEN 'admin' THEN 3
        WHEN 'editor' THEN 4
        WHEN 'viewer' THEN 5
    END,
    created_at;
