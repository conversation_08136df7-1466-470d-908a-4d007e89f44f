-- Debug script for blog issues
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Check if author_display column exists
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
AND column_name = 'author_display';

-- 2. Check current blog posts with author information
SELECT 
    bp.id,
    bp.title,
    bp.author_id,
    bp.author_display,
    bp.content,
    bp.status,
    bp.created_at,
    bp.updated_at,
    p.full_name as author_name,
    p.email as author_email
FROM blog_posts bp
LEFT JOIN profiles p ON bp.author_id = p.id
ORDER BY bp.updated_at DESC 
LIMIT 3;

-- 3. Check the full table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
ORDER BY ordinal_position;

-- 4. If author_display column doesn't exist, add it
-- (Only run this if the first query returns no results)
/*
ALTER TABLE blog_posts 
ADD COLUMN author_display TEXT DEFAULT 'real_name' 
CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team'));

UPDATE blog_posts 
SET author_display = 'real_name' 
WHERE author_display IS NULL;
*/
