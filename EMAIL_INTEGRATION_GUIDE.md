# 📧 Email Integration System Guide

## Overview

The MBI workflow system now includes a comprehensive email integration system that allows users to connect their preferred email services and send automated emails through workflows.

## Features

### ✅ Supported Email Providers

1. **Gmail** - Using App Passwords
2. **Outlook/Hotmail** - Using SMTP
3. **Resend** - Modern transactional email service
4. **Postmark** - High-deliverability email service
5. **SendGrid** - Comprehensive email platform
6. **Mailgun** - Developer-friendly email service
7. **Custom SMTP** - Any SMTP-compatible email provider

### ✅ Key Features

- **Multiple Integrations**: Users can configure multiple email services
- **Default Integration**: Set a default email service for workflows
- **Test Functionality**: Send test emails to verify configuration
- **Template Variables**: Support for Liquid-style template variables
- **Secure Storage**: Encrypted storage of API keys and credentials
- **User Isolation**: Each user manages their own integrations

## Getting Started

### 1. Access Email Integrations

Navigate to **Admin → Email Integrations** in the admin panel. This section is available to users with admin, super_admin, or owner roles.

### 2. Add Your First Integration

1. Click **"Add Integration"**
2. Choose your email provider
3. Enter the required configuration details
4. Test the integration
5. Save and activate

### 3. Provider-Specific Setup

#### Gmail Setup
1. Enable 2-factor authentication on your Google account
2. Generate an App Password:
   - Go to [Google Account App Passwords](https://myaccount.google.com/apppasswords)
   - Or navigate: Google Account Settings → Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. Use your Gmail address and the 16-character app password

#### Outlook Setup
1. Use your Outlook/Hotmail email address
2. Use your regular password or app password if 2FA is enabled

#### Resend Setup
1. Sign up at [resend.com](https://resend.com)
2. Get your API key from the dashboard
3. Verify your sending domain

#### Postmark Setup
1. Sign up at [postmarkapp.com](https://postmarkapp.com)
2. Create a server and get the Server API Token
3. Verify your sending domain

#### SendGrid Setup
1. Sign up at [sendgrid.com](https://sendgrid.com)
2. Create an API key with Mail Send permissions
3. Verify your sending domain

#### Mailgun Setup
1. Sign up at [mailgun.com](https://mailgun.com)
2. Get your API key and domain from the dashboard
3. Verify your sending domain

#### Custom SMTP Setup
1. Get SMTP settings from your email provider
2. Enter host, port, username, and password
3. Enable TLS/SSL if required

## Using Email Integrations in Workflows

### 1. Create Email Action Node

In the workflow editor:
1. Add an "Action" node
2. Select "Send Email" as the action type
3. Configure the email settings

### 2. Configure Email Action

- **Email Integration**: Select from your configured integrations
- **To Email**: Recipient email (supports variables like `{{ contact.email }}`)
- **Subject**: Email subject (supports variables like `{{ quote.project_type }} Quote`)
- **Email Body**: HTML email content with variable support

### 3. Template Variables

Use Liquid-style variables in your emails:

```liquid
Hello {{ contact.name }},

Thank you for your {{ quote.project_type }} quote request.

Project Details:
- Industry: {{ quote.industry }}
- Budget: {{ quote.budget }}
- Timeline: {{ quote.timeline }}

We'll get back to you within 24 hours.

Best regards,
MBI Team
```

## Database Schema

The system uses two main tables:

### email_integrations
- Stores user email service configurations
- Encrypted credential storage
- Active/inactive status
- Default integration setting

### email_templates
- Reusable email templates
- Variable definitions
- Integration-specific templates

## Security Features

- **Encrypted Storage**: All API keys and passwords are stored securely
- **User Isolation**: Users can only access their own integrations
- **Admin Oversight**: Admins can view all integrations if needed
- **Input Validation**: All inputs are validated and sanitized

## API Integration Details

### Client-Side Email Sending

The system supports direct API calls for:
- Resend
- Postmark
- SendGrid
- Mailgun

### Server-Side Requirements

For SMTP-based providers (Gmail, Outlook, Custom SMTP), you'll need:
- Server-side email sending capability
- Supabase Edge Functions or similar
- SMTP library (like Nodemailer)

## Testing Your Integration

1. Go to Email Integrations page
2. Click "Test" on any integration
3. Enter a test email address
4. Check your inbox for the test email

## Troubleshooting

### Common Issues

1. **Gmail "Less secure app access"**
   - Use App Passwords instead of regular password
   - Ensure 2FA is enabled

2. **API Key Errors**
   - Verify API key is correct and has proper permissions
   - Check if domain is verified with the email service

3. **SMTP Connection Issues**
   - Verify host and port settings
   - Check if TLS/SSL is required
   - Ensure firewall allows SMTP connections

### Error Messages

- **"Integration not found"**: The integration was deleted or doesn't exist
- **"API key invalid"**: Check your API key configuration
- **"Domain not verified"**: Verify your sending domain with the provider
- **"Rate limit exceeded"**: You've hit the provider's sending limits

## Future Enhancements

- **OAuth Integration**: Direct OAuth for Gmail and Outlook
- **Email Templates Library**: Pre-built email templates
- **Advanced Analytics**: Email open/click tracking
- **Bulk Email Support**: Send to multiple recipients
- **Email Scheduling**: Schedule emails for later delivery

## Support

For issues with email integrations:
1. Check the provider's documentation
2. Verify your account settings with the email service
3. Test with a simple email first
4. Contact support if issues persist

---

**Note**: This system is designed for transactional emails (notifications, confirmations, etc.). For marketing emails, consider using dedicated email marketing platforms.
