# OpenRouter Free Models Setup Guide

## 🎯 Overview
Your AI Assistant now uses **FREE models** from OpenRouter for both DeepSeek and Qwen, which means:
- ✅ **No API costs** for DeepSeek R1 and Qwen 72B
- ✅ **Still charge credits** for premium content generation (your business logic)
- ✅ **High-quality models** without the API fees

## 🔑 Setup Steps

### 1. Get OpenRouter API Key (Free)
```bash
# 1. Go to https://openrouter.ai/
# 2. Sign up for a free account
# 3. Go to https://openrouter.ai/keys
# 4. Create a new API key
# 5. Copy the key (starts with sk-or-v1-...)
```

### 2. Add to Supabase Secrets
```bash
# In your Supabase project dashboard:
# Settings → Edge Functions → Environment Variables

OPENROUTER_API_KEY=sk-or-v1-your-key-here
```

### 3. Deploy the Updated Function
```bash
supabase functions deploy ai-chat
```

## 🤖 Free Models Configuration

### **Current Setup:**
```typescript
const modelMap = {
  'deepseek-r1': 'deepseek/deepseek-r1:free',        // FREE DeepSeek R1
  'qwen-plus': 'qwen/qwen-2.5-72b-instruct:free'     // FREE Qwen 72B
}
```

### **Model Capabilities:**

#### **DeepSeek R1 (Free)**
- **Context:** 164K tokens
- **API Cost:** $0.00 (FREE)
- **Quality:** Excellent reasoning
- **Use Case:** Complex workflows, code assistance

#### **Qwen 72B (Free)**  
- **Context:** 128K tokens
- **API Cost:** $0.00 (FREE)
- **Quality:** Excellent content generation
- **Use Case:** Blog posts, creative writing

## 💰 Cost Structure

### **Your Business Model:**
- **Support Questions:** FREE (Gemini Flash + no credits)
- **Blog Generation:** 1 Credit (Qwen 72B Free + 1 credit charge)
- **Workflow Creation:** 1 Credit (DeepSeek R1 Free + 1 credit charge)

### **API Costs:**
- **Gemini Flash:** ~$0.075 per 1M input tokens
- **DeepSeek R1:** $0.00 (FREE tier)
- **Qwen 72B:** $0.00 (FREE tier)

## 🎯 Benefits

### **Cost Savings:**
- **80%+ reduction** in API costs for premium content
- **Free high-quality models** for blog and workflow generation
- **Only pay for Gemini** for support questions

### **Quality Maintained:**
- **DeepSeek R1** is on par with GPT-4 for reasoning
- **Qwen 72B** excels at content generation
- **Same user experience** with better economics

## 🔧 Technical Details

### **Request Flow:**
```
User Request → Smart Detection → Model Selection → API Call

Support Question:
"How do I create a blog?" → Gemini Flash → $0.075/1M tokens → FREE to user

Blog Generation:
"Write a blog about AI" → Qwen 72B Free → $0.00 API cost → 1 credit charged

Workflow Creation:
"Create email automation" → DeepSeek R1 Free → $0.00 API cost → 1 credit charged
```

### **Rate Limits (OpenRouter Free Tier):**
- **DeepSeek R1:** Generous free tier limits
- **Qwen 72B:** Generous free tier limits
- **No daily limits** for most use cases

## 🧪 Testing

### **Test Commands:**
```bash
# Test support (should be free)
"How do I upgrade my plan?"

# Test blog generation (should use Qwen 72B Free + 1 credit)
"Write a blog post about digital marketing"

# Test workflow (should use DeepSeek R1 Free + 1 credit)
"Create a workflow for new customer onboarding"
```

### **Verify in UI:**
- Check model badges show "DeepSeek R1 (Free)" and "Qwen 72B (Free)"
- Confirm credits are only charged for content generation
- Support questions should remain free

## 🚀 Deployment

### **Deploy Command:**
```bash
# Deploy the updated function
supabase functions deploy ai-chat

# Verify deployment
supabase functions list
```

### **Environment Check:**
```bash
# Verify environment variables
supabase secrets list
```

## 📊 Monitoring

### **Check Usage:**
- Monitor OpenRouter dashboard for API usage
- Track credit consumption in your analytics
- Watch for any rate limiting issues

### **Fallback Strategy:**
If free tier limits are hit:
1. Automatic fallback to Gemini Flash
2. User notification about temporary limitations
3. Retry logic for failed requests

## 🎉 Result

You now have:
- **FREE API calls** for premium content generation
- **High-quality AI models** (DeepSeek R1 & Qwen 72B)
- **Same credit system** for your business model
- **Significant cost savings** on API usage

The user experience remains the same, but your API costs are dramatically reduced while maintaining excellent AI quality!
