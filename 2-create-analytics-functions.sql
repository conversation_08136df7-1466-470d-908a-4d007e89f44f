-- Step 2: Create Analytics Functions
-- Run this AFTER creating the indexes
-- You can run this entire file at once in Supabase SQL Editor

-- 1. Create optimized analytics overview function
CREATE OR REPLACE FUNCTION get_analytics_overview(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    total_views BIGINT,
    total_unique_views BIGINT,
    total_clicks BIGINT,
    avg_reading_time INTEGER,
    avg_bounce_rate DECIMAL,
    total_posts BIGINT,
    published_posts BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH post_stats AS (
        SELECT 
            bp.id,
            bp.view_count,
            bp.unique_view_count,
            bp.click_count,
            bp.avg_reading_time,
            bp.bounce_rate
        FROM blog_posts bp
        WHERE bp.status = 'published'
        AND bp.published_at >= p_start_date
        AND bp.published_at <= p_end_date
        AND (p_organization_id IS NULL OR bp.organization_id = p_organization_id)
    )
    SELECT 
        COALESCE(SUM(ps.view_count), 0)::BIGINT,
        COALESCE(SUM(ps.unique_view_count), 0)::BIGINT,
        COALESCE(SUM(ps.click_count), 0)::BIGINT,
        COALESCE(AVG(ps.avg_reading_time), 0)::INTEGER,
        COALESCE(AVG(ps.bounce_rate), 0)::DECIMAL,
        COUNT(*)::BIGINT,
        COUNT(*)::BIGINT
    FROM post_stats ps;
END;
$$ LANGUAGE plpgsql;

-- 2. Create optimized chart data function
CREATE OR REPLACE FUNCTION get_analytics_chart_data(
    p_organization_id UUID DEFAULT NULL,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE(
    date_label TEXT,
    views BIGINT,
    unique_views BIGINT,
    clicks BIGINT,
    avg_reading_time INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH date_series AS (
        SELECT 
            generate_series(
                CURRENT_DATE - INTERVAL '1 day' * (p_days - 1),
                CURRENT_DATE,
                INTERVAL '1 day'
            )::DATE as date_val
    ),
    daily_stats AS (
        SELECT 
            ds.date_val,
            COALESCE(COUNT(bpv.id), 0) as day_views,
            COALESCE(COUNT(DISTINCT bpv.session_id), 0) as day_unique_views,
            COALESCE(COUNT(bpc.id), 0) as day_clicks,
            COALESCE(AVG(bprt.time_spent), 0) as day_avg_reading_time
        FROM date_series ds
        LEFT JOIN blog_post_views bpv ON DATE(bpv.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        LEFT JOIN blog_post_clicks bpc ON DATE(bpc.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bpc.organization_id = p_organization_id)
        LEFT JOIN blog_post_reading_time bprt ON DATE(bprt.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bprt.organization_id = p_organization_id)
        GROUP BY ds.date_val
        ORDER BY ds.date_val
    )
    SELECT 
        TO_CHAR(ds.date_val, 'Mon DD') as date_label,
        ds.day_views,
        ds.day_unique_views,
        ds.day_clicks,
        ds.day_avg_reading_time::INTEGER
    FROM daily_stats ds;
END;
$$ LANGUAGE plpgsql;

-- 3. Create optimized traffic sources function
CREATE OR REPLACE FUNCTION get_traffic_sources(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    referrer TEXT,
    views BIGINT,
    percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH referrer_stats AS (
        SELECT 
            CASE 
                WHEN bpv.referrer IS NULL OR bpv.referrer = '' THEN 'Direct'
                WHEN bpv.referrer LIKE '%google%' THEN 'Google'
                WHEN bpv.referrer LIKE '%facebook%' THEN 'Facebook'
                WHEN bpv.referrer LIKE '%twitter%' OR bpv.referrer LIKE '%t.co%' THEN 'Twitter'
                WHEN bpv.referrer LIKE '%linkedin%' THEN 'LinkedIn'
                WHEN bpv.referrer LIKE '%youtube%' THEN 'YouTube'
                WHEN bpv.referrer LIKE '%instagram%' THEN 'Instagram'
                WHEN bpv.referrer LIKE '%reddit%' THEN 'Reddit'
                WHEN bpv.referrer LIKE '%github%' THEN 'GitHub'
                ELSE regexp_replace(
                    regexp_replace(bpv.referrer, '^https?://', ''),
                    '/.*$', ''
                )
            END as clean_referrer,
            COUNT(*) as referrer_views
        FROM blog_post_views bpv
        WHERE bpv.created_at >= p_start_date
        AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        GROUP BY clean_referrer
    ),
    total_views AS (
        SELECT SUM(referrer_views) as total_count
        FROM referrer_stats
    )
    SELECT 
        rs.clean_referrer,
        rs.referrer_views,
        ROUND((rs.referrer_views::DECIMAL / tv.total_count * 100), 1)
    FROM referrer_stats rs, total_views tv
    ORDER BY rs.referrer_views DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 4. Create optimized device stats function
CREATE OR REPLACE FUNCTION get_device_stats(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days'
)
RETURNS TABLE(
    device_type TEXT,
    views BIGINT,
    percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH device_stats AS (
        SELECT 
            COALESCE(bpv.device_type, 'Unknown') as device,
            COUNT(*) as device_views
        FROM blog_post_views bpv
        WHERE bpv.created_at >= p_start_date
        AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        GROUP BY device
    ),
    total_views AS (
        SELECT SUM(device_views) as total_count
        FROM device_stats
    )
    SELECT 
        ds.device,
        ds.device_views,
        ROUND((ds.device_views::DECIMAL / tv.total_count * 100), 1)
    FROM device_stats ds, total_views tv
    ORDER BY ds.device_views DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. Create optimized top posts function
CREATE OR REPLACE FUNCTION get_top_posts(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_sort_by TEXT DEFAULT 'view_count',
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    post_id UUID,
    title TEXT,
    slug TEXT,
    view_count INTEGER,
    unique_view_count INTEGER,
    click_count INTEGER,
    avg_reading_time INTEGER,
    bounce_rate DECIMAL,
    reaction_count INTEGER,
    comment_count INTEGER,
    share_count INTEGER,
    published_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    EXECUTE format('
        SELECT 
            bp.id,
            bp.title,
            bp.slug,
            bp.view_count,
            bp.unique_view_count,
            bp.click_count,
            bp.avg_reading_time,
            bp.bounce_rate,
            bp.reaction_count,
            bp.comment_count,
            bp.share_count,
            bp.published_at
        FROM blog_posts bp
        WHERE bp.status = ''published''
        AND bp.published_at >= $1
        AND ($2 IS NULL OR bp.organization_id = $2)
        ORDER BY bp.%I DESC NULLS LAST
        LIMIT $3
    ', p_sort_by)
    USING p_start_date, p_organization_id, p_limit;
END;
$$ LANGUAGE plpgsql;

-- 6. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_analytics_overview TO authenticated;
GRANT EXECUTE ON FUNCTION get_analytics_chart_data TO authenticated;
GRANT EXECUTE ON FUNCTION get_traffic_sources TO authenticated;
GRANT EXECUTE ON FUNCTION get_device_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_posts TO authenticated;

SELECT 'Analytics functions created successfully! 🚀' as status;
