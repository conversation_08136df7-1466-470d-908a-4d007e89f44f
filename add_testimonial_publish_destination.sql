-- Add category and tags columns to blog_posts table for testimonial publish destinations
-- Run this in Supabase Dashboard > SQL Editor
-- Safe to run multiple times

-- Add category column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_posts' AND column_name = 'category'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN category TEXT DEFAULT 'blog';
        RAISE NOTICE 'Added category column to blog_posts';
    ELSE
        RAISE NOTICE 'Category column already exists';
    END IF;
END $$;

-- Add tags column if it doesn't exist (using TEXT[] for array of strings)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_posts' AND column_name = 'tags'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE 'Added tags column to blog_posts';
    ELSE
        RAISE NOTICE 'Tags column already exists';
    END IF;
END $$;

-- Set default category for existing posts that don't have one
UPDATE blog_posts 
SET category = 'blog' 
WHERE category IS NULL;

-- Add index on category for better query performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON blog_posts(category);

-- Add index on tags for better query performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_tags ON blog_posts USING GIN(tags);

-- Add comments to document the columns
COMMENT ON COLUMN blog_posts.category IS 'Single category for the blog post (blog, testimonials)';
COMMENT ON COLUMN blog_posts.tags IS 'Array of tags for categorizing and filtering blog posts';

-- Verify the columns were added
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'blog_posts'
AND column_name IN ('category', 'tags')
ORDER BY column_name;

-- Show sample of updated data
SELECT 
    id, 
    title, 
    category, 
    tags, 
    status,
    created_at
FROM blog_posts 
ORDER BY created_at DESC 
LIMIT 5;
