-- Add thumbnail support to blog posts
-- Run this in Supabase Dashboard > SQL Editor
-- Safe to run multiple times

-- Add thumbnail_url column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_posts' AND column_name = 'thumbnail_url'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN thumbnail_url TEXT;
    END IF;
END $$;

-- Verify the column was added
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'blog_posts'
ORDER BY ordinal_position;
