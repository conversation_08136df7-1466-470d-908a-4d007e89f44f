-- Fix Profiles Table Constraints
-- This fixes the role constraint to allow all necessary roles

-- 1. First, let's see what constraints exist
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

-- 2. Drop the existing role constraint if it exists
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find the role constraint name
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'profiles'::regclass 
    AND pg_get_constraintdef(oid) LIKE '%role%';
    
    -- Drop it if found
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE profiles DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

-- 3. Add the correct role constraint
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner'));

-- 4. Add subscription_plan column if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'free';

-- 5. Add subscription_plan constraint
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find any existing subscription_plan constraint
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'profiles'::regclass 
    AND pg_get_constraintdef(oid) LIKE '%subscription_plan%';
    
    -- Drop it if found
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE profiles DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

ALTER TABLE profiles ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise'));

-- 6. Show the current table structure
SELECT 'Current profiles table structure:' as info;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 7. Show current constraints
SELECT 'Current constraints:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

SELECT 'Profiles table constraints fixed!' as status;
