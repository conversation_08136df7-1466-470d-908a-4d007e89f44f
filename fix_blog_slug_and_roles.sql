-- Fix Blog Slug Conflicts and Role Levels
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Check for duplicate slugs in blog_posts
SELECT 
    'Duplicate Slugs Check' as check_type,
    slug, 
    COUNT(*) as count,
    array_agg(id) as post_ids,
    array_agg(title) as titles
FROM blog_posts 
GROUP BY slug 
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- 2. Delete any existing test posts to clear conflicts
DELETE FROM blog_posts WHERE slug = 'test' OR title = 'test' OR slug LIKE 'test-%' OR title LIKE 'test%';

-- 3. Check <PERSON>'s profile and role
SELECT
    'Stephen Profile Check' as check_type,
    id,
    email,
    full_name,
    role,
    created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 4. Ensure <PERSON> has owner role
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- 5. Check all profiles and their roles
SELECT
    'All Profiles' as check_type,
    email,
    full_name,
    role,
    created_at
FROM profiles
ORDER BY 
    CASE role 
        WHEN 'owner' THEN 1
        WHEN 'super_admin' THEN 2
        WHEN 'admin' THEN 3
        WHEN 'editor' THEN 4
    END,
    created_at;

-- 6. Verify blog post policies are still correct
SELECT 
    'Blog Post Policies' as check_type,
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    cmd, 
    qual 
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 7. Clean up any other potential slug conflicts
UPDATE blog_posts
SET slug = slug || '-' || extract(epoch from now())::text
WHERE slug IN (
  SELECT slug
  FROM blog_posts
  GROUP BY slug
  HAVING COUNT(*) > 1
);

-- 8. Add missing columns to blog_posts table for testimonials
-- Add category column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_posts' AND column_name = 'category'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN category TEXT DEFAULT 'blog';
        RAISE NOTICE 'Added category column to blog_posts';
    ELSE
        RAISE NOTICE 'Category column already exists';
    END IF;
END $$;

-- Add tags column if it doesn't exist (using TEXT[] for array of strings)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_posts' AND column_name = 'tags'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE 'Added tags column to blog_posts';
    ELSE
        RAISE NOTICE 'Tags column already exists';
    END IF;
END $$;

-- Add custom_name column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_posts' AND column_name = 'custom_name'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN custom_name TEXT;
        RAISE NOTICE 'Added custom_name column to blog_posts';
    ELSE
        RAISE NOTICE 'Custom_name column already exists';
    END IF;
END $$;

-- Update author_display constraint to include custom_name
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find and drop existing check constraints on author_display
    FOR constraint_name IN
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_name = 'blog_posts'
        AND tc.constraint_type = 'CHECK'
        AND cc.check_clause LIKE '%author_display%'
    LOOP
        EXECUTE 'ALTER TABLE blog_posts DROP CONSTRAINT IF EXISTS ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END LOOP;
END $$;

-- Add new CHECK constraint that includes custom_name
ALTER TABLE blog_posts
ADD CONSTRAINT blog_posts_author_display_check
CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team', 'custom_name'));

-- Set default category for existing posts that don't have one
UPDATE blog_posts
SET category = 'blog'
WHERE category IS NULL;

-- 9. Clean up any testimonial-related slug conflicts
DELETE FROM blog_posts
WHERE slug LIKE '%-success-story-%'
AND (title LIKE 'Success Story: test%' OR title = 'Success Story: test');

-- 10. Show any remaining blog posts with new columns
SELECT
    'Existing Blog Posts' as check_type,
    id,
    title,
    slug,
    status,
    category,
    tags,
    author_id,
    author_display,
    custom_name,
    created_at
FROM blog_posts
ORDER BY created_at DESC
LIMIT 10;

-- 11. Show table structure to verify all columns exist
SELECT
    'Blog Posts Table Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'blog_posts'
ORDER BY ordinal_position;
