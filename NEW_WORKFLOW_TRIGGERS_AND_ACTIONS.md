# New Workflow Triggers and Actions

This document outlines the new triggers and actions that have been added to your workflow system.

## 🚀 New Triggers

### 1. **Blog Post Created** (`blog_created`)
- **Triggers when**: A new blog post is created
- **Available data**:
  - `blog_post` - Full blog post object
  - `post_id` - Blog post ID
  - `title` - Blog post title
  - `author_id` - Author's user ID
  - `status` - Post status (draft/published)
  - `category` - Post category

### 2. **Blog Comment Added** (`blog_comment`)
- **Triggers when**: Someone comments on a blog post
- **Available data**:
  - `comment` - Full comment object
  - `comment_id` - Comment ID
  - `post_id` - Blog post ID
  - `user_id` - Commenter's user ID (if authenticated)
  - `content` - Comment content
  - `guest_email` - Guest email (if anonymous)
  - `guest_name` - Guest name (if provided)
  - `is_anonymous` - Whether comment is anonymous
  - `parent_id` - Parent comment ID (for replies)

### 3. **Blog Post Reaction** (`blog_reaction`)
- **Triggers when**: Someone reacts to a blog post (like, heart, etc.)
- **Available data**:
  - `reaction` - Full reaction object
  - `reaction_id` - Reaction ID
  - `post_id` - Blog post ID
  - `user_id` - User ID (if authenticated)
  - `anonymous_id` - Anonymous user ID
  - `reaction_type` - Type of reaction (like, heart, clap)

### 4. **Low Credits Warning** (`low_credits`)
- **Triggers when**: User's credits reach 80% usage threshold
- **Available data**:
  - `organization_id` - Organization ID
  - `credit_type` - Type of credits (workflow/ai)
  - `credits_used` - Number of credits used
  - `credits_limit` - Credit limit
  - `usage_percentage` - Usage percentage
  - `threshold` - Threshold that triggered the alert (80)

## 🎯 New Actions

### 1. **Send Notification** (`send_notification`)
- **Purpose**: Send in-app notifications to users
- **Features**:
  - Automatically determines recipients based on trigger type
  - Creates notifications in the database
  - Supports real-time notifications
  - Different notification types for different events

**Automatic Recipients**:
- **Blog comments**: Notifies blog author and parent comment author (for replies)
- **Blog reactions**: Notifies blog author (if different from reactor)
- **Low credits**: Notifies all organization members

### 2. **Auto Response** (`auto_response`)
- **Purpose**: Automatically respond to blog comments
- **Features**:
  - Customizable response templates
  - Variable substitution support
  - Only responds to comments not from blog author
  - Creates response as a reply to the original comment

**Available Variables**:
- `{{ commenter_name }}` - Name of the commenter
- `{{ post_title }}` - Title of the blog post
- `{{ current_date }}` - Current date
- `{{ current_time }}` - Current time

## 📋 Workflow Templates

Several pre-built workflow templates have been added:

### 1. **Blog Comment Notifications**
- Trigger: Blog Comment Added
- Action: Send Notification to blog author

### 2. **Blog Comment Auto-Response**
- Trigger: Blog Comment Added
- Action: Wait 2 minutes → Send Auto Response

### 3. **Blog Reaction Notifications**
- Trigger: Blog Post Reaction
- Action: Send Notification to blog author

### 4. **Low Credits Alert**
- Trigger: Low Credits Warning
- Actions: Send Email Alert → Send In-App Notification

### 5. **New Blog Post Notifications**
- Trigger: Blog Post Created
- Condition: Check if Published
- Action: Notify Team Members

## 🔧 Technical Implementation

### Database Changes
- **New triggers**: Added database triggers for `blog_posts`, `blog_comments`, and `blog_reactions` tables
- **Credit monitoring**: Added trigger on `organizations` table to monitor credit usage
- **System notifications**: New `system_notifications` table for in-app notifications

### Frontend Changes
- **Workflow Editor**: Updated with new trigger and action options
- **System Notifications**: New component for displaying in-app notifications
- **Real-time updates**: Notifications appear in real-time using Supabase subscriptions

### Edge Function Updates
- **New action handlers**: Added `executeSendNotificationAction` and `executeAutoResponseAction`
- **Smart recipient detection**: Automatically determines who should receive notifications
- **Template processing**: Supports variable substitution in auto-responses

## 🎨 UI Components

### SystemNotifications Component
A new React component that provides:
- Bell icon with unread count badge
- Dropdown with notification list
- Real-time notification updates
- Mark as read functionality
- Different icons for different notification types

**Usage**:
```tsx
import SystemNotifications from '@/components/SystemNotifications'

// Add to your navigation or header
<SystemNotifications />
```

## 🚀 Getting Started

1. **Run the migrations**:
   ```sql
   -- Run these in your Supabase SQL Editor
   -- 20241221000001_fix_workflow_triggers.sql (updated)
   -- 20241222000000_add_credit_monitoring.sql
   -- 20241222000001_add_blog_workflow_templates.sql
   ```

2. **Add SystemNotifications to your app**:
   ```tsx
   // In your main layout or navigation component
   import SystemNotifications from '@/components/SystemNotifications'
   
   <SystemNotifications />
   ```

3. **Create workflows**:
   - Go to Admin → Automations
   - Create new workflow or use templates
   - Select the new triggers and actions
   - Configure and activate

## 📊 Available Variables by Trigger

### Blog Comment Trigger
- `{{ comment.content }}` - Comment text
- `{{ commenter_name }}` - Commenter's name
- `{{ post_title }}` - Blog post title
- `{{ guest_email }}` - Guest email (if anonymous)

### Blog Reaction Trigger
- `{{ reaction_type }}` - Type of reaction
- `{{ post_title }}` - Blog post title
- `{{ user_name }}` - Reactor's name

### Low Credits Trigger
- `{{ credit_type }}` - workflow or ai
- `{{ usage_percentage }}` - Percentage used
- `{{ credits_used }}` - Number used
- `{{ credits_limit }}` - Total limit

### Blog Created Trigger
- `{{ blog_post.title }}` - Post title
- `{{ blog_post.category }}` - Post category
- `{{ blog_post.status }}` - Post status
- `{{ author_name }}` - Author's name

## 🔄 Real-time Features

- **Live notifications**: New notifications appear instantly
- **Unread count**: Bell icon shows unread notification count
- **Auto-refresh**: Notification list updates in real-time
- **Toast notifications**: Brief popup for new notifications

All triggers and actions are now fully functional and ready to use! 🎉
