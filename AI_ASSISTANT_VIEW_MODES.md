# AI Assistant View Modes

## Overview
The AI Assistant now supports multiple view modes to enhance your workflow and provide the best experience for different use cases.

## Available View Modes

### 🔸 **Compact Mode** (Default)
- **Size**: 384px × 500px
- **Position**: Bottom-right corner
- **Best for**: Quick questions and brief interactions
- **Keyboard**: `Ctrl/Cmd + 1`

### 📱 **Sidebar Mode**
- **Size**: 384px × Full height
- **Position**: Right side of screen
- **Best for**: Extended conversations while working
- **Keyboard**: `Ctrl/Cmd + 2`

### 🖥️ **Fullscreen Mode**
- **Size**: Full screen
- **Position**: Covers entire viewport
- **Best for**: Deep conversations, content creation, complex tasks
- **Keyboard**: `Ctrl/Cmd + 3` (Exit with `Esc`)

### 🪟 **Detached Mode**
- **Size**: Full screen overlay
- **Position**: Separate layer
- **Best for**: Multitasking, reference while working in other apps
- **Keyboard**: `Ctrl/Cmd + 4`

## Features by Mode

### Compact Mode
- ✅ Quick access floating button
- ✅ Minimal screen real estate
- ✅ Perfect for quick support questions
- ✅ Maintains context while browsing

### Sidebar Mode
- ✅ Persistent side panel
- ✅ Work alongside the main interface
- ✅ Great for ongoing conversations
- ✅ Easy to reference while navigating

### Fullscreen Mode
- ✅ Maximum reading space
- ✅ Larger text for better readability
- ✅ Immersive conversation experience
- ✅ Perfect for content generation

### Detached Mode
- ✅ Independent overlay window
- ✅ Can work with other applications
- ✅ Maintains conversation state
- ✅ Professional presentation mode

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl/Cmd + 1` | Switch to Compact Mode |
| `Ctrl/Cmd + 2` | Switch to Sidebar Mode |
| `Ctrl/Cmd + 3` | Switch to Fullscreen Mode |
| `Ctrl/Cmd + 4` | Switch to Detached Mode |
| `Esc` | Exit Fullscreen Mode |
| `Enter` | Send message |
| `Shift + Enter` | New line in message |

## View Mode Controls

### Header Controls
Each mode has intuitive controls in the header:

1. **💬 Compact**: Small floating window
2. **📋 Sidebar**: Right-side panel
3. **⛶ Fullscreen**: Full screen (toggles to minimize)
4. **🔗 Detached**: Independent overlay
5. **❓ Help**: Shows keyboard shortcuts
6. **✕ Close**: Close the assistant

### Smart Switching
- Modes remember your conversation
- Seamless transitions between views
- Context is preserved across switches
- Model selection stays consistent

## Use Cases

### **Compact Mode** - Quick Support
```
"How do I create a blog post?"
"What's included in the Pro plan?"
"Where do I find my API keys?"
```

### **Sidebar Mode** - Extended Help
```
"Help me set up a complex workflow"
"Guide me through email integration"
"Explain the analytics dashboard"
```

### **Fullscreen Mode** - Content Creation
```
"Write a comprehensive blog post about AI trends"
"Create a detailed product description"
"Help me draft a professional email sequence"
```

### **Detached Mode** - Reference & Multitasking
```
"Keep this conversation open while I work"
"I need to reference this while coding"
"Present this to my team"
```

## Technical Implementation

### Responsive Design
- **Compact**: Fixed 384×500px
- **Sidebar**: 384px width, 100vh height
- **Fullscreen**: 100vw × 100vh
- **Detached**: 100vw × 100vh overlay

### State Management
- View mode persists during session
- Messages sync across mode switches
- Model selection maintained
- Credit tracking continues

### Performance
- Smooth transitions with CSS transforms
- Optimized rendering for each mode
- Efficient message handling
- Minimal re-renders on mode switch

## Customization Options

### For Developers
```typescript
// Customize view mode styles
const getContainerStyles = (mode: ViewMode) => {
  switch (mode) {
    case 'compact': return 'fixed bottom-6 right-6 w-96 h-[500px]'
    case 'sidebar': return 'fixed top-0 right-0 w-96 h-full'
    case 'fullscreen': return 'fixed inset-0 w-full h-full'
    // Add custom modes here
  }
}
```

### Configuration
- Default view mode can be set
- Keyboard shortcuts can be customized
- Transition animations adjustable
- Size constraints configurable

## Browser Compatibility

### Supported Features
- ✅ All modern browsers (Chrome, Firefox, Safari, Edge)
- ✅ Keyboard shortcuts work universally
- ✅ Responsive design adapts to screen size
- ✅ Touch-friendly controls on mobile

### Mobile Considerations
- Compact mode works well on tablets
- Fullscreen mode recommended for phones
- Touch gestures for mode switching
- Responsive text sizing

## Future Enhancements

### Planned Features
1. **True Detached Windows**: Actual separate browser windows
2. **Picture-in-Picture**: Floating mini-window
3. **Split Screen**: Side-by-side with main content
4. **Custom Layouts**: User-defined positions and sizes
5. **Multi-Monitor**: Support for external displays

### Integration Ideas
1. **Slack Integration**: Same view modes in Slack
2. **Mobile App**: Native view modes
3. **Desktop App**: System-level window management
4. **Browser Extension**: Overlay on any website

## Best Practices

### When to Use Each Mode

**Compact Mode** ✨
- Quick questions during work
- Brief clarifications
- Status checks
- Simple tasks

**Sidebar Mode** 📋
- Learning new features
- Step-by-step guidance
- Ongoing conversations
- Reference while working

**Fullscreen Mode** 🖥️
- Content creation
- Complex problem solving
- Deep conversations
- Focused work sessions

**Detached Mode** 🪟
- Presentations
- Team collaboration
- Multi-app workflows
- Professional meetings

### Tips for Best Experience
1. **Use keyboard shortcuts** for quick switching
2. **Start compact** for most interactions
3. **Go fullscreen** for content creation
4. **Use sidebar** for tutorials
5. **Detach** for presentations

## Troubleshooting

### Common Issues
1. **Mode not switching**: Check if chat is open
2. **Keyboard shortcuts not working**: Ensure chat has focus
3. **Layout issues**: Refresh the page
4. **Performance**: Close other browser tabs

### Support
If you experience issues with view modes:
1. Try refreshing the page
2. Check browser console for errors
3. Ensure you're using a supported browser
4. Contact support with specific mode and browser info

---

**Enjoy your enhanced AI Assistant experience!** 🚀

The new view modes make it easier than ever to get help exactly when and how you need it.
