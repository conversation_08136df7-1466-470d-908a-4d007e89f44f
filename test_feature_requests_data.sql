-- Test data for feature requests system
-- Run this in Supabase Dashboard > SQL Editor to add sample data

-- Insert sample boards (if they don't exist)
INSERT INTO feature_request_boards (name, description, slug, color, icon, sort_order) VALUES
('Feature Requests', 'New feature ideas and enhancements', 'feature-requests', '#3B82F6', 'lightbulb', 1),
('Integrations', 'Third-party service integrations', 'integrations', '#10B981', 'plug', 2),
('Bug Reports', 'Issues and bugs that need fixing', 'bug-reports', '#EF4444', 'bug', 3),
('Improvements', 'Performance and UX improvements', 'improvements', '#8B5CF6', 'trending-up', 4)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample statuses (if they don't exist)
INSERT INTO feature_request_statuses (name, description, slug, color, icon, sort_order) VALUES
('Planned', 'Features planned for future development', 'planned', '#6B7280', 'clock', 1),
('In Progress', 'Currently being worked on', 'in-progress', '#F59E0B', 'play-circle', 2),
('Complete', 'Finished and deployed', 'complete', '#10B981', 'check-circle', 3),
('On Hold', 'Temporarily paused', 'on-hold', '#8B5CF6', 'pause-circle', 4),
('Rejected', 'Will not be implemented', 'rejected', '#EF4444', 'x-circle', 5)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample feature requests
WITH boards AS (
  SELECT id, slug FROM feature_request_boards
),
statuses AS (
  SELECT id, slug FROM feature_request_statuses
)
INSERT INTO feature_requests (
  board_id, 
  status_id, 
  title, 
  description, 
  priority, 
  author_name, 
  author_email,
  upvotes_count
) VALUES
(
  (SELECT id FROM boards WHERE slug = 'feature-requests'),
  (SELECT id FROM statuses WHERE slug = 'planned'),
  'Dark Mode Toggle',
  'Add a dark mode toggle to the main navigation for better user experience during night time usage.',
  'medium',
  'John Doe',
  '<EMAIL>',
  15
),
(
  (SELECT id FROM boards WHERE slug = 'feature-requests'),
  (SELECT id FROM statuses WHERE slug = 'in-progress'),
  'Advanced Search Filters',
  'Implement advanced search functionality with filters for date range, categories, and content type.',
  'high',
  'Jane Smith',
  '<EMAIL>',
  23
),
(
  (SELECT id FROM boards WHERE slug = 'integrations'),
  (SELECT id FROM statuses WHERE slug = 'planned'),
  'Slack Integration',
  'Connect with Slack to send notifications and updates directly to team channels.',
  'medium',
  'Mike Johnson',
  '<EMAIL>',
  8
),
(
  (SELECT id FROM boards WHERE slug = 'bug-reports'),
  (SELECT id FROM statuses WHERE slug = 'in-progress'),
  'Mobile Responsive Issues',
  'Fix layout issues on mobile devices, particularly with the navigation menu and form elements.',
  'high',
  'Sarah Wilson',
  '<EMAIL>',
  31
),
(
  (SELECT id FROM boards WHERE slug = 'improvements'),
  (SELECT id FROM statuses WHERE slug = 'complete'),
  'Page Load Speed Optimization',
  'Optimize images and implement lazy loading to improve overall page load performance.',
  'high',
  'Alex Brown',
  '<EMAIL>',
  42
),
(
  (SELECT id FROM boards WHERE slug = 'feature-requests'),
  (SELECT id FROM statuses WHERE slug = 'on-hold'),
  'Multi-language Support',
  'Add internationalization support for multiple languages including Spanish, French, and German.',
  'low',
  'Maria Garcia',
  '<EMAIL>',
  12
),
(
  (SELECT id FROM boards WHERE slug = 'integrations'),
  (SELECT id FROM statuses WHERE slug = 'complete'),
  'Google Analytics Integration',
  'Integrate Google Analytics for better tracking and insights into user behavior.',
  'medium',
  'David Lee',
  '<EMAIL>',
  19
),
(
  (SELECT id FROM boards WHERE slug = 'bug-reports'),
  (SELECT id FROM statuses WHERE slug = 'rejected'),
  'Legacy Browser Support',
  'Add support for Internet Explorer 11 and older browsers.',
  'low',
  'Tom Anderson',
  '<EMAIL>',
  3
)
ON CONFLICT DO NOTHING;

-- Verify the data was inserted
SELECT 
  fr.title,
  b.name as board,
  s.name as status,
  fr.upvotes_count,
  fr.priority
FROM feature_requests fr
JOIN feature_request_boards b ON fr.board_id = b.id
LEFT JOIN feature_request_statuses s ON fr.status_id = s.id
ORDER BY fr.created_at DESC;
