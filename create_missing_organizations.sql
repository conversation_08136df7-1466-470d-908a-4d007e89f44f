-- Create Missing Organizations for Existing Users
-- This script creates personal organizations for users who don't have one

-- 1. First, ensure the organizations table exists with all required columns
DO $$
BEGIN
  -- Add ai_credits_used column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'ai_credits_used') THEN
    ALTER TABLE organizations ADD COLUMN ai_credits_used INTEGER DEFAULT 0;
  END IF;

  -- Add ai_credits_limit column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'ai_credits_limit') THEN
    ALTER TABLE organizations ADD COLUMN ai_credits_limit INTEGER DEFAULT 50;
  END IF;

  -- Add workflow_credits_used column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'workflow_credits_used') THEN
    ALTER TABLE organizations ADD COLUMN workflow_credits_used INTEGER DEFAULT 0;
  END IF;

  -- Add workflow_credits_limit column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organizations' AND column_name = 'workflow_credits_limit') THEN
    ALTER TABLE organizations ADD COLUMN workflow_credits_limit INTEGER DEFAULT 100;
  END IF;
END $$;

-- 2. Create personal organizations for users who don't have one
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
SELECT
  COALESCE(p.full_name, p.email) || '''s Workspace',
  'user-' || p.id,
  'free',
  100,  -- Free tier workflow credits
  50    -- Free tier AI credits
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.role = 'owner'
)
AND p.email != '<EMAIL>'  -- Skip Stephen as he has special handling
ON CONFLICT (slug) DO NOTHING;

-- 3. Add users as owners of their personal organizations
INSERT INTO organization_members (organization_id, user_id, role, joined_at)
SELECT 
  o.id,
  p.id,
  'owner',
  NOW()
FROM profiles p
JOIN organizations o ON o.slug = 'user-' || p.id
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.organization_id = o.id AND om.user_id = p.id
)
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- 4. Verify the results
SELECT 'Organizations created:' as info;
SELECT 
  o.name,
  o.slug,
  o.subscription_plan,
  p.email as owner_email
FROM organizations o
JOIN organization_members om ON om.organization_id = o.id AND om.role = 'owner'
JOIN profiles p ON p.id = om.user_id
WHERE o.slug LIKE 'user-%'
ORDER BY o.created_at;

SELECT 'Users without organizations (should be empty):' as info;
SELECT 
  p.email,
  p.full_name
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.is_active = true
)
ORDER BY p.created_at;

SELECT 'Organization setup complete!' as status;
