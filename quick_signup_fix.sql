-- Quick Sign-up Fix
-- Run this in your Supabase SQL Editor to fix immediate sign-up issues

-- 1. Temporarily disable the trigger to prevent errors
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 2. Fix role constraints
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('user', 'editor', 'admin', 'super_admin', 'owner', 'saas_owner', 'platform_owner'));

-- 3. Create a simple, safe trigger function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Just create the profile, skip organization for now
  INSERT INTO profiles (id, email, full_name, role, subscription_plan)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    'user',
    'free'
  )
  ON CONFLICT (id) DO UPDATE SET
    email = NEW.email,
    full_name = COALESCE(NEW.raw_user_meta_data->>'full_name', profiles.full_name, NEW.email);
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the signup
    RAISE LOG 'Profile creation error for %: %', NEW.email, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Recreate the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 5. Enable RLS but with permissive policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Enable profile creation during signup" ON profiles;

-- Create simple, permissive policies
CREATE POLICY "Users can manage own profile" ON profiles
  FOR ALL USING (auth.uid() = id);

-- Allow profile creation during signup (this is crucial)
CREATE POLICY "Enable profile creation" ON profiles
  FOR INSERT WITH CHECK (true);

-- 6. Test the fix
SELECT 'Sign-up fix applied successfully!' as status;
SELECT 'You can now test sign-up functionality' as next_step;
