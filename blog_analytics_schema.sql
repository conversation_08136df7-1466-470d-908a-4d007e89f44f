-- Blog Analytics Database Schema
-- Run this in Supabase Dashboard > SQL Editor
-- This creates comprehensive analytics tracking for blog posts

-- 1. Blog Post Views/Traffic Tracking
CREATE TABLE IF NOT EXISTS blog_post_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL, -- Nullable for anonymous views
  session_id TEXT, -- For anonymous tracking
  ip_address INET, -- For unique visitor tracking
  user_agent TEXT, -- Browser/device info
  referrer TEXT, -- Where they came from
  country TEXT, -- Geolocation data
  city TEXT,
  device_type TEXT, -- mobile, desktop, tablet
  browser TEXT, -- chrome, firefox, safari, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Blog Post Click Tracking (for external links, CTAs, etc.)
CREATE TABLE IF NOT EXISTS blog_post_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  session_id TEXT,
  click_type TEXT NOT NULL, -- 'external_link', 'cta_button', 'author_link', 'share_button'
  click_target TEXT, -- URL or element clicked
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Blog Post Reading Time Tracking
CREATE TABLE IF NOT EXISTS blog_post_reading_time (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  session_id TEXT,
  time_spent INTEGER NOT NULL, -- seconds spent reading
  scroll_percentage INTEGER, -- how far they scrolled (0-100)
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add analytics columns to blog_posts for performance
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS unique_view_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS click_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS avg_reading_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS bounce_rate DECIMAL(5,2) DEFAULT 0.00;

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blog_post_views_post_id ON blog_post_views(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_created_at ON blog_post_views(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_ip_session ON blog_post_views(ip_address, session_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_post_id ON blog_post_clicks(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_post_id ON blog_post_reading_time(post_id);

-- 6. Function to update blog post analytics counts
CREATE OR REPLACE FUNCTION update_blog_post_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update view counts
  IF TG_TABLE_NAME = 'blog_post_views' THEN
    UPDATE blog_posts 
    SET 
      view_count = (SELECT COUNT(*) FROM blog_post_views WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)),
      unique_view_count = (
        SELECT COUNT(DISTINCT COALESCE(user_id::text, session_id, ip_address::text)) 
        FROM blog_post_views 
        WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
      )
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;
  
  -- Update click counts
  IF TG_TABLE_NAME = 'blog_post_clicks' THEN
    UPDATE blog_posts 
    SET click_count = (SELECT COUNT(*) FROM blog_post_clicks WHERE post_id = COALESCE(NEW.post_id, OLD.post_id))
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;
  
  -- Update reading time averages
  IF TG_TABLE_NAME = 'blog_post_reading_time' THEN
    UPDATE blog_posts 
    SET 
      avg_reading_time = (
        SELECT COALESCE(AVG(time_spent), 0)::INTEGER 
        FROM blog_post_reading_time 
        WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
      ),
      bounce_rate = (
        SELECT CASE 
          WHEN COUNT(*) = 0 THEN 0
          ELSE (COUNT(*) FILTER (WHERE time_spent < 30) * 100.0 / COUNT(*))::DECIMAL(5,2)
        END
        FROM blog_post_reading_time 
        WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
      )
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 7. Create triggers to automatically update analytics
DROP TRIGGER IF EXISTS trigger_update_blog_views ON blog_post_views;
CREATE TRIGGER trigger_update_blog_views
  AFTER INSERT OR DELETE ON blog_post_views
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_analytics();

DROP TRIGGER IF EXISTS trigger_update_blog_clicks ON blog_post_clicks;
CREATE TRIGGER trigger_update_blog_clicks
  AFTER INSERT OR DELETE ON blog_post_clicks
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_analytics();

DROP TRIGGER IF EXISTS trigger_update_blog_reading_time ON blog_post_reading_time;
CREATE TRIGGER trigger_update_blog_reading_time
  AFTER INSERT OR UPDATE OR DELETE ON blog_post_reading_time
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_analytics();

-- 8. Enable Row Level Security
ALTER TABLE blog_post_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reading_time ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies (allow all for now, can be restricted later)
CREATE POLICY "Allow all access to blog_post_views" ON blog_post_views FOR ALL USING (true);
CREATE POLICY "Allow all access to blog_post_clicks" ON blog_post_clicks FOR ALL USING (true);
CREATE POLICY "Allow all access to blog_post_reading_time" ON blog_post_reading_time FOR ALL USING (true);

SELECT 'Blog analytics schema created successfully!' as status;
