-- Multi-Tenant SaaS Platform Migration (Simple Version)
-- This creates the workspace/organization structure with subscription plans

-- 1. First, ensure we have the profiles table with proper structure
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner')),
  subscription_plan TEXT DEFAULT 'free' CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for profiles
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles
  FOR ALL USING (auth.uid() = id);

-- 2. Create Organizations/Workspaces table
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  avatar_url TEXT,
  subscription_plan TEXT DEFAULT 'free' CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trialing')),
  workflow_credits_used INTEGER DEFAULT 0,
  workflow_credits_limit INTEGER DEFAULT 0,
  ai_credits_used INTEGER DEFAULT 0,
  ai_credits_limit INTEGER DEFAULT 0,
  credits_reset_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 month'),
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create Organization Members table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'editor' CHECK (role IN ('owner', 'admin', 'editor', 'viewer')),
  invited_by UUID REFERENCES profiles(id),
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  joined_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(organization_id, user_id)
);

-- 4. Create Workspace Invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'editor' CHECK (role IN ('admin', 'editor', 'viewer')),
  invited_by UUID NOT NULL REFERENCES profiles(id),
  token TEXT UNIQUE NOT NULL DEFAULT gen_random_uuid(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create Workflow Credits Usage Log
CREATE TABLE IF NOT EXISTS workflow_credit_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_id UUID, -- Can be null for manual actions
  action_type TEXT NOT NULL, -- email_send, webhook_call, sms_send, etc.
  credits_used INTEGER NOT NULL DEFAULT 1,
  execution_id TEXT, -- For tracking specific workflow runs
  metadata JSONB, -- Store additional context
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create Support Access Log (for troubleshooting)
CREATE TABLE IF NOT EXISTS support_access_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  support_user_id UUID NOT NULL REFERENCES profiles(id),
  target_organization_id UUID NOT NULL REFERENCES organizations(id),
  target_user_id UUID REFERENCES profiles(id),
  access_reason TEXT NOT NULL,
  access_granted_by UUID REFERENCES profiles(id), -- The user who granted access
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true
);

-- 7. Add organization_id to existing tables (if they exist)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'blog_posts') THEN
    ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workflows') THEN
    ALTER TABLE workflows ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feature_requests') THEN
    ALTER TABLE feature_requests ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
  END IF;
END $$;

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_workflow_credit_usage_org_id ON workflow_credit_usage(organization_id);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_token ON workspace_invitations(token);

-- 9. Create default organization for MBI
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
VALUES ('Millennial Business Innovations', 'mbi', 'enterprise', 25000, -1)
ON CONFLICT (slug) DO NOTHING;

-- 10. Enable RLS on new tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_credit_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for multi-tenant access

-- Organizations: Users can only see organizations they're members of
DROP POLICY IF EXISTS "Users can view their organizations" ON organizations;
CREATE POLICY "Users can view their organizations" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- Organization Members: Users can see members of their organizations
DROP POLICY IF EXISTS "Users can view organization members" ON organization_members;
CREATE POLICY "Users can view organization members" ON organization_members
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- 12. Create functions for credit management
CREATE OR REPLACE FUNCTION check_workflow_credits(org_id UUID, credits_needed INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_plan TEXT;
  credits_limit INTEGER;
  credits_used INTEGER;
BEGIN
  SELECT subscription_plan, workflow_credits_limit, workflow_credits_used
  INTO current_plan, credits_limit, credits_used
  FROM organizations
  WHERE id = org_id;
  
  -- -1 means unlimited
  IF credits_limit = -1 THEN
    RETURN TRUE;
  END IF;
  
  -- Check if adding credits would exceed limit
  RETURN (credits_used + credits_needed) <= credits_limit;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION check_ai_credits(org_id UUID, credits_needed INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_plan TEXT;
  credits_limit INTEGER;
  credits_used INTEGER;
BEGIN
  SELECT subscription_plan, ai_credits_limit, ai_credits_used
  INTO current_plan, credits_limit, credits_used
  FROM organizations
  WHERE id = org_id;
  
  -- -1 means unlimited
  IF credits_limit = -1 THEN
    RETURN TRUE;
  END IF;
  
  -- Check if adding credits would exceed limit
  RETURN (credits_used + credits_needed) <= credits_limit;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION consume_workflow_credits(
  org_id UUID, 
  credits_to_consume INTEGER,
  action_type TEXT,
  workflow_id UUID DEFAULT NULL,
  execution_id TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  can_consume BOOLEAN;
BEGIN
  -- Check if organization has enough credits
  SELECT check_workflow_credits(org_id, credits_to_consume) INTO can_consume;
  
  IF NOT can_consume THEN
    RETURN FALSE;
  END IF;
  
  -- Consume the credits
  UPDATE organizations 
  SET workflow_credits_used = workflow_credits_used + credits_to_consume
  WHERE id = org_id;
  
  -- Log the usage
  INSERT INTO workflow_credit_usage (
    organization_id, 
    workflow_id, 
    action_type, 
    credits_used, 
    execution_id
  ) VALUES (
    org_id, 
    workflow_id, 
    action_type, 
    credits_to_consume, 
    execution_id
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION consume_ai_credits(
  org_id UUID, 
  credits_to_consume INTEGER,
  action_type TEXT,
  metadata JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  can_consume BOOLEAN;
BEGIN
  -- Check if organization has enough credits
  SELECT check_ai_credits(org_id, credits_to_consume) INTO can_consume;
  
  IF NOT can_consume THEN
    RETURN FALSE;
  END IF;
  
  -- Consume the credits
  UPDATE organizations 
  SET ai_credits_used = ai_credits_used + credits_to_consume
  WHERE id = org_id;
  
  -- Log the usage
  INSERT INTO workflow_credit_usage (
    organization_id, 
    workflow_id, 
    action_type, 
    credits_used, 
    metadata
  ) VALUES (
    org_id, 
    NULL, -- AI credits don't have workflow_id
    action_type, 
    credits_to_consume, 
    metadata
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 13. Show results
SELECT 'Migration completed successfully!' as status;

-- Show organizations created
SELECT 'Organizations:' as info;
SELECT name, slug, subscription_plan FROM organizations ORDER BY created_at;
