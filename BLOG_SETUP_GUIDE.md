# Blog Feature Setup Guide

This guide will help you set up the blog feature with Supabase authentication and database.

## 🚀 Quick Setup

### 1. Install Dependencies (Already Done)
```bash
npm install @supabase/supabase-js @tiptap/react @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-link @tiptap/extension-youtube @tiptap/extension-placeholder react-dropzone
```

### 2. Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be ready
4. Go to Settings > API to get your keys

### 3. Environment Variables
1. Copy `.env.example` to `.env.local`
2. Fill in your Supabase credentials:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

### 4. Database Setup
Run these SQL commands in your Supabase SQL Editor:

```sql
-- Enable RLS (Row Level Security)
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  role TEXT DEFAULT 'viewer' CHECK (role IN ('admin', 'editor', 'viewer')),
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create blog_categories table
CREATE TABLE blog_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT UNIQUE NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create blog_posts table
CREATE TABLE blog_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content JSONB NOT NULL,
  excerpt TEXT,
  featured_image TEXT,
  thumbnail_url TEXT, -- For Medium-style thumbnails
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  author_id UUID REFERENCES profiles(id),
  author_display TEXT DEFAULT 'real_name' CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team')),
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create blog_post_categories junction table
CREATE TABLE blog_post_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  category_id UUID REFERENCES blog_categories(id) ON DELETE CASCADE,
  UNIQUE(post_id, category_id)
);

-- Create storage bucket for blog images
INSERT INTO storage.buckets (id, name, public) VALUES ('blog-images', 'blog-images', true);

-- RLS Policies for profiles
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for blog_posts
CREATE POLICY "Published posts are viewable by everyone" ON blog_posts
  FOR SELECT USING (status = 'published' OR auth.uid() = author_id);

CREATE POLICY "Authenticated users can create posts" ON blog_posts
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authors can update their own posts" ON blog_posts
  FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own posts" ON blog_posts
  FOR DELETE USING (auth.uid() = author_id);

-- RLS Policies for blog_categories
CREATE POLICY "Categories are viewable by everyone" ON blog_categories
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage categories" ON blog_categories
  FOR ALL USING (auth.role() = 'authenticated');

-- RLS Policies for blog_post_categories
CREATE POLICY "Post categories are viewable by everyone" ON blog_post_categories
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage post categories" ON blog_post_categories
  FOR ALL USING (auth.role() = 'authenticated');

-- Storage policies for blog images
CREATE POLICY "Blog images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'blog-images');

CREATE POLICY "Authenticated users can upload blog images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'blog-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update their own blog images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'blog-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own blog images" ON storage.objects
  FOR DELETE USING (bucket_id = 'blog-images' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Function to automatically create profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 5. Configure Google OAuth (Optional)
1. Go to Supabase Dashboard > Authentication > Providers
2. Enable Google provider
3. Add your Google OAuth credentials
4. Set redirect URL to: `https://your-domain.com/admin/blog`

### 6. Create Your First Admin User
1. Go to your blog at `/blog`
2. Try to access `/admin/blog` (will be created in next phase)
3. Sign up with your email
4. Go to Supabase Dashboard > Authentication > Users
5. Find your user and note the UUID
6. Run this SQL to make yourself admin:
```sql
UPDATE profiles SET role = 'admin' WHERE id = 'your-user-uuid-here';
```

## 📝 Features Implemented

### ✅ Public Blog Features
- **Blog listing page** (`/blog`) - SEO optimized
- **Individual blog posts** (`/blog/:slug`) - Dynamic routing
- **Search functionality** - Filter posts by title/content
- **Responsive design** - Mobile-friendly layout
- **Social sharing** - Native share API with clipboard fallback

### ✅ Authentication System
- **Google Sign-in** - One-click authentication
- **Email/Password** - Traditional signup/signin
- **Role-based access** - Admin, Editor, Viewer roles
- **Protected routes** - Secure admin areas
- **Auto profile creation** - Seamless user onboarding

### 🚧 Next Phase: Admin Dashboard
- Rich text editor (Tiptap/Notion-style)
- Image upload and management
- Draft/publish workflow
- Category management
- Analytics dashboard

## 🔐 Access Control

### Role Hierarchy
- **Viewer**: Read-only access to dashboard
- **Editor**: Create and edit own posts
- **Admin**: Full access to all features

### Security Features
- Row Level Security (RLS) enabled
- JWT-based authentication
- Secure file uploads
- CORS protection

## 🎯 SEO Benefits

- **Dynamic meta tags** for each blog post
- **Structured URLs** (`/blog/post-slug`)
- **Fast loading** with optimized images
- **Mobile responsive** for better rankings
- **Social media previews** with Open Graph tags

## 📱 Usage

1. **Public Access**: Anyone can read published blog posts
2. **Team Access**: Sign in to access admin features
3. **Content Creation**: Use the rich editor to write posts
4. **Publishing**: Control when posts go live

The blog is now ready for content creation! 🚀
