-- Fix Blog Policies for User Role
-- This script updates the blog post policies to allow 'user' role instead of 'editor'

-- 1. Drop existing blog post policies
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can create posts" ON blog_posts;

-- 2. Create new policy allowing 'user' role to create posts
CREATE POLICY "Users can create posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('user', 'admin', 'super_admin', 'owner')
    )
  );

-- 3. Verify the policy is in place
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'blog_posts' AND policyname = 'Users can create posts';

SELECT 'Blog policies updated for user role!' as status;
