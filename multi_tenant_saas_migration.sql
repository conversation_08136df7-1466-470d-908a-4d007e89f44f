-- Multi-Tenant SaaS Platform Migration
-- This creates the workspace/organization structure with subscription plans

-- 0. First, ensure we have the profiles table with proper structure
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner')),
  subscription_plan TEXT DEFAULT 'free' CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for profiles
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles
  FOR ALL USING (auth.uid() = id);

-- 1. Create Organizations/Workspaces table
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  avatar_url TEXT,
  subscription_plan TEXT DEFAULT 'free' CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trialing')),
  workflow_credits_used INTEGER DEFAULT 0,
  workflow_credits_limit INTEGER DEFAULT 0,
  ai_credits_used INTEGER DEFAULT 0,
  ai_credits_limit INTEGER DEFAULT 0,
  credits_reset_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 month'),
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create Organization Members table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'editor' CHECK (role IN ('owner', 'admin', 'editor', 'viewer')),
  invited_by UUID REFERENCES profiles(id),
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  joined_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(organization_id, user_id)
);

-- 3. Create Workspace Invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'editor' CHECK (role IN ('admin', 'editor', 'viewer')),
  invited_by UUID NOT NULL REFERENCES profiles(id),
  token TEXT UNIQUE NOT NULL DEFAULT gen_random_uuid(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create Workflow Credits Usage Log
CREATE TABLE IF NOT EXISTS workflow_credit_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_id UUID, -- Can be null for manual actions
  action_type TEXT NOT NULL, -- email_send, webhook_call, sms_send, etc.
  credits_used INTEGER NOT NULL DEFAULT 1,
  execution_id TEXT, -- For tracking specific workflow runs
  metadata JSONB, -- Store additional context
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create Support Access Log (for troubleshooting)
CREATE TABLE IF NOT EXISTS support_access_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  support_user_id UUID NOT NULL REFERENCES profiles(id),
  target_organization_id UUID NOT NULL REFERENCES organizations(id),
  target_user_id UUID REFERENCES profiles(id),
  access_reason TEXT NOT NULL,
  access_granted_by UUID REFERENCES profiles(id), -- The user who granted access
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true
);

-- 6. Add organization_id to existing tables
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
ALTER TABLE workflows ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);
ALTER TABLE feature_requests ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_organization_id ON blog_posts(organization_id);
CREATE INDEX IF NOT EXISTS idx_workflows_organization_id ON workflows(organization_id);
CREATE INDEX IF NOT EXISTS idx_workflow_credit_usage_org_id ON workflow_credit_usage(organization_id);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_token ON workspace_invitations(token);

-- 8. Create default organization for existing users
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
VALUES ('Millennial Business Innovations', 'mbi', 'enterprise', 25000, -1)
ON CONFLICT (slug) DO NOTHING;

-- 9. Insert/Update Stephen as platform owner (super admin across all organizations)
INSERT INTO profiles (id, email, full_name, role, subscription_plan)
SELECT
  auth.uid(),
  '<EMAIL>',
  'Stephen Jan Lovino',
  'platform_owner',
  'enterprise'
WHERE EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
ON CONFLICT (email) DO UPDATE SET
  role = 'platform_owner',
  subscription_plan = 'enterprise';

-- 10. Add Stephen to MBI organization as owner (only if he exists)
INSERT INTO organization_members (organization_id, user_id, role, joined_at)
SELECT
  o.id,
  p.id,
  'owner',
  NOW()
FROM organizations o
CROSS JOIN profiles p
WHERE o.slug = 'mbi'
AND p.email = '<EMAIL>'
AND EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
ON CONFLICT (organization_id, user_id) DO UPDATE SET role = 'owner';

-- 11. Create personal organizations for existing users (except Stephen)
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
SELECT
  COALESCE(p.full_name, p.email) || '''s Workspace',
  'user-' || p.id,
  'free',
  100,
  50
FROM profiles p
WHERE p.email != '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM organization_members om WHERE om.user_id = p.id
)
ON CONFLICT (slug) DO NOTHING;

-- 12. Add users to their personal organizations
INSERT INTO organization_members (organization_id, user_id, role, joined_at)
SELECT 
  o.id,
  p.id,
  'owner',
  NOW()
FROM profiles p
JOIN organizations o ON o.slug = 'user-' || p.id
WHERE p.email != '<EMAIL>'
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- 13. Update existing blog posts to belong to user's personal organization
UPDATE blog_posts 
SET organization_id = (
  SELECT om.organization_id 
  FROM organization_members om 
  WHERE om.user_id = blog_posts.author_id 
  AND om.role = 'owner'
  LIMIT 1
)
WHERE organization_id IS NULL;

-- 14. Update existing workflows to belong to user's personal organization  
UPDATE workflows 
SET organization_id = (
  SELECT om.organization_id 
  FROM organization_members om 
  WHERE om.user_id = workflows.created_by 
  AND om.role = 'owner'
  LIMIT 1
)
WHERE organization_id IS NULL;

-- 15. Create RLS policies for multi-tenant access

-- Organizations: Users can only see organizations they're members of
CREATE POLICY "Users can view their organizations" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- Organization Members: Users can see members of their organizations
CREATE POLICY "Users can view organization members" ON organization_members
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- Blog Posts: Public for published, private for drafts within organization
CREATE POLICY "Blog posts access control" ON blog_posts
  FOR SELECT USING (
    -- Published posts are public
    status = 'published'
    OR
    -- Draft/pending posts only visible within organization
    (organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    ))
    OR
    -- Platform owner can see everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- Workflows: Only visible within organization
CREATE POLICY "Workflows organization access" ON workflows
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'platform_owner'
    )
  );

-- Enable RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_credit_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;

-- 16. Create functions for credit management
CREATE OR REPLACE FUNCTION check_workflow_credits(org_id UUID, credits_needed INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_plan TEXT;
  credits_limit INTEGER;
  credits_used INTEGER;
BEGIN
  SELECT subscription_plan, workflow_credits_limit, workflow_credits_used
  INTO current_plan, credits_limit, credits_used
  FROM organizations
  WHERE id = org_id;

  -- Free plan has limited workflow access (100 credits)
  -- All plans can use workflows, just with different limits

  -- Check if adding credits would exceed limit
  RETURN (credits_used + credits_needed) <= credits_limit;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION check_ai_credits(org_id UUID, credits_needed INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_plan TEXT;
  credits_limit INTEGER;
  credits_used INTEGER;
BEGIN
  SELECT subscription_plan, ai_credits_limit, ai_credits_used
  INTO current_plan, credits_limit, credits_used
  FROM organizations
  WHERE id = org_id;

  -- -1 means unlimited
  IF credits_limit = -1 THEN
    RETURN TRUE;
  END IF;

  -- Check if adding credits would exceed limit
  RETURN (credits_used + credits_needed) <= credits_limit;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION consume_workflow_credits(
  org_id UUID, 
  credits_to_consume INTEGER,
  action_type TEXT,
  workflow_id UUID DEFAULT NULL,
  execution_id TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  can_consume BOOLEAN;
BEGIN
  -- Check if organization has enough credits
  SELECT check_workflow_credits(org_id, credits_to_consume) INTO can_consume;
  
  IF NOT can_consume THEN
    RETURN FALSE;
  END IF;
  
  -- Consume the credits
  UPDATE organizations 
  SET workflow_credits_used = workflow_credits_used + credits_to_consume
  WHERE id = org_id;
  
  -- Log the usage
  INSERT INTO workflow_credit_usage (
    organization_id, 
    workflow_id, 
    action_type, 
    credits_used, 
    execution_id
  ) VALUES (
    org_id, 
    workflow_id, 
    action_type, 
    credits_to_consume, 
    execution_id
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION consume_ai_credits(
  org_id UUID,
  credits_to_consume INTEGER,
  action_type TEXT,
  metadata JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  can_consume BOOLEAN;
BEGIN
  -- Check if organization has enough credits
  SELECT check_ai_credits(org_id, credits_to_consume) INTO can_consume;

  IF NOT can_consume THEN
    RETURN FALSE;
  END IF;

  -- Consume the credits
  UPDATE organizations
  SET ai_credits_used = ai_credits_used + credits_to_consume
  WHERE id = org_id;

  -- Log the usage
  INSERT INTO workflow_credit_usage (
    organization_id,
    workflow_id,
    action_type,
    credits_used,
    metadata
  ) VALUES (
    org_id,
    NULL, -- AI credits don't have workflow_id
    action_type,
    credits_to_consume,
    metadata
  );

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 17. Show results
SELECT 'Migration completed successfully!' as status;

-- Show organizations created
SELECT 'Organizations:' as info;
SELECT name, slug, subscription_plan FROM organizations ORDER BY created_at;

-- Show organization memberships
SELECT 'Organization Memberships:' as info;
SELECT 
  o.name as organization,
  p.full_name as user_name,
  p.email,
  om.role
FROM organization_members om
JOIN organizations o ON o.id = om.organization_id
JOIN profiles p ON p.id = om.user_id
ORDER BY o.name, om.role;
