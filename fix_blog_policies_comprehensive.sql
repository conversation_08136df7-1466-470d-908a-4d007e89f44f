-- Comprehensive Blog Policies Fix
-- Run this in Supabase Dashboard > SQL Editor
-- This will forcefully remove ALL conflicting policies and recreate them properly

-- 1. First, update <PERSON>'s role to owner (ensure it's set correctly)
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- 2. Drop the role constraint and recreate without viewer
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('owner', 'super_admin', 'admin', 'editor'));

-- 3. FORCEFULLY DROP ALL BLOG POST POLICIES (comprehensive list)
DROP POLICY IF EXISTS "Users can create posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Anyone can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can view their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Authenticated users can create posts" ON blog_posts;
DROP POLICY IF EXISTS "Authors can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Authors can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Published posts are viewable by everyone" ON blog_posts;
DROP POLICY IF EXISTS "Users can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Admins can view all posts" ON blog_posts;
DROP POLICY IF EXISTS "Admins can manage all posts" ON blog_posts;

-- 4. Get all existing policies and drop them (in case we missed any)
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'blog_posts'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON blog_posts';
    END LOOP;
END $$;

-- 5. Create NEW comprehensive blog post policies with proper role hierarchy

-- Policy 1: Anyone can view published posts
CREATE POLICY "Anyone can view published posts" ON blog_posts
  FOR SELECT USING (status = 'published');

-- Policy 2: Users can view their own posts + Admins can view all posts
CREATE POLICY "Users can view their own posts" ON blog_posts
  FOR SELECT USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 3: Users with editor+ role can create posts (NO STATUS RESTRICTIONS)
CREATE POLICY "Users can create posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('editor', 'admin', 'super_admin', 'owner')
    )
  );

-- Policy 4: Users can update their own posts + Admins can update any post
CREATE POLICY "Users can update their own posts" ON blog_posts
  FOR UPDATE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 5: Users can delete their own posts + Admins can delete any post
CREATE POLICY "Users can delete their own posts" ON blog_posts
  FOR DELETE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- 6. Verify Stephen's profile
SELECT
    'Stephen Profile Check' as check_type,
    id,
    email,
    full_name,
    role,
    created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 7. Verify all profiles and their roles
SELECT
    'All Profiles' as check_type,
    email,
    full_name,
    role,
    created_at
FROM profiles
ORDER BY 
    CASE role 
        WHEN 'owner' THEN 1
        WHEN 'super_admin' THEN 2
        WHEN 'admin' THEN 3
        WHEN 'editor' THEN 4
    END,
    created_at;

-- 8. Verify blog post policies are correctly set
SELECT 
    'Blog Post Policies' as check_type,
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    cmd, 
    qual 
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 9. Test query to verify permissions (this should show if policies work)
SELECT 
    'Permission Test' as check_type,
    'If you can see this, SELECT policies work' as message;
