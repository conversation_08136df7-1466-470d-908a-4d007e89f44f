-- Analytics Performance Optimization
-- Run this in Supabase SQL Editor to dramatically improve analytics loading speed

-- 1. Add missing indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_created_at_post_id 
ON blog_post_views(created_at, post_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_post_created 
ON blog_post_views(post_id, created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_clicks_created_at_post_id 
ON blog_post_clicks(created_at, post_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_clicks_post_created 
ON blog_post_clicks(post_id, created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_reading_time_created_at_post_id 
ON blog_post_reading_time(created_at, post_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_reading_time_post_created 
ON blog_post_reading_time(post_id, created_at);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_posts_status_org_published 
ON blog_posts(status, organization_id, published_at) WHERE status = 'published';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_referrer_created 
ON blog_post_views(referrer, created_at) WHERE referrer IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_device_created 
ON blog_post_views(device_type, created_at) WHERE device_type IS NOT NULL;

-- 2. Create optimized analytics functions for faster queries
CREATE OR REPLACE FUNCTION get_analytics_overview(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    total_views BIGINT,
    total_unique_views BIGINT,
    total_clicks BIGINT,
    avg_reading_time INTEGER,
    avg_bounce_rate DECIMAL,
    total_posts BIGINT,
    published_posts BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH post_stats AS (
        SELECT 
            bp.id,
            bp.view_count,
            bp.unique_view_count,
            bp.click_count,
            bp.avg_reading_time,
            bp.bounce_rate
        FROM blog_posts bp
        WHERE bp.status = 'published'
        AND bp.published_at >= p_start_date
        AND bp.published_at <= p_end_date
        AND (p_organization_id IS NULL OR bp.organization_id = p_organization_id)
    )
    SELECT 
        COALESCE(SUM(ps.view_count), 0)::BIGINT,
        COALESCE(SUM(ps.unique_view_count), 0)::BIGINT,
        COALESCE(SUM(ps.click_count), 0)::BIGINT,
        COALESCE(AVG(ps.avg_reading_time), 0)::INTEGER,
        COALESCE(AVG(ps.bounce_rate), 0)::DECIMAL,
        COUNT(*)::BIGINT,
        COUNT(*)::BIGINT
    FROM post_stats ps;
END;
$$ LANGUAGE plpgsql;

-- 3. Create optimized chart data function
CREATE OR REPLACE FUNCTION get_analytics_chart_data(
    p_organization_id UUID DEFAULT NULL,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE(
    date_label TEXT,
    views BIGINT,
    unique_views BIGINT,
    clicks BIGINT,
    avg_reading_time INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH date_series AS (
        SELECT 
            generate_series(
                CURRENT_DATE - INTERVAL '1 day' * (p_days - 1),
                CURRENT_DATE,
                INTERVAL '1 day'
            )::DATE as date_val
    ),
    daily_stats AS (
        SELECT 
            ds.date_val,
            COALESCE(COUNT(bpv.id), 0) as day_views,
            COALESCE(COUNT(DISTINCT bpv.session_id), 0) as day_unique_views,
            COALESCE(COUNT(bpc.id), 0) as day_clicks,
            COALESCE(AVG(bprt.time_spent), 0) as day_avg_reading_time
        FROM date_series ds
        LEFT JOIN blog_post_views bpv ON DATE(bpv.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        LEFT JOIN blog_post_clicks bpc ON DATE(bpc.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bpc.organization_id = p_organization_id)
        LEFT JOIN blog_post_reading_time bprt ON DATE(bprt.created_at) = ds.date_val
            AND (p_organization_id IS NULL OR bprt.organization_id = p_organization_id)
        GROUP BY ds.date_val
        ORDER BY ds.date_val
    )
    SELECT 
        TO_CHAR(ds.date_val, 'Mon DD') as date_label,
        ds.day_views,
        ds.day_unique_views,
        ds.day_clicks,
        ds.day_avg_reading_time::INTEGER
    FROM daily_stats ds;
END;
$$ LANGUAGE plpgsql;

-- 4. Create optimized traffic sources function
CREATE OR REPLACE FUNCTION get_traffic_sources(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    referrer TEXT,
    views BIGINT,
    percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH referrer_stats AS (
        SELECT
            CASE
                WHEN bpv.referrer IS NULL OR bpv.referrer = '' THEN 'Direct'
                WHEN bpv.referrer LIKE '%google%' THEN 'Google'
                WHEN bpv.referrer LIKE '%facebook%' OR bpv.referrer LIKE '%fb.com%' OR bpv.referrer LIKE '%fb.me%' THEN 'Facebook'
                WHEN bpv.referrer LIKE '%twitter%' OR bpv.referrer LIKE '%t.co%' OR bpv.referrer LIKE '%x.com%' THEN 'Twitter/X'
                WHEN bpv.referrer LIKE '%linkedin%' OR bpv.referrer LIKE '%lnkd.in%' THEN 'LinkedIn'
                WHEN bpv.referrer LIKE '%youtube%' OR bpv.referrer LIKE '%youtu.be%' THEN 'YouTube'
                WHEN bpv.referrer LIKE '%instagram%' OR bpv.referrer LIKE '%instagr.am%' THEN 'Instagram'
                WHEN bpv.referrer LIKE '%reddit%' OR bpv.referrer LIKE '%redd.it%' THEN 'Reddit'
                WHEN bpv.referrer LIKE '%github%' THEN 'GitHub'
                WHEN bpv.referrer LIKE '%tiktok%' THEN 'TikTok'
                WHEN bpv.referrer LIKE '%pinterest%' OR bpv.referrer LIKE '%pin.it%' THEN 'Pinterest'
                WHEN bpv.referrer LIKE '%snapchat%' THEN 'Snapchat'
                WHEN bpv.referrer LIKE '%discord%' THEN 'Discord'
                WHEN bpv.referrer LIKE '%telegram%' OR bpv.referrer LIKE '%t.me%' THEN 'Telegram'
                WHEN bpv.referrer LIKE '%whatsapp%' OR bpv.referrer LIKE '%wa.me%' THEN 'WhatsApp'
                WHEN bpv.referrer LIKE '%bing%' THEN 'Bing'
                WHEN bpv.referrer LIKE '%yahoo%' THEN 'Yahoo'
                WHEN bpv.referrer LIKE '%duckduckgo%' THEN 'DuckDuckGo'
                WHEN bpv.referrer LIKE '%medium%' THEN 'Medium'
                WHEN bpv.referrer LIKE '%dev.to%' THEN 'Dev.to'
                WHEN bpv.referrer LIKE '%stackoverflow%' THEN 'Stack Overflow'
                WHEN bpv.referrer LIKE '%hackernews%' OR bpv.referrer LIKE '%news.ycombinator%' THEN 'Hacker News'
                ELSE regexp_replace(
                    regexp_replace(bpv.referrer, '^https?://', ''),
                    '/.*$', ''
                )
            END as clean_referrer,
            COUNT(*) as referrer_views
        FROM blog_post_views bpv
        WHERE bpv.created_at >= p_start_date
        AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        GROUP BY clean_referrer
    ),
    total_views AS (
        SELECT SUM(referrer_views) as total_count
        FROM referrer_stats
    )
    SELECT 
        rs.clean_referrer,
        rs.referrer_views,
        ROUND((rs.referrer_views::DECIMAL / tv.total_count * 100), 1)
    FROM referrer_stats rs, total_views tv
    ORDER BY rs.referrer_views DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 5. Create optimized device stats function
CREATE OR REPLACE FUNCTION get_device_stats(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days'
)
RETURNS TABLE(
    device_type TEXT,
    views BIGINT,
    percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH device_stats AS (
        SELECT 
            COALESCE(bpv.device_type, 'Unknown') as device,
            COUNT(*) as device_views
        FROM blog_post_views bpv
        WHERE bpv.created_at >= p_start_date
        AND (p_organization_id IS NULL OR bpv.organization_id = p_organization_id)
        GROUP BY device
    ),
    total_views AS (
        SELECT SUM(device_views) as total_count
        FROM device_stats
    )
    SELECT 
        ds.device,
        ds.device_views,
        ROUND((ds.device_views::DECIMAL / tv.total_count * 100), 1)
    FROM device_stats ds, total_views tv
    ORDER BY ds.device_views DESC;
END;
$$ LANGUAGE plpgsql;

-- 6. Create optimized top posts function
CREATE OR REPLACE FUNCTION get_top_posts(
    p_organization_id UUID DEFAULT NULL,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_sort_by TEXT DEFAULT 'view_count',
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    post_id UUID,
    title TEXT,
    slug TEXT,
    view_count INTEGER,
    unique_view_count INTEGER,
    click_count INTEGER,
    avg_reading_time INTEGER,
    bounce_rate DECIMAL,
    reaction_count INTEGER,
    comment_count INTEGER,
    share_count INTEGER,
    published_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    EXECUTE format('
        SELECT 
            bp.id,
            bp.title,
            bp.slug,
            bp.view_count,
            bp.unique_view_count,
            bp.click_count,
            bp.avg_reading_time,
            bp.bounce_rate,
            bp.reaction_count,
            bp.comment_count,
            bp.share_count,
            bp.published_at
        FROM blog_posts bp
        WHERE bp.status = ''published''
        AND bp.published_at >= $1
        AND ($2 IS NULL OR bp.organization_id = $2)
        ORDER BY bp.%I DESC NULLS LAST
        LIMIT $3
    ', p_sort_by)
    USING p_start_date, p_organization_id, p_limit;
END;
$$ LANGUAGE plpgsql;

-- 7. Create materialized view for faster analytics (optional - for high traffic sites)
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics_daily_summary AS
SELECT 
    DATE(bpv.created_at) as date_val,
    bpv.organization_id,
    COUNT(*) as total_views,
    COUNT(DISTINCT bpv.session_id) as unique_views,
    COUNT(DISTINCT bpv.post_id) as posts_viewed,
    COUNT(DISTINCT CASE WHEN bpv.device_type = 'mobile' THEN bpv.session_id END) as mobile_views,
    COUNT(DISTINCT CASE WHEN bpv.device_type = 'desktop' THEN bpv.session_id END) as desktop_views,
    COUNT(DISTINCT CASE WHEN bpv.device_type = 'tablet' THEN bpv.session_id END) as tablet_views
FROM blog_post_views bpv
WHERE bpv.created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(bpv.created_at), bpv.organization_id;

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_analytics_daily_summary_date_org 
ON analytics_daily_summary(date_val, organization_id);

-- Function to refresh materialized view (call this daily via cron or manually)
CREATE OR REPLACE FUNCTION refresh_analytics_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_daily_summary;
END;
$$ LANGUAGE plpgsql;

-- 8. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_analytics_overview TO authenticated;
GRANT EXECUTE ON FUNCTION get_analytics_chart_data TO authenticated;
GRANT EXECUTE ON FUNCTION get_traffic_sources TO authenticated;
GRANT EXECUTE ON FUNCTION get_device_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_posts TO authenticated;
GRANT SELECT ON analytics_daily_summary TO authenticated;

SELECT 'Analytics performance optimization complete! 🚀' as status;
