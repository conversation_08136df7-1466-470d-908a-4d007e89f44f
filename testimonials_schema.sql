-- Testimonials and Case Studies Database Schema
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Testimonials Table
CREATE TABLE IF NOT EXISTS testimonials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  author_name VARCHAR(100) NOT NULL,
  author_title VARCHAR(100),
  author_company VARCHAR(100),
  author_industry VARCHAR(50),
  quote TEXT NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5) DEFAULT 5,
  project_type VARCHAR(50), -- 'mvp', 'saas', 'web-app', 'consulting'
  project_value INTEGER, -- Project value in USD
  timeline_months INTEGER, -- Project duration in months
  results_achieved TEXT, -- e.g., "Raised $2.3M Series A"
  is_featured BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT FALSE,
  is_generated BOOLEAN DEFAULT FALSE, -- For placeholder content
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Case Studies Table
CREATE TABLE IF NOT EXISTS case_studies (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  title VARCHAR(200) NOT NULL,
  slug VARCHAR(200) UNIQUE NOT NULL,
  client_name VARCHAR(100),
  client_industry VARCHAR(50),
  project_type VARCHAR(50),
  challenge TEXT NOT NULL,
  solution TEXT NOT NULL,
  results TEXT NOT NULL,
  technologies JSONB, -- Array of technologies used
  timeline_months INTEGER,
  team_size INTEGER,
  project_value INTEGER, -- Project value in USD
  roi_percentage INTEGER, -- Return on investment
  featured_image TEXT, -- URL to featured image
  gallery_images JSONB, -- Array of image URLs
  testimonial_id UUID REFERENCES testimonials(id) ON DELETE SET NULL,
  is_featured BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT FALSE,
  is_generated BOOLEAN DEFAULT FALSE, -- For placeholder content
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- 3. Case Study Metrics (for detailed analytics)
CREATE TABLE IF NOT EXISTS case_study_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_study_id UUID NOT NULL REFERENCES case_studies(id) ON DELETE CASCADE,
  metric_name VARCHAR(100) NOT NULL, -- e.g., "User Growth", "Revenue Increase"
  metric_value VARCHAR(50) NOT NULL, -- e.g., "300%", "$2.3M", "50,000 users"
  metric_type VARCHAR(20) DEFAULT 'percentage', -- 'percentage', 'currency', 'number'
  time_period VARCHAR(50), -- e.g., "6 months", "1 year"
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_testimonials_approved ON testimonials(is_approved, is_featured);
CREATE INDEX IF NOT EXISTS idx_testimonials_author ON testimonials(author_id);
CREATE INDEX IF NOT EXISTS idx_case_studies_approved ON case_studies(is_approved, is_featured);
CREATE INDEX IF NOT EXISTS idx_case_studies_slug ON case_studies(slug);
CREATE INDEX IF NOT EXISTS idx_case_studies_author ON case_studies(author_id);

-- Enable RLS
ALTER TABLE testimonials ENABLE ROW LEVEL SECURITY;
ALTER TABLE case_studies ENABLE ROW LEVEL SECURITY;
ALTER TABLE case_study_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Testimonials
CREATE POLICY "Anyone can view approved testimonials" ON testimonials 
  FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create testimonials" ON testimonials 
  FOR INSERT WITH CHECK (auth.uid() = author_id OR auth.uid() IS NULL);
CREATE POLICY "Authors can update their testimonials" ON testimonials 
  FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can manage all testimonials" ON testimonials 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'super_admin', 'admin')));

-- RLS Policies for Case Studies
CREATE POLICY "Anyone can view approved case studies" ON case_studies 
  FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create case studies" ON case_studies 
  FOR INSERT WITH CHECK (auth.uid() = author_id OR auth.uid() IS NULL);
CREATE POLICY "Authors can update their case studies" ON case_studies 
  FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can manage all case studies" ON case_studies 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'super_admin', 'admin')));

-- RLS Policies for Case Study Metrics
CREATE POLICY "Anyone can view metrics for approved case studies" ON case_study_metrics 
  FOR SELECT USING (
    case_study_id IN (SELECT id FROM case_studies WHERE is_approved = true)
  );
CREATE POLICY "Admins can manage metrics" ON case_study_metrics 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'super_admin', 'admin')));

-- Insert placeholder testimonials (marked as generated)
INSERT INTO testimonials (
  author_name, author_title, author_company, author_industry, quote, 
  rating, project_type, project_value, timeline_months, results_achieved, 
  is_featured, is_approved, is_generated, display_order
) VALUES
(
  'Sarah Chen', 'Co-Founder & CEO', 'TechFlow Analytics', 'B2B SaaS',
  'MBI transformed our startup idea into a fully functional SaaS platform in just 4 months. Their team didn''t just build what we asked for—they anticipated our needs and built for scale. Six months post-launch, we''ve raised $2.3M in Series A funding. The platform they built was a huge factor in investor confidence.',
  5, 'saas', 75000, 4, 'Raised $2.3M Series A', true, true, true, 1
),
(
  'Marcus Rodriguez', 'Founder', 'LocalEats', 'Food Tech',
  'Working with MBI was a game-changer. They took our complex restaurant management idea and turned it into an intuitive platform that our clients actually love using. We went from concept to 500+ restaurant partners in 8 months.',
  5, 'web-app', 45000, 3, '500+ restaurant partners', false, true, true, 2
),
(
  'Jennifer Park', 'CTO', 'HealthSync', 'HealthTech',
  'The technical expertise at MBI is outstanding. They built our HIPAA-compliant patient portal with enterprise-grade security from day one. No shortcuts, no compromises—just solid, scalable architecture that grows with us.',
  5, 'saas', 120000, 6, 'HIPAA compliance achieved', false, true, true, 3
)
ON CONFLICT DO NOTHING;

-- Insert placeholder case studies (marked as generated)
INSERT INTO case_studies (
  title, slug, client_name, client_industry, project_type, challenge, solution, results,
  technologies, timeline_months, team_size, project_value, roi_percentage,
  is_featured, is_approved, is_generated, display_order, published_at
) VALUES
(
  'TechFlow Analytics: From Idea to $2.3M Series A',
  'techflow-analytics-series-a',
  'TechFlow Analytics', 'B2B SaaS', 'saas',
  'TechFlow had a revolutionary idea for business analytics but lacked the technical expertise to build a scalable SaaS platform. They needed to move fast to capture market opportunity while ensuring enterprise-grade security and performance.',
  'We built a comprehensive SaaS platform with real-time analytics, custom dashboards, and enterprise integrations. Our team implemented advanced data processing, multi-tenant architecture, and robust API infrastructure that could scale to millions of data points.',
  'Within 4 months of launch, TechFlow secured 50+ enterprise clients and raised $2.3M in Series A funding. The platform now processes over 10M data points daily and has achieved 99.9% uptime.',
  '["React", "Node.js", "PostgreSQL", "Redis", "AWS", "Docker", "Kubernetes"]',
  4, 3, 75000, 3000,
  true, true, true, 1, NOW()
),
(
  'LocalEats: Revolutionizing Restaurant Management',
  'localeats-restaurant-platform',
  'LocalEats', 'Food Tech', 'web-app',
  'LocalEats wanted to create a comprehensive restaurant management platform but struggled with complex integrations between POS systems, inventory management, and customer ordering. They needed a solution that worked for both small cafes and large restaurant chains.',
  'We developed a modular platform with seamless POS integrations, real-time inventory tracking, and a customer-facing ordering system. The solution included mobile apps for staff, web dashboards for managers, and APIs for third-party integrations.',
  'LocalEats now serves 500+ restaurants across 12 cities, processing $50M+ in annual transactions. Restaurant partners report 30% increase in efficiency and 25% boost in customer satisfaction.',
  '["Vue.js", "Python", "Django", "PostgreSQL", "Stripe", "Twilio", "AWS"]',
  3, 4, 45000, 1100,
  false, true, true, 2, NOW()
)
ON CONFLICT (slug) DO NOTHING;

-- Insert metrics for case studies
INSERT INTO case_study_metrics (case_study_id, metric_name, metric_value, metric_type, time_period) 
SELECT 
  cs.id,
  metric.name,
  metric.value,
  metric.type,
  metric.period
FROM case_studies cs
CROSS JOIN (
  VALUES 
    ('User Growth', '500%', 'percentage', '6 months'),
    ('Revenue Increase', '$2.3M', 'currency', 'Series A'),
    ('Uptime Achievement', '99.9%', 'percentage', 'ongoing'),
    ('Data Processing', '10M+', 'number', 'daily')
) AS metric(name, value, type, period)
WHERE cs.slug = 'techflow-analytics-series-a'
ON CONFLICT DO NOTHING;

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_testimonials_updated_at BEFORE UPDATE ON testimonials 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_case_studies_updated_at BEFORE UPDATE ON case_studies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
