-- Fix Workflow Template Visibility Issue
-- Run this in Supabase Dashboard > SQL Editor
-- This fixes the issue where workflow templates created by saas_owner are not visible to regular users

-- 1. Check current state
SELECT 'Current Workflow Template Status' as check_type;
SELECT 
    COUNT(*) as total_workflows,
    COUNT(*) FILTER (WHERE is_template = true) as template_count,
    COUNT(*) FILTER (WHERE is_template = false) as regular_workflow_count,
    COUNT(*) FILTER (WHERE is_template = true AND created_by = (
        SELECT id FROM profiles WHERE role = 'saas_owner' LIMIT 1
    )) as saas_owner_templates
FROM workflows;

-- 2. Show current workflow RLS policies
SELECT 'Current Workflow RLS Policies' as check_type;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'workflows'
ORDER BY policyname;

-- 3. Drop existing admin policy that doesn't include saas_owner
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all workflows" ON workflows;

-- 4. <PERSON>reate updated admin policy that includes saas_owner
CREATE POLICY "<PERSON><PERSON> can manage all workflows" ON workflows
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin', 'saas_owner')
    )
  );

-- 5. Add new policy: All authenticated users can view workflow templates
-- This is the key fix - allows all users to see templates regardless of who created them
CREATE POLICY "All users can view workflow templates" ON workflows
  FOR SELECT USING (
    is_template = true AND auth.uid() IS NOT NULL
  );

-- 6. Verify the fix
SELECT 'Updated Workflow Template Status' as check_type;
SELECT 
    COUNT(*) as total_workflows,
    COUNT(*) FILTER (WHERE is_template = true) as template_count,
    COUNT(*) FILTER (WHERE is_template = false) as regular_workflow_count
FROM workflows;

-- 7. Show updated workflow policies
SELECT 'Updated Workflow RLS Policies' as check_type;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename = 'workflows'
ORDER BY policyname;

-- 8. Test query to simulate what a regular user would see
-- This simulates the query that WorkflowService.getWorkflowTemplates() runs
SELECT 'Templates Visible to Regular Users' as check_type;
SELECT 
    id,
    name,
    description,
    created_by,
    created_at,
    is_template
FROM workflows 
WHERE is_template = true
ORDER BY created_at DESC;

SELECT 'Workflow template visibility issue fixed successfully!' as status;
