-- Fix Organization RLS Policies - Remove Infinite Recursion
-- This script cleans up all organization policies and creates simple, non-conflicting ones

-- 1. First, let's see what policies currently exist
SELECT
  schemaname,
  tablename,
  policyname,
  cmd,
  qual
FROM pg_policies
WHERE tablename IN ('organizations', 'organization_members')
ORDER BY tablename, policyname;

-- 2. Drop ALL existing policies to start fresh
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    -- Drop all organization policies
    FOR policy_record IN
        SELECT policyname FROM pg_policies
        WHERE tablename = 'organizations'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON organizations';
    END LOOP;

    -- Drop all organization_members policies
    FOR policy_record IN
        SELECT policyname FROM pg_policies
        WHERE tablename = 'organization_members'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON organization_members';
    END LOOP;
END $$;

-- 3. Create simple, non-recursive policies for organizations
CREATE POLICY "organizations_select" ON organizations
  FOR SELECT USING (
    -- Users can see organizations they own (direct check, no joins)
    slug = 'user-' || auth.uid()::text
    OR
    -- Platform owners can see all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'platform_owner'
    )
  );

CREATE POLICY "organizations_insert" ON organizations
  FOR INSERT WITH CHECK (
    -- Users can only create organizations with their own user ID in slug
    slug = 'user-' || auth.uid()::text
  );

CREATE POLICY "organizations_update" ON organizations
  FOR UPDATE USING (
    -- Users can update their own organizations
    slug = 'user-' || auth.uid()::text
    OR
    -- Platform owners can update all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'platform_owner'
    )
  );

-- 4. Create simple, non-recursive policies for organization_members
CREATE POLICY "organization_members_select" ON organization_members
  FOR SELECT USING (
    -- Users can see their own memberships
    user_id = auth.uid()
    OR
    -- Platform owners can see all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'platform_owner'
    )
  );

CREATE POLICY "organization_members_insert" ON organization_members
  FOR INSERT WITH CHECK (
    -- Users can only add themselves as members
    user_id = auth.uid()
  );

CREATE POLICY "organization_members_update" ON organization_members
  FOR UPDATE USING (
    -- Users can update their own memberships
    user_id = auth.uid()
    OR
    -- Platform owners can update all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'platform_owner'
    )
  );

-- 5. Verify the new policies
SELECT
  schemaname,
  tablename,
  policyname,
  cmd,
  qual
FROM pg_policies
WHERE tablename IN ('organizations', 'organization_members')
ORDER BY tablename, policyname;
