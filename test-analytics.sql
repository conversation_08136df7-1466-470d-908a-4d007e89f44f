-- Test Analytics Data Collection
-- Run this in Supabase Dashboard > SQL Editor to verify analytics are working

-- 1. Check if analytics tables exist
SELECT 
    'Analytics Tables Status' as check_type,
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('blog_post_views', 'blog_post_clicks', 'blog_post_reading_time', 'blog_post_reactions')
ORDER BY table_name;

-- 2. Check if blog_posts has analytics columns
SELECT 
    'Blog Posts Analytics Columns' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
AND column_name IN ('view_count', 'unique_view_count', 'click_count', 'avg_reading_time', 'bounce_rate', 'reaction_count', 'comment_count', 'share_count')
ORDER BY column_name;

-- 3. Check recent analytics data (last 24 hours)
SELECT 
    'Recent Views (24h)' as data_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT post_id) as unique_posts,
    COUNT(DISTINCT session_id) as unique_sessions
FROM blog_post_views 
WHERE created_at >= NOW() - INTERVAL '24 hours';

SELECT 
    'Recent Clicks (24h)' as data_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT post_id) as unique_posts,
    COUNT(DISTINCT click_type) as unique_click_types
FROM blog_post_clicks 
WHERE created_at >= NOW() - INTERVAL '24 hours';

SELECT 
    'Recent Reading Time (24h)' as data_type,
    COUNT(*) as total_records,
    AVG(time_spent) as avg_time_seconds,
    AVG(scroll_percentage) as avg_scroll_percent
FROM blog_post_reading_time 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- 4. Check if triggers are working (analytics columns should be updated)
SELECT 
    'Blog Posts with Analytics' as check_type,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE view_count > 0) as posts_with_views,
    COUNT(*) FILTER (WHERE click_count > 0) as posts_with_clicks,
    SUM(view_count) as total_views,
    SUM(click_count) as total_clicks
FROM blog_posts 
WHERE status = 'published';

-- 5. Sample of recent analytics data
SELECT 
    'Sample Recent Views' as data_type,
    bpv.created_at,
    bp.title,
    bpv.device_type,
    bpv.browser,
    bpv.referrer
FROM blog_post_views bpv
JOIN blog_posts bp ON bp.id = bpv.post_id
ORDER BY bpv.created_at DESC
LIMIT 5;

-- 6. Check RLS policies
SELECT 
    'RLS Policies' as check_type,
    tablename,
    policyname,
    cmd,
    qual
FROM pg_policies 
WHERE tablename IN ('blog_post_views', 'blog_post_clicks', 'blog_post_reading_time', 'blog_post_reactions')
ORDER BY tablename, policyname;

-- 7. Test data insertion (simulate a view)
-- This will help verify if the tracking is working
INSERT INTO blog_post_views (
    post_id,
    session_id,
    device_type,
    browser,
    referrer,
    user_agent
) 
SELECT 
    id,
    'test_session_' || EXTRACT(EPOCH FROM NOW()),
    'desktop',
    'test_browser',
    'test_referrer',
    'Test User Agent'
FROM blog_posts 
WHERE status = 'published' 
LIMIT 1
RETURNING 'Test View Inserted' as result, post_id, session_id;

-- 8. Verify the test view was recorded and triggered analytics update
SELECT 
    'After Test Insert' as check_type,
    bp.title,
    bp.view_count,
    bp.unique_view_count,
    (SELECT COUNT(*) FROM blog_post_views WHERE post_id = bp.id) as actual_view_count
FROM blog_posts bp
WHERE status = 'published'
ORDER BY bp.updated_at DESC
LIMIT 1;
