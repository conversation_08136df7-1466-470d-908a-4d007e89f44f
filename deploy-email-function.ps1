# PowerShell script to deploy the email function to Supabase

Write-Host "🚀 Deploying Email Function to Supabase..." -ForegroundColor Green

# Check if Supabase CLI is installed
try {
    $supabaseVersion = supabase --version
    Write-Host "✅ Supabase CLI found: $supabaseVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Supabase CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "npm install -g supabase" -ForegroundColor Yellow
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "supabase/functions/send-email/index.ts")) {
    Write-Host "❌ Email function not found. Make sure you're in the project root directory." -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found email function at supabase/functions/send-email/index.ts" -ForegroundColor Green

# Deploy the function
Write-Host "🔄 Deploying function..." -ForegroundColor Yellow
try {
    supabase functions deploy send-email
    Write-Host "✅ Email function deployed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to deploy function. Error: $_" -ForegroundColor Red
    Write-Host "💡 Make sure you're logged in: supabase login" -ForegroundColor Yellow
    Write-Host "💡 Make sure your project is linked: supabase link --project-ref YOUR_PROJECT_REF" -ForegroundColor Yellow
    exit 1
}

# List functions to verify
Write-Host "📋 Listing deployed functions..." -ForegroundColor Yellow
supabase functions list

Write-Host ""
Write-Host "🎉 Deployment complete! Your email integrations should now work with:" -ForegroundColor Green
Write-Host "   ✅ Gmail (with app password)" -ForegroundColor White
Write-Host "   ✅ Outlook (with password)" -ForegroundColor White  
Write-Host "   ✅ Custom SMTP servers" -ForegroundColor White
Write-Host ""
Write-Host "💡 Test your email integrations in the admin panel!" -ForegroundColor Cyan
