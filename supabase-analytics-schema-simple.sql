-- Simple Analytics Schema (without organization dependencies)
-- Run this if you get errors about missing organizations table

-- 1. Add analytics columns to blog_posts table
DO $$ 
BEGIN
    -- Add analytics columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN view_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'unique_view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN unique_view_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'click_count') THEN
        ALTER TABLE blog_posts ADD COLUMN click_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'avg_reading_time') THEN
        ALTER TABLE blog_posts ADD COLUMN avg_reading_time INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'bounce_rate') THEN
        ALTER TABLE blog_posts ADD COLUMN bounce_rate DECIMAL(5,2) DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'reaction_count') THEN
        ALTER TABLE blog_posts ADD COLUMN reaction_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'comment_count') THEN
        ALTER TABLE blog_posts ADD COLUMN comment_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'share_count') THEN
        ALTER TABLE blog_posts ADD COLUMN share_count INTEGER DEFAULT 0;
    END IF;
END $$;

-- 2. Blog Post Views Tracking (simplified)
CREATE TABLE IF NOT EXISTS blog_post_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  device_type TEXT CHECK (device_type IN ('desktop', 'mobile', 'tablet')),
  browser TEXT,
  os TEXT,
  country TEXT,
  city TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Blog Post Clicks Tracking
CREATE TABLE IF NOT EXISTS blog_post_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  click_type TEXT DEFAULT 'link',
  target_url TEXT,
  element_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Blog Post Reading Time Tracking
CREATE TABLE IF NOT EXISTS blog_post_reading_time (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  time_spent INTEGER NOT NULL, -- in seconds
  scroll_percentage INTEGER DEFAULT 0,
  is_bounce BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, session_id)
);

-- 5. Blog Post Reactions
CREATE TABLE IF NOT EXISTS blog_post_reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'love', 'share', 'bookmark')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id, reaction_type)
);

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_post_views_post_id ON blog_post_views(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_created_at ON blog_post_views(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_session_id ON blog_post_views(session_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_post_id ON blog_post_clicks(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_created_at ON blog_post_clicks(created_at);

CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_post_id ON blog_post_reading_time(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_session_id ON blog_post_reading_time(session_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_post_id ON blog_post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_user_id ON blog_post_reactions(user_id);

-- 7. Functions to update blog_posts analytics columns
CREATE OR REPLACE FUNCTION update_blog_post_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update view count and unique view count
  UPDATE blog_posts 
  SET 
    view_count = (SELECT COUNT(*) FROM blog_post_views WHERE post_id = NEW.post_id),
    unique_view_count = (SELECT COUNT(DISTINCT session_id) FROM blog_post_views WHERE post_id = NEW.post_id)
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_clicks()
RETURNS TRIGGER AS $$
BEGIN
  -- Update click count
  UPDATE blog_posts 
  SET click_count = (SELECT COUNT(*) FROM blog_post_clicks WHERE post_id = NEW.post_id)
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_reading_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update average reading time and bounce rate
  UPDATE blog_posts 
  SET 
    avg_reading_time = (
      SELECT COALESCE(AVG(time_spent), 0)::INTEGER 
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    ),
    bounce_rate = (
      SELECT COALESCE(
        (COUNT(*) FILTER (WHERE is_bounce = true)::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 
        0
      )
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    )
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_reactions()
RETURNS TRIGGER AS $$
BEGIN
  -- Update reaction count
  UPDATE blog_posts 
  SET reaction_count = (SELECT COUNT(*) FROM blog_post_reactions WHERE post_id = COALESCE(NEW.post_id, OLD.post_id))
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 8. Create triggers
DROP TRIGGER IF EXISTS trigger_update_blog_post_analytics ON blog_post_views;
CREATE TRIGGER trigger_update_blog_post_analytics
  AFTER INSERT ON blog_post_views
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_analytics();

DROP TRIGGER IF EXISTS trigger_update_blog_post_clicks ON blog_post_clicks;
CREATE TRIGGER trigger_update_blog_post_clicks
  AFTER INSERT ON blog_post_clicks
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_clicks();

DROP TRIGGER IF EXISTS trigger_update_blog_post_reading_stats ON blog_post_reading_time;
CREATE TRIGGER trigger_update_blog_post_reading_stats
  AFTER INSERT OR UPDATE ON blog_post_reading_time
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_reading_stats();

DROP TRIGGER IF EXISTS trigger_update_blog_post_reactions ON blog_post_reactions;
CREATE TRIGGER trigger_update_blog_post_reactions
  AFTER INSERT OR DELETE ON blog_post_reactions
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_reactions();

-- 9. Enable RLS (Row Level Security)
ALTER TABLE blog_post_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reading_time ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reactions ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies (basic - you may want to customize these)
CREATE POLICY "Users can view all analytics" ON blog_post_views FOR SELECT USING (true);
CREATE POLICY "Users can insert their own views" ON blog_post_views FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view all clicks" ON blog_post_clicks FOR SELECT USING (true);
CREATE POLICY "Users can insert their own clicks" ON blog_post_clicks FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view all reading time" ON blog_post_reading_time FOR SELECT USING (true);
CREATE POLICY "Users can manage their own reading time" ON blog_post_reading_time FOR ALL WITH CHECK (true);

CREATE POLICY "Users can view all reactions" ON blog_post_reactions FOR SELECT USING (true);
CREATE POLICY "Users can manage their own reactions" ON blog_post_reactions FOR ALL USING (auth.uid() = user_id);
