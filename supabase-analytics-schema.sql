-- Analytics Database Schema for Live Blog Tracking
-- Run this SQL in your Supabase SQL Editor

-- First, let's check and add missing columns to blog_posts table
DO $$
BEGIN
    -- Add organization_id if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'organization_id') THEN
        ALTER TABLE blog_posts ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
    END IF;

    -- Add analytics columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN view_count INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'unique_view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN unique_view_count INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'click_count') THEN
        ALTER TABLE blog_posts ADD COLUMN click_count INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'avg_reading_time') THEN
        ALTER TABLE blog_posts ADD COLUMN avg_reading_time INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'bounce_rate') THEN
        ALTER TABLE blog_posts ADD COLUMN bounce_rate DECIMAL(5,2) DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'reaction_count') THEN
        ALTER TABLE blog_posts ADD COLUMN reaction_count INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'comment_count') THEN
        ALTER TABLE blog_posts ADD COLUMN comment_count INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'blog_posts' AND column_name = 'share_count') THEN
        ALTER TABLE blog_posts ADD COLUMN share_count INTEGER DEFAULT 0;
    END IF;
END $$;

-- 1. Blog Post Views Tracking
CREATE TABLE IF NOT EXISTS blog_post_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL, -- For tracking unique sessions
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  device_type TEXT CHECK (device_type IN ('desktop', 'mobile', 'tablet')),
  browser TEXT,
  os TEXT,
  country TEXT,
  city TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Blog Post Clicks Tracking
CREATE TABLE IF NOT EXISTS blog_post_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  click_type TEXT DEFAULT 'link', -- 'link', 'cta', 'share', etc.
  target_url TEXT,
  element_id TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Reading Time Tracking
CREATE TABLE IF NOT EXISTS blog_post_reading_time (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  time_spent INTEGER NOT NULL, -- in seconds
  scroll_percentage INTEGER DEFAULT 0, -- how far they scrolled
  is_bounce BOOLEAN DEFAULT FALSE, -- if they left quickly
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Blog Post Reactions (likes, shares, etc.)
CREATE TABLE IF NOT EXISTS blog_post_reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'love', 'share', 'bookmark')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id, reaction_type) -- Prevent duplicate reactions
);

-- 5. Update blog_posts table to include analytics columns
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS unique_view_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS click_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS avg_reading_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS bounce_rate DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS reaction_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS share_count INTEGER DEFAULT 0;

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_post_views_post_id ON blog_post_views(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_created_at ON blog_post_views(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_session_id ON blog_post_views(session_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_org_id ON blog_post_views(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_post_id ON blog_post_clicks(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_created_at ON blog_post_clicks(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_org_id ON blog_post_clicks(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_post_id ON blog_post_reading_time(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_created_at ON blog_post_reading_time(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_org_id ON blog_post_reading_time(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_post_id ON blog_post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_user_id ON blog_post_reactions(user_id);

-- 7. Create functions to update blog_posts analytics columns
CREATE OR REPLACE FUNCTION update_blog_post_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update view count and unique view count
  UPDATE blog_posts 
  SET 
    view_count = (
      SELECT COUNT(*) 
      FROM blog_post_views 
      WHERE post_id = NEW.post_id
    ),
    unique_view_count = (
      SELECT COUNT(DISTINCT session_id) 
      FROM blog_post_views 
      WHERE post_id = NEW.post_id
    ),
    click_count = (
      SELECT COUNT(*) 
      FROM blog_post_clicks 
      WHERE post_id = NEW.post_id
    ),
    avg_reading_time = (
      SELECT COALESCE(AVG(time_spent), 0)::INTEGER 
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    ),
    bounce_rate = (
      SELECT COALESCE(
        (COUNT(*) FILTER (WHERE is_bounce = true) * 100.0 / NULLIF(COUNT(*), 0)), 
        0
      )
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    ),
    reaction_count = (
      SELECT COUNT(*) 
      FROM blog_post_reactions 
      WHERE post_id = NEW.post_id
    )
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Create triggers to automatically update analytics
CREATE TRIGGER trigger_update_analytics_on_view
  AFTER INSERT ON blog_post_views
  FOR EACH ROW
  EXECUTE FUNCTION update_blog_post_analytics();

CREATE TRIGGER trigger_update_analytics_on_click
  AFTER INSERT ON blog_post_clicks
  FOR EACH ROW
  EXECUTE FUNCTION update_blog_post_analytics();

CREATE TRIGGER trigger_update_analytics_on_reading_time
  AFTER INSERT OR UPDATE ON blog_post_reading_time
  FOR EACH ROW
  EXECUTE FUNCTION update_blog_post_analytics();

CREATE TRIGGER trigger_update_analytics_on_reaction
  AFTER INSERT OR DELETE ON blog_post_reactions
  FOR EACH ROW
  EXECUTE FUNCTION update_blog_post_analytics();

-- 9. Row Level Security (RLS) Policies
ALTER TABLE blog_post_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reading_time ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reactions ENABLE ROW LEVEL SECURITY;

-- Allow public read access for tracking (anonymous users can track)
CREATE POLICY "Allow public tracking" ON blog_post_views
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Allow public click tracking" ON blog_post_clicks
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Allow public reading time tracking" ON blog_post_reading_time
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated reactions" ON blog_post_reactions
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Allow organization members to view their analytics
CREATE POLICY "Organization members can view analytics" ON blog_post_views
  FOR SELECT TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can view click analytics" ON blog_post_clicks
  FOR SELECT TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can view reading analytics" ON blog_post_reading_time
  FOR SELECT TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- 10. Create a view for easy analytics querying
CREATE OR REPLACE VIEW blog_analytics_summary AS
SELECT 
  bp.id,
  bp.title,
  bp.slug,
  bp.organization_id,
  bp.view_count,
  bp.unique_view_count,
  bp.click_count,
  bp.avg_reading_time,
  bp.bounce_rate,
  bp.reaction_count,
  bp.share_count,
  bp.published_at,
  bp.created_at,
  -- Additional calculated metrics
  CASE 
    WHEN bp.view_count > 0 
    THEN ROUND((bp.click_count::DECIMAL / bp.view_count) * 100, 2)
    ELSE 0 
  END as click_through_rate,
  
  -- Recent performance (last 7 days)
  (
    SELECT COUNT(*) 
    FROM blog_post_views bpv 
    WHERE bpv.post_id = bp.id 
    AND bpv.created_at >= NOW() - INTERVAL '7 days'
  ) as views_last_7_days,
  
  -- Top referrer
  (
    SELECT referrer 
    FROM blog_post_views bpv 
    WHERE bpv.post_id = bp.id 
    AND bpv.referrer IS NOT NULL
    GROUP BY referrer 
    ORDER BY COUNT(*) DESC 
    LIMIT 1
  ) as top_referrer

FROM blog_posts bp
WHERE bp.status = 'published';
