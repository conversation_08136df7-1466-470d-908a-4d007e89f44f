-- Create AI interactions table for tracking usage and analytics
-- Run this after the blog posts table fix

-- Create ai_interactions table
CREATE TABLE IF NOT EXISTS ai_interactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    message_type VARCHAR(50) NOT NULL DEFAULT 'chat_help',
    input_tokens INTEGER DEFAULT 0,
    output_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    model_used VARCHAR(100) DEFAULT 'deepseek-r1',
    credits_used INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON ai_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_organization_id ON ai_interactions(organization_id);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_created_at ON ai_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_message_type ON ai_interactions(message_type);

-- Add RLS (Row Level Security) policies
ALTER TABLE ai_interactions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own interactions
CREATE POLICY "Users can view own ai_interactions" ON ai_interactions
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own interactions
CREATE POLICY "Users can insert own ai_interactions" ON ai_interactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Admins can view all interactions in their organization
CREATE POLICY "Admins can view organization ai_interactions" ON ai_interactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            JOIN organization_members om ON om.user_id = p.id
            WHERE p.id = auth.uid()
            AND p.role IN ('platform_owner', 'super_admin', 'admin', 'owner')
            AND om.organization_id = ai_interactions.organization_id
            AND om.is_active = true
        )
    );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_ai_interactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_interactions_updated_at
    BEFORE UPDATE ON ai_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_interactions_updated_at();

-- Add comment for documentation
COMMENT ON TABLE ai_interactions IS 'Tracks AI assistant interactions for usage analytics and credit management';
COMMENT ON COLUMN ai_interactions.message_type IS 'Type of AI interaction: chat_help, blog_generation, workflow_help, etc.';
COMMENT ON COLUMN ai_interactions.input_tokens IS 'Number of tokens in the user input';
COMMENT ON COLUMN ai_interactions.output_tokens IS 'Number of tokens in the AI response';
COMMENT ON COLUMN ai_interactions.total_tokens IS 'Total tokens used (input + output)';
COMMENT ON COLUMN ai_interactions.model_used IS 'AI model used for the interaction';
COMMENT ON COLUMN ai_interactions.credits_used IS 'Number of AI credits consumed for this interaction';

-- Insert some sample data for testing (optional)
-- INSERT INTO ai_interactions (user_id, organization_id, message_type, total_tokens, credits_used)
-- SELECT
--     p.id as user_id,
--     om.organization_id,
--     'chat_help' as message_type,
--     100 as total_tokens,
--     1 as credits_used
-- FROM profiles p
-- JOIN organization_members om ON om.user_id = p.id AND om.is_active = true
-- WHERE p.role = 'platform_owner'
-- LIMIT 1;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'AI interactions table created successfully!';
END $$;
