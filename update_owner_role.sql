-- Update <PERSON> to Owner role
-- Run this in Supabase Dashboard > SQL Editor
-- Safe to run multiple times

-- First, drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Add the new constraint with owner role
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('owner', 'super_admin', 'admin', 'editor', 'viewer'));

-- Update <PERSON>'s role to owner
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- Verify the update
SELECT
    id,
    email,
    full_name,
    role,
    created_at
FROM profiles
WHERE email = '<EMAIL>';

-- Show all users and their roles
SELECT 
    email,
    full_name,
    role,
    created_at
FROM profiles 
ORDER BY 
    CASE role 
        WHEN 'owner' THEN 1
        WHEN 'super_admin' THEN 2
        WHEN 'admin' THEN 3
        WHEN 'editor' THEN 4
        WHEN 'viewer' THEN 5
    END,
    created_at;
