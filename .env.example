# Supabase Configuration
# NEVER commit real credentials to version control!
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# Turnstile Captcha (Cloudflare)
VITE_TURNSTILE_SITE_KEY=your_turnstile_site_key_here

# OpenRouter AI Configuration
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Google AI Configuration (for Gemini)
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Stripe Configuration
# Get your keys from: https://dashboard.stripe.com/apikeys
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Example:
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your_anon_key_here
# VITE_TURNSTILE_SITE_KEY=0x4AAAAAAABkMYinukE_fake_key
# OPENROUTER_API_KEY=sk-or-v1-your-key-here
# GOOGLE_AI_API_KEY=your-google-ai-key-here
# VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51ABC123...
# STRIPE_SECRET_KEY=sk_test_51ABC123...
# STRIPE_WEBHOOK_SECRET=whsec_ABC123...
