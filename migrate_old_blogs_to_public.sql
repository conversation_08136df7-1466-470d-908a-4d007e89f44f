-- Migrate all existing old blogs to public MBI blog
-- This will make all your existing content visible again

-- 1. First, let's see what we're working with
SELECT 
    'Before Migration - Current State' as info,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_posts,
    COUNT(*) FILTER (WHERE organization_id IS NOT NULL) as with_org
FROM blog_posts;

-- 2. Move ALL existing blog posts to public MBI blog
-- This treats all old content as "legacy public content"
UPDATE blog_posts 
SET 
    submission_status = 'published_mbi',
    -- Keep organization_id for reference but mark as MBI published
    updated_at = NOW()
WHERE 
    -- Only update posts that aren't already MBI published
    submission_status != 'published_mbi' OR submission_status IS NULL;

-- 3. Ensure all posts have proper organization assignment for reference
UPDATE blog_posts 
SET organization_id = (
    SELECT om.organization_id 
    FROM organization_members om 
    WHERE om.user_id = blog_posts.author_id 
    AND om.is_active = true 
    LIMIT 1
)
WHERE organization_id IS NULL;

-- 4. Handle any remaining orphaned posts
DO $$
DECLARE
  default_org_id UUID;
  orphaned_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO orphaned_count FROM blog_posts WHERE organization_id IS NULL;
  
  IF orphaned_count > 0 THEN
    SELECT id INTO default_org_id FROM organizations ORDER BY created_at ASC LIMIT 1;
    
    IF default_org_id IS NULL THEN
      INSERT INTO organizations (name, slug, description, subscription_plan)
      VALUES ('Legacy Content', 'legacy-content', 'Organization for legacy blog content', 'free')
      RETURNING id INTO default_org_id;
      
      RAISE NOTICE 'Created legacy organization for % orphaned posts', orphaned_count;
    END IF;
    
    UPDATE blog_posts 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
  END IF;
END $$;

-- 5. Clean up policies and create new simplified ones
DROP POLICY IF EXISTS "temp_view_all_posts" ON blog_posts;
DROP POLICY IF EXISTS "public_view_mbi_posts" ON blog_posts;
DROP POLICY IF EXISTS "org_members_view_private" ON blog_posts;
DROP POLICY IF EXISTS "super_admin_view_all" ON blog_posts;
DROP POLICY IF EXISTS "org_members_create_posts" ON blog_posts;
DROP POLICY IF EXISTS "update_posts_policy" ON blog_posts;
DROP POLICY IF EXISTS "delete_posts_policy" ON blog_posts;

-- 6. Create new simplified policies

-- Everyone can view MBI published posts (public blog)
CREATE POLICY "public_can_view_mbi_blog" ON blog_posts
  FOR SELECT USING (
    submission_status = 'published_mbi' AND status = 'published'
  );

-- Organization members can view their private workspace posts
CREATE POLICY "members_view_workspace_posts" ON blog_posts
  FOR SELECT USING (
    submission_status = 'private' AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Super admins can view everything
CREATE POLICY "super_admins_view_all" ON blog_posts
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- Users can create posts (will be private by default, can submit to MBI)
CREATE POLICY "users_create_posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Users can update their own posts, admins can update org posts
CREATE POLICY "users_update_posts" ON blog_posts
  FOR UPDATE USING (
    (auth.uid() = author_id) OR
    (organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true AND role IN ('admin', 'owner')
    )) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- Users can delete their own posts, admins can delete org posts
CREATE POLICY "users_delete_posts" ON blog_posts
  FOR DELETE USING (
    (auth.uid() = author_id) OR
    (organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true AND role IN ('admin', 'owner')
    )) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- 7. Show final state
SELECT 
    'After Migration - Final State' as info,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_workspace_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as public_mbi_posts,
    COUNT(*) FILTER (WHERE submission_status = 'submitted') as pending_approval,
    COUNT(*) FILTER (WHERE status = 'published') as published_posts,
    COUNT(*) FILTER (WHERE status = 'draft') as draft_posts
FROM blog_posts;

-- 8. Show posts by organization for reference
SELECT 
    'Posts by Organization' as info,
    o.name as org_name,
    COUNT(*) FILTER (WHERE bp.submission_status = 'private') as workspace_posts,
    COUNT(*) FILTER (WHERE bp.submission_status = 'published_mbi') as mbi_posts
FROM organizations o
LEFT JOIN blog_posts bp ON o.id = bp.organization_id
GROUP BY o.id, o.name
ORDER BY (COUNT(*) FILTER (WHERE bp.submission_status = 'published_mbi')) DESC;
