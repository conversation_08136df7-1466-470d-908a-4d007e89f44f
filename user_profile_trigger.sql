-- User Profile Auto-Creation Trigger
-- This automatically creates a profile and personal organization when a user signs up

-- 1. Create function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_org_id UUID;
BEGIN
  -- Insert user profile
  INSERT INTO profiles (id, email, full_name, role, subscription_plan)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    'user',
    'free'
  )
  ON CONFLICT (id) DO UPDATE SET
    email = NEW.email,
    full_name = COALESCE(NEW.raw_user_meta_data->>'full_name', profiles.full_name, NEW.email);

  -- Create personal organization for the user
  INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
  VALUES (
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email) || '''s Workspace',
    'user-' || NEW.id,
    'free',
    100,  -- Free tier workflow credits
    50    -- Free tier AI credits
  )
  ON CONFLICT (slug) DO NOTHING
  RETURNING id INTO user_org_id;

  -- If organization was created, add user as owner
  IF user_org_id IS NOT NULL THEN
    INSERT INTO organization_members (organization_id, user_id, role, joined_at)
    VALUES (user_org_id, NEW.id, 'owner', NOW())
    ON CONFLICT (organization_id, user_id) DO NOTHING;
  ELSE
    -- If organization already exists, get its ID and ensure membership
    SELECT id INTO user_org_id FROM organizations WHERE slug = 'user-' || NEW.id;
    
    INSERT INTO organization_members (organization_id, user_id, role, joined_at)
    VALUES (user_org_id, NEW.id, 'owner', NOW())
    ON CONFLICT (organization_id, user_id) DO NOTHING;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create trigger on auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 3. Handle existing users (run this once)
DO $$
DECLARE
  user_record RECORD;
  user_org_id UUID;
BEGIN
  FOR user_record IN SELECT * FROM auth.users LOOP
    -- Insert user profile if not exists
    INSERT INTO profiles (id, email, full_name, role, subscription_plan)
    VALUES (
      user_record.id,
      user_record.email,
      COALESCE(user_record.raw_user_meta_data->>'full_name', user_record.email),
      'user',
      'free'
    )
    ON CONFLICT (id) DO NOTHING;

    -- Create personal organization if not exists
    INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
    VALUES (
      COALESCE(user_record.raw_user_meta_data->>'full_name', user_record.email) || '''s Workspace',
      'user-' || user_record.id,
      'free',
      100,  -- Free tier workflow credits
      50    -- Free tier AI credits
    )
    ON CONFLICT (slug) DO NOTHING
    RETURNING id INTO user_org_id;

    -- Get organization ID if it already existed
    IF user_org_id IS NULL THEN
      SELECT id INTO user_org_id FROM organizations WHERE slug = 'user-' || user_record.id;
    END IF;

    -- Add user as owner of their organization
    INSERT INTO organization_members (organization_id, user_id, role, joined_at)
    VALUES (user_org_id, user_record.id, 'owner', NOW())
    ON CONFLICT (organization_id, user_id) DO NOTHING;
  END LOOP;
END $$;

-- 4. Update Stephen's profile if he exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    UPDATE profiles 
    SET role = 'platform_owner', subscription_plan = 'enterprise'
    WHERE email = '<EMAIL>';
    
    -- Add Stephen to MBI organization
    INSERT INTO organization_members (organization_id, user_id, role, joined_at)
    SELECT 
      o.id,
      p.id,
      'owner',
      NOW()
    FROM organizations o
    CROSS JOIN profiles p
    WHERE o.slug = 'mbi' 
    AND p.email = '<EMAIL>'
    ON CONFLICT (organization_id, user_id) DO UPDATE SET role = 'owner';
  END IF;
END $$;

SELECT 'User profile trigger created successfully!' as status;
