# Stripe Billing Integration Setup Guide

This guide will help you set up Stripe billing integration for your AI chat functionality.

## Prerequisites

- Stripe account (https://stripe.com)
- Supabase project with edge functions enabled
- Environment variables configured

## Step 1: Stripe Dashboard Setup

### 1.1 Create Stripe Account
1. Go to https://stripe.com and create an account
2. Complete the account verification process
3. Navigate to the Dashboard

### 1.2 Get API Keys
1. Go to **Developers > API keys**
2. Copy your **Publishable key** (starts with `pk_`)
3. Copy your **Secret key** (starts with `sk_`)
4. Keep these secure - you'll need them for environment variables

### 1.3 Create Products and Prices
1. Go to **Products** in the Stripe Dashboard
2. Create products for each subscription plan:

#### Basic Plan
- **Name**: Basic Plan
- **Description**: Advanced workflows and AI features
- **Pricing**: $29.00 USD / month
- **Copy the Price ID** (starts with `price_`)

#### Pro Plan
- **Name**: Pro Plan  
- **Description**: Enhanced automation and team features
- **Pricing**: $99.00 USD / month
- **Copy the Price ID** (starts with `price_`)

#### Enterprise Plan
- **Name**: Enterprise Plan
- **Description**: Full-scale automation and unlimited features
- **Pricing**: $299.00 USD / month
- **Copy the Price ID** (starts with `price_`)

### 1.4 Configure Webhooks
1. Go to **Developers > Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL to: `https://your-project.supabase.co/functions/v1/stripe-webhook`
4. Select these events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copy the **Webhook signing secret** (starts with `whsec_`)

## Step 2: Environment Variables

### 2.1 Update .env file
Create a `.env` file in your project root with:

```env
# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 2.2 Update Supabase Environment Variables
1. Go to your Supabase project dashboard
2. Navigate to **Settings > Environment Variables**
3. Add these variables:
   - `STRIPE_SECRET_KEY`: Your Stripe secret key
   - `STRIPE_WEBHOOK_SECRET`: Your webhook signing secret

## Step 3: Update Price IDs

### 3.1 Update Stripe Configuration
Edit `src/lib/stripe.ts` and replace the placeholder price IDs with your actual Stripe price IDs:

```typescript
export const STRIPE_PLANS = {
  basic: {
    priceId: 'price_your_actual_basic_price_id', // Replace this
    // ... rest of config
  },
  pro: {
    priceId: 'price_your_actual_pro_price_id', // Replace this
    // ... rest of config
  },
  enterprise: {
    priceId: 'price_your_actual_enterprise_price_id', // Replace this
    // ... rest of config
  }
}
```

### 3.2 Update Webhook Handler
Edit `supabase/functions/stripe-webhook/index.ts` and update the price ID mapping:

```typescript
function getPlanNameFromPriceId(priceId: string): string {
  const priceIdMap: Record<string, string> = {
    'price_your_actual_basic_price_id': 'basic',
    'price_your_actual_pro_price_id': 'pro', 
    'price_your_actual_enterprise_price_id': 'enterprise',
  }
  
  return priceIdMap[priceId] || 'free'
}
```

## Step 4: Deploy Supabase Edge Functions

### 4.1 Install Supabase CLI
```bash
npm install -g supabase
```

### 4.2 Login to Supabase
```bash
supabase login
```

### 4.3 Link Your Project
```bash
supabase link --project-ref your-project-ref
```

### 4.4 Deploy Edge Functions
```bash
# Deploy all Stripe-related functions
supabase functions deploy create-checkout-session
supabase functions deploy create-portal-session
supabase functions deploy stripe-webhook
supabase functions deploy get-subscription
supabase functions deploy cancel-subscription
supabase functions deploy update-subscription
```

### 4.5 Set Environment Variables
```bash
# Set Stripe secret key
supabase secrets set STRIPE_SECRET_KEY=sk_test_your_secret_key_here

# Set webhook secret
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## Step 5: Test the Integration

### 5.1 Test Mode
- Use Stripe test mode for development
- Test cards: https://stripe.com/docs/testing#cards
- Recommended test card: `4242 4242 4242 4242`

### 5.2 Test Workflow
1. Navigate to `/pricing` in your app
2. Click "Upgrade Now" on any paid plan
3. Complete the checkout process with a test card
4. Verify the subscription is created in Stripe Dashboard
5. Check that user's subscription is updated in your database

### 5.3 Test Webhooks
1. Use Stripe CLI to forward webhooks locally:
   ```bash
   stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook
   ```
2. Trigger test events in Stripe Dashboard
3. Verify webhook processing in Supabase logs

## Step 6: Go Live

### 6.1 Activate Live Mode
1. Complete Stripe account activation
2. Switch to live mode in Stripe Dashboard
3. Get live API keys and webhook secrets
4. Update environment variables with live keys

### 6.2 Update Webhook Endpoint
1. Update webhook endpoint URL to your production domain
2. Test with real payment methods

## Troubleshooting

### Common Issues

1. **Webhook signature verification failed**
   - Ensure webhook secret is correctly set
   - Check that the endpoint URL is correct

2. **Stripe not initialized**
   - Verify publishable key is set in environment variables
   - Check that the key starts with `pk_`

3. **Subscription not updating in database**
   - Check Supabase edge function logs
   - Verify webhook events are being received
   - Ensure database permissions are correct

### Debug Tools

1. **Stripe Dashboard Events**
   - View all webhook events and their status
   - Retry failed webhook deliveries

2. **Supabase Logs**
   - Check edge function execution logs
   - Monitor database operations

3. **Browser Console**
   - Check for JavaScript errors
   - Monitor network requests

## Security Notes

- Never expose secret keys in client-side code
- Use environment variables for all sensitive data
- Validate webhook signatures to prevent fraud
- Use HTTPS in production
- Regularly rotate API keys

## Support

For additional help:
- Stripe Documentation: https://stripe.com/docs
- Supabase Documentation: https://supabase.com/docs
- Contact support if you encounter issues
