-- Debug User Organization Setup
-- This script helps identify and fix organization issues for users

-- 1. Check what users exist
SELECT 'Current users in profiles table:' as info;
SELECT 
  id,
  email,
  full_name,
  role,
  created_at
FROM profiles 
ORDER BY created_at DESC;

-- 2. Check what organizations exist
SELECT 'Current organizations:' as info;
SELECT 
  id,
  name,
  slug,
  subscription_plan,
  workflow_credits_limit,
  ai_credits_limit,
  created_at
FROM organizations 
ORDER BY created_at DESC;

-- 3. Check organization memberships
SELECT 'Current organization memberships:' as info;
SELECT 
  om.id,
  o.name as organization_name,
  o.slug as organization_slug,
  p.email as user_email,
  om.role as membership_role,
  om.is_active,
  om.joined_at
FROM organization_members om
JOIN organizations o ON o.id = om.organization_id
JOIN profiles p ON p.id = om.user_id
ORDER BY om.joined_at DESC;

-- 4. Find users without organizations
SELECT 'Users without organizations:' as info;
SELECT 
  p.id,
  p.email,
  p.full_name,
  p.role
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.is_active = true
)
ORDER BY p.created_at;

-- 5. Check if tables exist and have the right structure
SELECT 'Organization table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'organizations' 
ORDER BY ordinal_position;

SELECT 'Organization members table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'organization_members' 
ORDER BY ordinal_position;
