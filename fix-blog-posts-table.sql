-- Quick fix for blog_posts table to add missing analytics columns
-- Run this FIRST if you're getting column errors

-- Add analytics columns to blog_posts table
DO $$ 
BEGIN
    -- Add view_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added view_count column to blog_posts';
    END IF;
    
    -- Add unique_view_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'unique_view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN unique_view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added unique_view_count column to blog_posts';
    END IF;
    
    -- Add click_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'click_count') THEN
        ALTER TABLE blog_posts ADD COLUMN click_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added click_count column to blog_posts';
    END IF;
    
    -- Add avg_reading_time if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'avg_reading_time') THEN
        ALTER TABLE blog_posts ADD COLUMN avg_reading_time INTEGER DEFAULT 0;
        RAISE NOTICE 'Added avg_reading_time column to blog_posts';
    END IF;
    
    -- Add bounce_rate if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'bounce_rate') THEN
        ALTER TABLE blog_posts ADD COLUMN bounce_rate DECIMAL(5,2) DEFAULT 0;
        RAISE NOTICE 'Added bounce_rate column to blog_posts';
    END IF;
    
    -- Add reaction_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'reaction_count') THEN
        ALTER TABLE blog_posts ADD COLUMN reaction_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added reaction_count column to blog_posts';
    END IF;
    
    -- Add comment_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'comment_count') THEN
        ALTER TABLE blog_posts ADD COLUMN comment_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added comment_count column to blog_posts';
    END IF;
    
    -- Add share_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'share_count') THEN
        ALTER TABLE blog_posts ADD COLUMN share_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added share_count column to blog_posts';
    END IF;
    
    RAISE NOTICE 'Blog posts table analytics columns setup complete!';
END $$;
