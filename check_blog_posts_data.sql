-- Check current blog posts data
-- Run this in Supabase Dashboard > SQL Editor

-- Check what's actually in the blog_posts table
SELECT 
    id,
    title,
    author_id,
    author_display,
    LEFT(content::text, 100) as content_preview,
    status,
    created_at,
    updated_at
FROM blog_posts 
ORDER BY updated_at DESC 
LIMIT 5;

-- Check if there are any NULL author_display values
SELECT COUNT(*) as total_posts,
       COUNT(author_display) as posts_with_author_display,
       COUNT(*) - COUNT(author_display) as posts_missing_author_display
FROM blog_posts;

-- Show distinct author_display values
SELECT author_display, COUNT(*) as count
FROM blog_posts 
GROUP BY author_display;
