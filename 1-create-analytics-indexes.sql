-- Step 1: Create Analytics Indexes
-- Run this FIRST in Supabase SQL Editor (run each CREATE INDEX statement individually)

-- IMPORTANT: Run each CREATE INDEX statement ONE AT A TIME in separate executions
-- Copy and paste each line individually into the SQL Editor

-- Index 1: Blog post views by date and post
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_created_at_post_id 
ON blog_post_views(created_at, post_id);

-- Index 2: Blog post views by post and date  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_post_created 
ON blog_post_views(post_id, created_at);

-- Index 3: Blog post clicks by date and post
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_clicks_created_at_post_id 
ON blog_post_clicks(created_at, post_id);

-- Index 4: Blog post clicks by post and date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_clicks_post_created 
ON blog_post_clicks(post_id, created_at);

-- Index 5: Blog post reading time by date and post
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_reading_time_created_at_post_id 
ON blog_post_reading_time(created_at, post_id);

-- Index 6: Blog post reading time by post and date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_reading_time_post_created 
ON blog_post_reading_time(post_id, created_at);

-- Index 7: Blog posts by status, organization, and published date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_posts_status_org_published 
ON blog_posts(status, organization_id, published_at) WHERE status = 'published';

-- Index 8: Blog post views by referrer and date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_referrer_created 
ON blog_post_views(referrer, created_at) WHERE referrer IS NOT NULL;

-- Index 9: Blog post views by device type and date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blog_post_views_device_created 
ON blog_post_views(device_type, created_at) WHERE device_type IS NOT NULL;
