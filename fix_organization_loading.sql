-- Fix Organization Loading Issue
-- This script creates organizations for users who don't have them

-- 1. First, ensure tables exist with basic structure
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  subscription_plan TEXT DEFAULT 'free',
  workflow_credits_used INTEGER DEFAULT 0,
  workflow_credits_limit INTEGER DEFAULT 100,
  ai_credits_used INTEGER DEFAULT 0,
  ai_credits_limit INTEGER DEFAULT 50,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'owner',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  UNIQUE(organization_id, user_id)
);

-- 2. Create organizations for users who don't have any
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
SELECT
  CASE 
    WHEN p.full_name IS NOT NULL AND p.full_name != '' 
    THEN p.full_name || '''s Workspace'
    ELSE p.email || '''s Workspace'
  END as name,
  'user-' || p.id as slug,
  'free' as subscription_plan,
  100 as workflow_credits_limit,
  50 as ai_credits_limit
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.is_active = true
)
ON CONFLICT (slug) DO NOTHING;

-- 3. Add users as owners of their organizations
INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
SELECT 
  o.id as organization_id,
  p.id as user_id,
  'owner' as role,
  NOW() as joined_at,
  true as is_active
FROM profiles p
JOIN organizations o ON o.slug = 'user-' || p.id
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.organization_id = o.id AND om.user_id = p.id
)
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- 4. Enable RLS if not already enabled
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;

-- 5. Create or update RLS policies
DROP POLICY IF EXISTS "Users can view their organizations" ON organizations;
CREATE POLICY "Users can view their organizations" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

DROP POLICY IF EXISTS "Users can view their memberships" ON organization_members;
CREATE POLICY "Users can view their memberships" ON organization_members
  FOR SELECT USING (user_id = auth.uid());

-- 6. Show results
SELECT 'Fix completed! Here are the results:' as status;

SELECT 'Users and their organizations:' as info;
SELECT 
  p.email,
  p.full_name,
  o.name as organization_name,
  o.slug as organization_slug,
  o.subscription_plan,
  o.workflow_credits_limit,
  o.ai_credits_limit,
  om.role as membership_role
FROM profiles p
LEFT JOIN organization_members om ON om.user_id = p.id AND om.is_active = true
LEFT JOIN organizations o ON o.id = om.organization_id
ORDER BY p.created_at;

SELECT 'Users still without organizations (should be empty):' as info;
SELECT 
  p.email,
  p.full_name
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.is_active = true
);

SELECT 'Organization loading should now work!' as final_status;
