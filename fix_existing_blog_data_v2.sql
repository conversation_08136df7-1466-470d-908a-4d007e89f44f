-- Fix existing blog data after hybrid blog system migration (Version 2)
-- This version handles existing policies and data more gracefully

-- 1. Drop the temporary policy if it exists (to avoid conflicts)
DROP POLICY IF EXISTS "Temporary: Allow viewing all posts during migration" ON blog_posts;

-- 2. Update existing blog posts to have proper submission_status and organization_id
UPDATE blog_posts 
SET 
  submission_status = COALESCE(submission_status, 'private'),
  organization_id = COALESCE(
    organization_id,
    (
      SELECT om.organization_id 
      FROM organization_members om 
      WHERE om.user_id = blog_posts.author_id 
      AND om.is_active = true 
      LIMIT 1
    )
  )
WHERE submission_status IS NULL OR organization_id IS NULL;

-- 3. For posts that still don't have an organization (orphaned posts), 
-- assign them to the first available organization or create a default one
DO $$
DECLARE
  default_org_id UUID;
  orphaned_count INTEGER;
BEGIN
  -- Count orphaned posts
  SELECT COUNT(*) INTO orphaned_count
  FROM blog_posts 
  WHERE organization_id IS NULL;
  
  IF orphaned_count > 0 THEN
    -- Get the first organization or create a default one
    SELECT id INTO default_org_id 
    FROM organizations 
    ORDER BY created_at ASC 
    LIMIT 1;
    
    IF default_org_id IS NULL THEN
      -- Create a default organization if none exists
      INSERT INTO organizations (name, slug, description, subscription_plan)
      VALUES ('Default Organization', 'default-org', 'Default organization for existing content', 'free')
      RETURNING id INTO default_org_id;
      
      RAISE NOTICE 'Created default organization with ID: %', default_org_id;
    END IF;
    
    -- Assign orphaned posts to the default organization
    UPDATE blog_posts 
    SET organization_id = default_org_id
    WHERE organization_id IS NULL;
    
    RAISE NOTICE 'Updated % orphaned blog posts', orphaned_count;
  END IF;
END $$;

-- 4. Create a NEW temporary policy to allow viewing all posts during transition
-- Using a different name to avoid conflicts
CREATE POLICY "Migration temp policy - view all posts" ON blog_posts
  FOR SELECT USING (true);

-- 5. Show the current state after fixes
SELECT 
    'After Fix - Blog System Status' as info,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts,
    COUNT(*) FILTER (WHERE submission_status = 'submitted') as submitted_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_published_posts,
    COUNT(*) FILTER (WHERE organization_id IS NOT NULL) as posts_with_org,
    COUNT(*) FILTER (WHERE organization_id IS NULL) as orphaned_posts
FROM blog_posts;

-- 6. Show organization distribution
SELECT 
    o.name as organization_name,
    COUNT(bp.id) as post_count,
    COUNT(*) FILTER (WHERE bp.status = 'published') as published_count
FROM organizations o
LEFT JOIN blog_posts bp ON o.id = bp.organization_id
GROUP BY o.id, o.name
ORDER BY post_count DESC;
