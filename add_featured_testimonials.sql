-- Add featured flag to blog_posts for homepage testimonials
-- Run this in Supabase Dashboard > SQL Editor
-- Safe to run multiple times

-- Add is_featured column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_posts' AND column_name = 'is_featured'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN is_featured BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_featured column to blog_posts';
    ELSE
        RAISE NOTICE 'is_featured column already exists';
    END IF;
END $$;

-- Add display_order column for controlling order of featured items
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_posts' AND column_name = 'display_order'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN display_order INTEGER DEFAULT 0;
        RAISE NOTICE 'Added display_order column to blog_posts';
    ELSE
        RAISE NOTICE 'display_order column already exists';
    END IF;
END $$;

-- Add index on is_featured for better query performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_featured ON blog_posts(is_featured, display_order);

-- Add comments to document the columns
COMMENT ON COLUMN blog_posts.is_featured IS 'Whether this testimonial should be featured on the homepage';
COMMENT ON COLUMN blog_posts.display_order IS 'Order for displaying featured testimonials (lower numbers first)';

-- Verify the columns were added
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'blog_posts'
AND column_name IN ('is_featured', 'display_order')
ORDER BY column_name;

-- Show current testimonials that could be featured
SELECT 
    id, 
    title, 
    category, 
    tags, 
    is_featured,
    display_order,
    status,
    created_at
FROM blog_posts 
WHERE category = 'testimonials' OR (tags IS NOT NULL AND 'testimonial' = ANY(tags))
ORDER BY created_at DESC 
LIMIT 10;
