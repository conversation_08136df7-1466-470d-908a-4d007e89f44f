-- Fix Workspace/Organization Isolation for Blog Posts
-- This migration ensures that blog posts are properly scoped to organizations
-- and that admins can only see content from their own organization

-- 1. Add organization_id to blog_posts if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_posts' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE blog_posts ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added organization_id column to blog_posts';
    ELSE
        RAISE NOTICE 'Organization_id column already exists in blog_posts';
    END IF;
END $$;

-- 2. Update existing blog posts to belong to their author's organization
UPDATE blog_posts 
SET organization_id = (
    SELECT om.organization_id 
    FROM organization_members om 
    WHERE om.user_id = blog_posts.author_id 
    AND om.is_active = true
    AND om.role IN ('owner', 'admin', 'editor')
    LIMIT 1
)
WHERE organization_id IS NULL;

-- 3. Add organization_id to other blog-related tables
DO $$
BEGIN
    -- Blog comments
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_comments' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE blog_comments ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added organization_id column to blog_comments';
    END IF;

    -- Blog reactions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_reactions' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE blog_reactions ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added organization_id column to blog_reactions';
    END IF;

    -- Blog saves
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'blog_saves' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE blog_saves ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added organization_id column to blog_saves';
    END IF;
END $$;

-- 4. Update blog comments to inherit organization from their post
UPDATE blog_comments 
SET organization_id = (
    SELECT bp.organization_id 
    FROM blog_posts bp 
    WHERE bp.id = blog_comments.post_id
)
WHERE organization_id IS NULL;

-- 5. Update blog reactions to inherit organization from their post
UPDATE blog_reactions 
SET organization_id = (
    SELECT bp.organization_id 
    FROM blog_posts bp 
    WHERE bp.id = blog_reactions.post_id
)
WHERE organization_id IS NULL;

-- 6. Update blog saves to inherit organization from their post
UPDATE blog_saves 
SET organization_id = (
    SELECT bp.organization_id 
    FROM blog_posts bp 
    WHERE bp.id = blog_saves.post_id
)
WHERE organization_id IS NULL;

-- 7. Drop existing blog post policies
DROP POLICY IF EXISTS "Anyone can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can view their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can create posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Published posts are viewable by everyone" ON blog_posts;
DROP POLICY IF EXISTS "Users can insert their own profile" ON blog_posts;
DROP POLICY IF EXISTS "Authors can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Authors can delete their own posts" ON blog_posts;

-- 8. Create new organization-scoped blog post policies

-- Policy 1: Anyone can view published posts (public blog reading)
CREATE POLICY "Anyone can view published posts" ON blog_posts
  FOR SELECT USING (status = 'published');

-- Policy 2: Organization members can view all posts within their organization
CREATE POLICY "Organization members can view org posts" ON blog_posts
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR
    -- Super admins can see everything
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- Policy 3: Users can create posts in their organization
CREATE POLICY "Users can create posts in their org" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('editor', 'admin', 'owner')
    )
  );

-- Policy 4: Users can update their own posts OR org admins can update any post in their org
CREATE POLICY "Users can update posts in their org" ON blog_posts
  FOR UPDATE USING (
    (auth.uid() = author_id) OR
    (organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can update everything
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- Policy 5: Users can delete their own posts OR org admins can delete any post in their org
CREATE POLICY "Users can delete posts in their org" ON blog_posts
  FOR DELETE USING (
    (auth.uid() = author_id) OR
    (organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can delete everything
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- 9. Update blog comments policies for organization scoping
DROP POLICY IF EXISTS "Anyone can view comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can create comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Users can delete their own comments" ON blog_comments;

CREATE POLICY "Anyone can view comments on published posts" ON blog_comments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM blog_posts bp 
      WHERE bp.id = blog_comments.post_id 
      AND bp.status = 'published'
    )
    OR
    -- Organization members can see comments on all org posts
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Users can create comments" ON blog_comments
  FOR INSERT WITH CHECK (
    -- Set organization_id from the post
    organization_id = (
      SELECT bp.organization_id FROM blog_posts bp WHERE bp.id = blog_comments.post_id
    )
  );

CREATE POLICY "Users can update their own comments" ON blog_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON blog_comments
  FOR DELETE USING (
    auth.uid() = user_id OR
    -- Org admins can delete comments in their org
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )
  );

-- 10. Update blog reactions policies for organization scoping
DROP POLICY IF EXISTS "Anyone can view reactions" ON blog_reactions;
DROP POLICY IF EXISTS "Users can manage their own reactions" ON blog_reactions;

CREATE POLICY "Anyone can view reactions" ON blog_reactions
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own reactions" ON blog_reactions
  FOR ALL USING (
    auth.uid() = user_id OR anonymous_id IS NOT NULL
  )
  WITH CHECK (
    -- Set organization_id from the post
    organization_id = (
      SELECT bp.organization_id FROM blog_posts bp WHERE bp.id = blog_reactions.post_id
    )
  );

-- 11. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_organization_id ON blog_posts(organization_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_organization_id ON blog_comments(organization_id);
CREATE INDEX IF NOT EXISTS idx_blog_reactions_organization_id ON blog_reactions(organization_id);
CREATE INDEX IF NOT EXISTS idx_blog_saves_organization_id ON blog_saves(organization_id);

-- 12. Create function to automatically set organization_id for new blog content
CREATE OR REPLACE FUNCTION set_blog_organization_id()
RETURNS TRIGGER AS $$
BEGIN
    -- For blog posts, use the author's primary organization
    IF TG_TABLE_NAME = 'blog_posts' THEN
        IF NEW.organization_id IS NULL THEN
            SELECT organization_id INTO NEW.organization_id
            FROM organization_members 
            WHERE user_id = NEW.author_id 
            AND is_active = true
            ORDER BY role = 'owner' DESC, role = 'admin' DESC, created_at ASC
            LIMIT 1;
        END IF;
    END IF;
    
    -- For comments and reactions, inherit from the post
    IF TG_TABLE_NAME IN ('blog_comments', 'blog_reactions', 'blog_saves') THEN
        IF NEW.organization_id IS NULL THEN
            SELECT bp.organization_id INTO NEW.organization_id
            FROM blog_posts bp 
            WHERE bp.id = NEW.post_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 13. Create triggers to automatically set organization_id
DROP TRIGGER IF EXISTS set_blog_post_organization_id ON blog_posts;
CREATE TRIGGER set_blog_post_organization_id
    BEFORE INSERT ON blog_posts
    FOR EACH ROW
    EXECUTE FUNCTION set_blog_organization_id();

DROP TRIGGER IF EXISTS set_blog_comment_organization_id ON blog_comments;
CREATE TRIGGER set_blog_comment_organization_id
    BEFORE INSERT ON blog_comments
    FOR EACH ROW
    EXECUTE FUNCTION set_blog_organization_id();

DROP TRIGGER IF EXISTS set_blog_reaction_organization_id ON blog_reactions;
CREATE TRIGGER set_blog_reaction_organization_id
    BEFORE INSERT ON blog_reactions
    FOR EACH ROW
    EXECUTE FUNCTION set_blog_organization_id();

DROP TRIGGER IF EXISTS set_blog_save_organization_id ON blog_saves;
CREATE TRIGGER set_blog_save_organization_id
    BEFORE INSERT ON blog_saves
    FOR EACH ROW
    EXECUTE FUNCTION set_blog_organization_id();

-- 14. Show organization isolation status
SELECT 
    'Blog Posts Organization Status' as info,
    COUNT(*) as total_posts,
    COUNT(organization_id) as posts_with_org,
    COUNT(*) - COUNT(organization_id) as posts_without_org
FROM blog_posts;

SELECT 
    'Organization Membership Check' as info,
    o.name as organization_name,
    COUNT(bp.id) as blog_posts_count
FROM organizations o
LEFT JOIN blog_posts bp ON bp.organization_id = o.id
GROUP BY o.id, o.name
ORDER BY blog_posts_count DESC;
