-- Hybrid Blog System: Private Workspaces + Public MBI Blog
-- This migration implements a system where:
-- 1. Organizations have private blog workspaces
-- 2. They can submit posts for approval to the main MBI blog
-- 3. Approved posts appear on the public blog with MBI branding

-- 1. Add fields to support hybrid blog system
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS submission_status VARCHAR(20) DEFAULT 'private';
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS submitted_for_approval BOOLEAN DEFAULT FALSE;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES profiles(id);
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS rejection_reason TEXT;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS original_organization_id UUID REFERENCES organizations(id);
<PERSON>TER TABLE blog_posts ADD COLUMN IF NOT EXISTS is_mbi_featured BOOLEAN DEFAULT FALSE;

-- 2. Create submission status enum constraint
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'blog_posts_submission_status_check'
    ) THEN
        ALTER TABLE blog_posts ADD CONSTRAINT blog_posts_submission_status_check 
        CHECK (submission_status IN ('private', 'submitted', 'approved', 'rejected', 'published_mbi'));
    END IF;
END $$;

-- 3. Create blog submissions table for tracking submission history
CREATE TABLE IF NOT EXISTS blog_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    submitted_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    submission_message TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    reviewed_by UUID REFERENCES profiles(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT blog_submissions_status_check 
    CHECK (status IN ('pending', 'approved', 'rejected', 'published'))
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_submission_status ON blog_posts(submission_status);
CREATE INDEX IF NOT EXISTS idx_blog_posts_submitted_for_approval ON blog_posts(submitted_for_approval);
CREATE INDEX IF NOT EXISTS idx_blog_posts_original_organization ON blog_posts(original_organization_id);
CREATE INDEX IF NOT EXISTS idx_blog_submissions_status ON blog_submissions(status);
CREATE INDEX IF NOT EXISTS idx_blog_submissions_organization ON blog_submissions(organization_id);

-- 5. Update RLS policies for hybrid system

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Anyone can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Organization members can view org posts" ON blog_posts;

-- New hybrid policies
-- Policy 1: Public can view MBI-approved posts (published on main blog)
CREATE POLICY "Public can view MBI published posts" ON blog_posts
  FOR SELECT USING (
    status = 'published' AND 
    (submission_status = 'published_mbi' OR organization_id IS NULL)
  );

-- Policy 2: Organization members can view their private workspace posts
CREATE POLICY "Organization members can view private posts" ON blog_posts
  FOR SELECT USING (
    submission_status = 'private' AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Policy 3: Super admins can view all posts for moderation
CREATE POLICY "Super admins can view all posts" ON blog_posts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- Policy 4: Users can create posts in their organization (private by default)
CREATE POLICY "Users can create private posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('editor', 'admin', 'owner')
    ) AND
    submission_status = 'private'
  );

-- Policy 5: Users can update their own posts OR org admins can update org posts
CREATE POLICY "Users can update posts in their scope" ON blog_posts
  FOR UPDATE USING (
    -- Own posts
    (auth.uid() = author_id) OR
    -- Org admin can update org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can update everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- Policy 6: Delete permissions similar to update
CREATE POLICY "Users can delete posts in their scope" ON blog_posts
  FOR DELETE USING (
    -- Own posts
    (auth.uid() = author_id) OR
    -- Org admin can delete org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can delete everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- 6. RLS policies for blog_submissions table
ALTER TABLE blog_submissions ENABLE ROW LEVEL SECURITY;

-- Organization members can view their submissions
CREATE POLICY "Organization members can view submissions" ON blog_submissions
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    ) OR
    -- Super admins can see all submissions
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- Organization admins can create submissions
CREATE POLICY "Organization admins can create submissions" ON blog_submissions
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    ) AND
    submitted_by = auth.uid()
  );

-- Super admins can update submissions (for review)
CREATE POLICY "Super admins can update submissions" ON blog_submissions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- 7. Create function to handle blog submission workflow
CREATE OR REPLACE FUNCTION submit_blog_for_approval(
  post_id UUID,
  submission_message TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  submission_id UUID;
  post_org_id UUID;
  user_id UUID;
BEGIN
  -- Get current user
  user_id := auth.uid();
  IF user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;

  -- Get post organization
  SELECT organization_id INTO post_org_id
  FROM blog_posts 
  WHERE id = post_id AND author_id = user_id;

  IF post_org_id IS NULL THEN
    RAISE EXCEPTION 'Post not found or access denied';
  END IF;

  -- Update post status
  UPDATE blog_posts 
  SET 
    submission_status = 'submitted',
    submitted_for_approval = TRUE,
    submitted_at = NOW(),
    original_organization_id = post_org_id
  WHERE id = post_id;

  -- Create submission record
  INSERT INTO blog_submissions (
    post_id,
    organization_id,
    submitted_by,
    submission_message,
    status
  ) VALUES (
    post_id,
    post_org_id,
    user_id,
    submission_message,
    'pending'
  ) RETURNING id INTO submission_id;

  RETURN submission_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to approve/reject submissions
CREATE OR REPLACE FUNCTION review_blog_submission(
  submission_id UUID,
  action VARCHAR(10), -- 'approve' or 'reject'
  review_notes TEXT DEFAULT NULL,
  publish_to_mbi BOOLEAN DEFAULT FALSE
) RETURNS BOOLEAN AS $$
DECLARE
  submission_record blog_submissions%ROWTYPE;
  user_id UUID;
BEGIN
  -- Get current user
  user_id := auth.uid();
  IF user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;

  -- Check if user is super admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND role = 'super_admin'
  ) THEN
    RAISE EXCEPTION 'Access denied: Super admin required';
  END IF;

  -- Get submission
  SELECT * INTO submission_record
  FROM blog_submissions 
  WHERE id = submission_id;

  IF submission_record.id IS NULL THEN
    RAISE EXCEPTION 'Submission not found';
  END IF;

  -- Update submission
  UPDATE blog_submissions 
  SET 
    status = action || 'ed', -- 'approved' or 'rejected'
    reviewed_by = user_id,
    reviewed_at = NOW(),
    review_notes = review_notes
  WHERE id = submission_id;

  -- Update blog post based on action
  IF action = 'approve' THEN
    UPDATE blog_posts 
    SET 
      submission_status = CASE 
        WHEN publish_to_mbi THEN 'published_mbi'
        ELSE 'approved'
      END,
      approved_by = user_id,
      approved_at = NOW(),
      is_mbi_featured = publish_to_mbi,
      -- If publishing to MBI, remove organization constraint for public access
      organization_id = CASE 
        WHEN publish_to_mbi THEN NULL 
        ELSE organization_id 
      END
    WHERE id = submission_record.post_id;
  ELSE
    UPDATE blog_posts 
    SET 
      submission_status = 'rejected',
      rejection_reason = review_notes
    WHERE id = submission_record.post_id;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create function to get blog posts for different contexts
CREATE OR REPLACE FUNCTION get_blog_posts_by_context(
  context_type VARCHAR(20), -- 'public', 'private', 'submissions'
  org_id UUID DEFAULT NULL,
  user_id UUID DEFAULT NULL
) RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  excerpt TEXT,
  status VARCHAR(20),
  submission_status VARCHAR(20),
  organization_name TEXT,
  author_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  IF context_type = 'public' THEN
    -- Public MBI blog posts
    RETURN QUERY
    SELECT 
      bp.id,
      bp.title,
      bp.slug,
      bp.excerpt,
      bp.status,
      bp.submission_status,
      COALESCE(o.name, 'MBI') as organization_name,
      p.full_name as author_name,
      bp.created_at
    FROM blog_posts bp
    LEFT JOIN organizations o ON bp.original_organization_id = o.id
    LEFT JOIN profiles p ON bp.author_id = p.id
    WHERE bp.status = 'published' 
      AND bp.submission_status = 'published_mbi'
    ORDER BY bp.created_at DESC;
    
  ELSIF context_type = 'private' AND org_id IS NOT NULL THEN
    -- Private organization workspace
    RETURN QUERY
    SELECT 
      bp.id,
      bp.title,
      bp.slug,
      bp.excerpt,
      bp.status,
      bp.submission_status,
      o.name as organization_name,
      p.full_name as author_name,
      bp.created_at
    FROM blog_posts bp
    LEFT JOIN organizations o ON bp.organization_id = o.id
    LEFT JOIN profiles p ON bp.author_id = p.id
    WHERE bp.organization_id = org_id 
      AND bp.submission_status = 'private'
    ORDER BY bp.created_at DESC;
    
  ELSIF context_type = 'submissions' THEN
    -- Pending submissions for super admin review
    RETURN QUERY
    SELECT 
      bp.id,
      bp.title,
      bp.slug,
      bp.excerpt,
      bp.status,
      bp.submission_status,
      o.name as organization_name,
      p.full_name as author_name,
      bp.created_at
    FROM blog_posts bp
    LEFT JOIN organizations o ON bp.organization_id = o.id
    LEFT JOIN profiles p ON bp.author_id = p.id
    WHERE bp.submission_status = 'submitted'
    ORDER BY bp.submitted_at DESC;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Show current blog system status
SELECT 
    'Blog System Status' as info,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts,
    COUNT(*) FILTER (WHERE submission_status = 'submitted') as submitted_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_published_posts
FROM blog_posts;
