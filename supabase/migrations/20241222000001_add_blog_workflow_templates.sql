-- Add Blog and Credit Workflow Templates
-- This migration adds sample workflow templates for the new blog and credit triggers

-- 1. Blog Comment Notification Workflow Template
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'Blog Comment Notifications',
  'Automatically notify blog authors when someone comments on their posts',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "blog_comment",
        "label": "Blog Comment Added"
      },
      "connections": ["notification-1"]
    },
    {
      "id": "notification-1",
      "type": "action",
      "position": {"x": 350, "y": 100},
      "data": {
        "actionType": "send_notification",
        "label": "Notify Blog Author",
        "config": {
          "notificationType": "blog_comment",
          "title": "New Comment on Your Blog Post",
          "message": "Someone commented on your blog post"
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'Blog Comment Notifications' AND is_template = true
);

-- 2. Blog Auto-Response Workflow Template
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'Blog Comment Auto-Response',
  'Automatically respond to comments on blog posts with a thank you message',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "blog_comment",
        "label": "Blog Comment Added"
      },
      "connections": ["delay-1"]
    },
    {
      "id": "delay-1",
      "type": "action",
      "position": {"x": 350, "y": 100},
      "data": {
        "actionType": "delay",
        "label": "Wait 2 minutes",
        "config": {
          "delayMs": 120000
        }
      },
      "connections": ["response-1"]
    },
    {
      "id": "response-1",
      "type": "action",
      "position": {"x": 600, "y": 100},
      "data": {
        "actionType": "auto_response",
        "label": "Send Auto Response",
        "config": {
          "responseTemplate": "Thank you for your comment, {{ commenter_name }}! We appreciate your engagement with our content."
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'Blog Comment Auto-Response' AND is_template = true
);

-- 3. Blog Reaction Notification Workflow Template
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'Blog Reaction Notifications',
  'Notify blog authors when someone reacts to their posts',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "blog_reaction",
        "label": "Blog Post Reaction"
      },
      "connections": ["notification-1"]
    },
    {
      "id": "notification-1",
      "type": "action",
      "position": {"x": 350, "y": 100},
      "data": {
        "actionType": "send_notification",
        "label": "Notify Blog Author",
        "config": {
          "notificationType": "blog_reaction",
          "title": "Someone Reacted to Your Blog Post",
          "message": "Your blog post received a new reaction!"
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'Blog Reaction Notifications' AND is_template = true
);

-- 4. Low Credits Alert Workflow Template
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'Low Credits Alert',
  'Send email alerts when workflow credits are running low',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "low_credits",
        "label": "Low Credits Warning",
        "creditType": "workflow"
      },
      "connections": ["email-1"]
    },
    {
      "id": "email-1",
      "type": "action",
      "position": {"x": 350, "y": 100},
      "data": {
        "actionType": "send_email",
        "label": "Send Alert Email",
        "config": {
          "to": "{{ user.email }}",
          "subject": "Low Workflow Credits Alert",
          "body": "Hi {{ user.name }},\\n\\nYour workflow credits are running low. You have used {{ usage_percentage }}% of your monthly allocation.\\n\\nConsider upgrading your plan to avoid interruptions.\\n\\nBest regards,\\nMBI Team"
        }
      },
      "connections": ["notification-1"]
    },
    {
      "id": "notification-1",
      "type": "action",
      "position": {"x": 600, "y": 100},
      "data": {
        "actionType": "send_notification",
        "label": "In-App Notification",
        "config": {
          "notificationType": "low_credits",
          "title": "Low Credits Warning",
          "message": "Your workflow credits are running low. Consider upgrading your plan."
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'Low Credits Alert' AND is_template = true
);

-- 5. New Blog Post Notification Workflow Template
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'New Blog Post Notifications',
  'Notify team members when a new blog post is published',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "blog_created",
        "label": "Blog Post Created"
      },
      "connections": ["condition-1"]
    },
    {
      "id": "condition-1",
      "type": "condition",
      "position": {"x": 350, "y": 100},
      "data": {
        "conditionType": "status_check",
        "label": "Check if Published",
        "field": "status",
        "operator": "equals",
        "value": "published"
      },
      "connections": ["notification-1"]
    },
    {
      "id": "notification-1",
      "type": "action",
      "position": {"x": 600, "y": 100},
      "data": {
        "actionType": "send_notification",
        "label": "Notify Team",
        "config": {
          "notificationType": "new_blog_post",
          "title": "New Blog Post Published",
          "message": "A new blog post has been published: {{ blog_post.title }}"
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'New Blog Post Notifications' AND is_template = true
);

-- 6. Update existing newsletter workflow template to include new variables
UPDATE workflows 
SET nodes = '[
  {
    "id": "trigger-1",
    "type": "trigger",
    "position": {"x": 100, "y": 100},
    "data": {
      "triggerType": "newsletter_signup",
      "label": "Newsletter Signup"
    },
    "connections": ["delay-1"]
  },
  {
    "id": "delay-1", 
    "type": "action",
    "position": {"x": 300, "y": 100},
    "data": {
      "actionType": "delay",
      "label": "Wait 5 minutes",
      "config": {
        "delayMs": 300000
      }
    },
    "connections": ["email-1"]
  },
  {
    "id": "email-1",
    "type": "action", 
    "position": {"x": 500, "y": 100},
    "data": {
      "actionType": "send_email",
      "label": "Welcome Email",
      "config": {
        "to": "{{ email }}",
        "subject": "Welcome to MBI Newsletter!",
        "body": "Hi {{ name || \"there\" }},\\n\\nThank you for subscribing to our newsletter! You will receive updates about business innovations, tech trends, and exclusive insights.\\n\\nWelcome aboard!\\n\\nBest regards,\\nMBI Team"
      }
    },
    "connections": []
  }
]'::jsonb
WHERE name = 'Newsletter Welcome Series' AND is_template = true;

-- 7. Show all workflow templates
SELECT 
  'Workflow Templates' as info,
  name,
  description,
  is_template,
  active,
  created_at
FROM workflows 
WHERE is_template = true
ORDER BY created_at DESC;
