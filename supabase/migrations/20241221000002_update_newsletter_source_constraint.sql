-- Update newsletter source constraint to allow more source types
-- This fixes the constraint violation when subscribing from different sources

-- Drop the existing constraint
ALTER TABLE newsletter_subscribers DROP CONSTRAINT IF EXISTS newsletter_subscribers_source_check;

-- Add the updated constraint with more allowed values
ALTER TABLE newsletter_subscribers ADD CONSTRAINT newsletter_subscribers_source_check
CHECK (source IN ('website', 'blog', 'quote_form', 'manual', 'footer', 'newsletter', 'contact', 'social', 'referral', 'api', 'website_footer'));

-- Fix profiles table role constraint and set <PERSON> as owner
-- Drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Add the new constraint with all roles (removing viewer - everyone can contribute)
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('owner', 'super_admin', 'admin', 'editor'));

-- Update <PERSON>'s role to owner
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- Update any existing 'viewer' roles to 'editor' (since we're removing viewer role)
UPDATE profiles
SET role = 'editor'
WHERE role = 'viewer' AND email != '<EMAIL>';

-- If <PERSON>'s profile doesn't exist, create it
INSERT INTO profiles (id, email, full_name, role)
SELECT
    auth.users.id,
    auth.users.email,
    COALESCE(auth.users.raw_user_meta_data->>'full_name', auth.users.email),
    'owner'
FROM auth.users
WHERE auth.users.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM profiles WHERE profiles.email = '<EMAIL>'
);

-- Add new blog post status for approval workflow
-- First check if the constraint exists and drop it
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'blog_posts_status_check'
        AND table_name = 'blog_posts'
    ) THEN
        ALTER TABLE blog_posts DROP CONSTRAINT blog_posts_status_check;
    END IF;
END $$;

-- Add the new constraint with pending_approval status
ALTER TABLE blog_posts
ADD CONSTRAINT blog_posts_status_check
CHECK (status IN ('draft', 'pending_approval', 'published', 'archived', 'rejected'));

-- Update RLS policies for better blog workflow
-- Drop existing policies
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;

-- New policy: Users with editor+ role can create posts but they start as draft or pending_approval
CREATE POLICY "Users can create posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('editor', 'admin', 'super_admin', 'owner')
    ) AND
    status IN ('draft', 'pending_approval')
  );

-- New policy: Users can update their own posts (but viewers/editors can't publish directly)
CREATE POLICY "Users can update their own posts" ON blog_posts
  FOR UPDATE USING (
    (auth.uid() = author_id AND
     EXISTS (
       SELECT 1 FROM profiles
       WHERE profiles.id = auth.uid()
       AND profiles.role IN ('viewer', 'editor')
     ) AND
     status IN ('draft', 'pending_approval', 'rejected')) -- Viewers/Editors can only modify non-published posts
    OR
    (EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )) -- Admins can update any post
  );

-- Verify the update
SELECT
    'Stephen Profile Check' as check_type,
    id,
    email,
    full_name,
    role,
    created_at
FROM profiles
WHERE email = '<EMAIL>';
