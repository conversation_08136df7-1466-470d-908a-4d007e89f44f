-- Create workflows table
CREATE TABLE IF NOT EXISTS workflows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  nodes JSONB NOT NULL DEFAULT '[]'::jsonb,
  active BOOLEAN DEFAULT true,
  is_template BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow execution logs table
CREATE TABLE IF NOT EXISTS workflow_executions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workflow_id UUID REFERENCES workflows(id) ON DELETE CASCADE,
  workflow_name TEXT NOT NULL,
  trigger_data JSONB,
  status TEXT CHECK (status IN ('success', 'failed', 'pending')) DEFAULT 'pending',
  error_message TEXT,
  execution_time_ms INTEGER,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workflows_created_by ON workflows(created_by);
CREATE INDEX IF NOT EXISTS idx_workflows_active ON workflows(active);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_executed_at ON workflow_executions(executed_at);

-- Enable RLS (Row Level Security)
ALTER TABLE workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for workflows
CREATE POLICY "Users can view workflows they created" ON workflows
  FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can create workflows" ON workflows
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own workflows" ON workflows
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own workflows" ON workflows
  FOR DELETE USING (created_by = auth.uid());

-- Admin users can manage all workflows
CREATE POLICY "Admins can manage all workflows" ON workflows
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create RLS policies for workflow executions
CREATE POLICY "Users can view executions for their workflows" ON workflow_executions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM workflows 
      WHERE workflows.id = workflow_executions.workflow_id 
      AND workflows.created_by = auth.uid()
    )
  );

CREATE POLICY "System can create execution logs" ON workflow_executions
  FOR INSERT WITH CHECK (true);

-- Admin users can view all execution logs
CREATE POLICY "Admins can view all execution logs" ON workflow_executions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_workflows_updated_at 
  BEFORE UPDATE ON workflows 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Create function to log workflow executions
CREATE OR REPLACE FUNCTION log_workflow_execution(
  p_workflow_id UUID,
  p_workflow_name TEXT,
  p_trigger_data JSONB DEFAULT NULL,
  p_status TEXT DEFAULT 'pending',
  p_error_message TEXT DEFAULT NULL,
  p_execution_time_ms INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  execution_id UUID;
BEGIN
  INSERT INTO workflow_executions (
    workflow_id,
    workflow_name,
    trigger_data,
    status,
    error_message,
    execution_time_ms
  ) VALUES (
    p_workflow_id,
    p_workflow_name,
    p_trigger_data,
    p_status,
    p_error_message,
    p_execution_time_ms
  ) RETURNING id INTO execution_id;
  
  RETURN execution_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create email integrations table
CREATE TABLE IF NOT EXISTS email_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  provider TEXT NOT NULL CHECK (provider IN ('gmail', 'outlook', 'resend', 'postmark', 'sendgrid', 'mailgun', 'smtp')),
  config JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email templates table
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  integration_id UUID REFERENCES email_integrations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  variables JSONB DEFAULT '[]',
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email integrations
CREATE INDEX IF NOT EXISTS idx_email_integrations_user_id ON email_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_email_integrations_active ON email_integrations(is_active);
CREATE INDEX IF NOT EXISTS idx_email_templates_user_id ON email_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_integration_id ON email_templates(integration_id);

-- Enable RLS for email integrations
ALTER TABLE email_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;

-- RLS policies for email integrations
CREATE POLICY "Users can view their own email integrations" ON email_integrations
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create email integrations" ON email_integrations
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own email integrations" ON email_integrations
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own email integrations" ON email_integrations
  FOR DELETE USING (user_id = auth.uid());

-- Admin users can manage all email integrations
CREATE POLICY "Admins can manage all email integrations" ON email_integrations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- RLS policies for email templates
CREATE POLICY "Users can view their own email templates" ON email_templates
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create email templates" ON email_templates
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own email templates" ON email_templates
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own email templates" ON email_templates
  FOR DELETE USING (user_id = auth.uid());

-- Admin users can manage all email templates
CREATE POLICY "Admins can manage all email templates" ON email_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create trigger to automatically update updated_at for email integrations
CREATE TRIGGER update_email_integrations_updated_at
  BEFORE UPDATE ON email_integrations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to automatically update updated_at for email templates
CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON email_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON workflows TO authenticated;
GRANT ALL ON workflow_executions TO authenticated;
GRANT ALL ON email_integrations TO authenticated;
GRANT ALL ON email_templates TO authenticated;
GRANT EXECUTE ON FUNCTION log_workflow_execution TO authenticated;
