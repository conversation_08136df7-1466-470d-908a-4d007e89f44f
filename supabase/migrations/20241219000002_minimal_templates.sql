-- Minimal migration to add template support only
-- This only adds what's missing without touching existing policies

-- Add is_template column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workflows' AND column_name = 'is_template'
    ) THEN
        ALTER TABLE workflows ADD COLUMN is_template BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Update existing workflows to not be templates
UPDATE workflows SET is_template = false WHERE is_template IS NULL;

-- Create index for templates if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_workflows_is_template ON workflows(is_template);

-- Create email integrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  provider TEXT NOT NULL CHECK (provider IN ('gmail', 'outlook', 'resend', 'postmark', 'sendgrid', 'mailgun', 'smtp')),
  config J<PERSON><PERSON><PERSON> NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email templates table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  integration_id UUID REFERENCES email_integrations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  variables JSONB DEFAULT '[]',
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email tables
CREATE INDEX IF NOT EXISTS idx_email_integrations_user_id ON email_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_email_integrations_active ON email_integrations(is_active);
CREATE INDEX IF NOT EXISTS idx_email_templates_user_id ON email_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_integration_id ON email_templates(integration_id);

-- Enable RLS for email tables only if they don't already have it
DO $$
BEGIN
    -- Check if RLS is already enabled for email_integrations
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'email_integrations' 
        AND n.nspname = 'public' 
        AND c.relrowsecurity = true
    ) THEN
        ALTER TABLE email_integrations ENABLE ROW LEVEL SECURITY;
    END IF;
    
    -- Check if RLS is already enabled for email_templates
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'email_templates' 
        AND n.nspname = 'public' 
        AND c.relrowsecurity = true
    ) THEN
        ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Only create email policies if they don't exist
DO $$ 
BEGIN
    -- Email integrations policies
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_integrations' 
        AND policyname = 'Users can view their own email integrations'
    ) THEN
        CREATE POLICY "Users can view their own email integrations" ON email_integrations
          FOR SELECT USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_integrations' 
        AND policyname = 'Users can create email integrations'
    ) THEN
        CREATE POLICY "Users can create email integrations" ON email_integrations
          FOR INSERT WITH CHECK (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_integrations' 
        AND policyname = 'Users can update their own email integrations'
    ) THEN
        CREATE POLICY "Users can update their own email integrations" ON email_integrations
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_integrations' 
        AND policyname = 'Users can delete their own email integrations'
    ) THEN
        CREATE POLICY "Users can delete their own email integrations" ON email_integrations
          FOR DELETE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_integrations' 
        AND policyname = 'Admins can manage all email integrations'
    ) THEN
        CREATE POLICY "Admins can manage all email integrations" ON email_integrations
          FOR ALL USING (
            EXISTS (
              SELECT 1 FROM profiles
              WHERE profiles.id = auth.uid()
              AND profiles.role IN ('owner', 'super_admin', 'admin')
            )
          );
    END IF;
    
    -- Email templates policies
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_templates' 
        AND policyname = 'Users can view their own email templates'
    ) THEN
        CREATE POLICY "Users can view their own email templates" ON email_templates
          FOR SELECT USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_templates' 
        AND policyname = 'Users can create email templates'
    ) THEN
        CREATE POLICY "Users can create email templates" ON email_templates
          FOR INSERT WITH CHECK (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_templates' 
        AND policyname = 'Users can update their own email templates'
    ) THEN
        CREATE POLICY "Users can update their own email templates" ON email_templates
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_templates' 
        AND policyname = 'Users can delete their own email templates'
    ) THEN
        CREATE POLICY "Users can delete their own email templates" ON email_templates
          FOR DELETE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'email_templates' 
        AND policyname = 'Admins can manage all email templates'
    ) THEN
        CREATE POLICY "Admins can manage all email templates" ON email_templates
          FOR ALL USING (
            EXISTS (
              SELECT 1 FROM profiles
              WHERE profiles.id = auth.uid()
              AND profiles.role IN ('owner', 'super_admin', 'admin')
            )
          );
    END IF;
END $$;

-- Create triggers only if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_email_integrations_updated_at'
    ) THEN
        CREATE TRIGGER update_email_integrations_updated_at
          BEFORE UPDATE ON email_integrations
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_email_templates_updated_at'
    ) THEN
        CREATE TRIGGER update_email_templates_updated_at
          BEFORE UPDATE ON email_templates
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Grant permissions safely
DO $$
BEGIN
    GRANT USAGE ON SCHEMA public TO authenticated;
    GRANT ALL ON email_integrations TO authenticated;
    GRANT ALL ON email_templates TO authenticated;
EXCEPTION WHEN OTHERS THEN
    -- Ignore permission errors if they already exist
    NULL;
END $$;

-- =====================================================
-- WORKFLOW TRIGGER SYSTEM
-- =====================================================

-- Function to trigger workflows when quotes are created
CREATE OR REPLACE FUNCTION trigger_workflows_on_quote()
RETURNS TRIGGER AS $$
DECLARE
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id UUID;
BEGIN
    -- Create trigger data from the new quote
    trigger_data := jsonb_build_object(
        'quote', row_to_json(NEW),
        'contact', jsonb_build_object(
            'id', NEW.id,
            'name', NEW.name,
            'email', NEW.email,
            'company', NEW.company,
            'phone', NEW.phone
        ),
        'event', 'quote.created',
        'timestamp', NOW()
    );

    -- Find all active workflows with quote.created triggers
    FOR workflow_record IN
        SELECT w.id, w.name, w.nodes
        FROM workflows w
        WHERE w.active = true
        AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(w.nodes) AS node
            WHERE node->>'type' = 'trigger'
            AND node->'data'->>'triggerType' = 'new_quote'
        )
    LOOP
        -- Log workflow execution as pending
        INSERT INTO workflow_executions (
            workflow_id,
            workflow_name,
            trigger_data,
            execution_time_ms,
            executed_at,
            status
        ) VALUES (
            workflow_record.id,
            workflow_record.name,
            trigger_data,
            0,
            NOW(),
            'pending'
        ) RETURNING id INTO execution_id;

        -- Call the workflow execution function asynchronously
        -- Note: Using hardcoded URL since current_setting requires special permissions
        PERFORM net.http_post(
            url := 'https://kwilluhxhthdrqomkecn.supabase.co/functions/v1/execute-workflow',
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt3aWxsdWh4aHRoZHJxb21rZWNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMjE3MTksImV4cCI6MjA2NDU5NzcxOX0.89VHFRkJS7fIGMIX7m4MFQ2P5e1Vrwax4L3auo6HYsk'
            ),
            body := jsonb_build_object(
                'workflow_id', workflow_record.id,
                'execution_id', execution_id,
                'trigger_data', trigger_data
            )
        );
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for workflow execution on quote creation
DROP TRIGGER IF EXISTS trigger_workflows_on_quote_created ON quote_requests;
CREATE TRIGGER trigger_workflows_on_quote_created
    AFTER INSERT ON quote_requests
    FOR EACH ROW
    EXECUTE FUNCTION trigger_workflows_on_quote();

-- Function to manually trigger workflows (for testing)
CREATE OR REPLACE FUNCTION manual_trigger_workflows(quote_id UUID)
RETURNS TEXT AS $$
DECLARE
    quote_record RECORD;
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id UUID;
    result_text TEXT := '';
BEGIN
    -- Get the quote data
    SELECT * INTO quote_record FROM quote_requests WHERE id = quote_id;

    IF NOT FOUND THEN
        RETURN 'Quote not found';
    END IF;

    -- Create trigger data
    trigger_data := jsonb_build_object(
        'quote', row_to_json(quote_record),
        'contact', jsonb_build_object(
            'id', quote_record.id,
            'name', quote_record.name,
            'email', quote_record.email,
            'company', quote_record.company,
            'phone', quote_record.phone
        ),
        'event', 'quote.created',
        'timestamp', NOW()
    );

    -- Find and trigger workflows
    FOR workflow_record IN
        SELECT w.id, w.name, w.nodes
        FROM workflows w
        WHERE w.active = true
        AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(w.nodes) AS node
            WHERE node->>'type' = 'trigger'
            AND node->'data'->>'triggerType' = 'new_quote'
        )
    LOOP
        -- Log workflow execution as pending
        INSERT INTO workflow_executions (
            workflow_id,
            workflow_name,
            trigger_data,
            execution_time_ms,
            executed_at,
            status
        ) VALUES (
            workflow_record.id,
            workflow_record.name,
            trigger_data,
            0,
            NOW(),
            'pending'
        ) RETURNING id INTO execution_id;

        result_text := result_text || 'Triggered workflow: ' || workflow_record.name || ' (ID: ' || execution_id || ') ';
    END LOOP;

    IF result_text = '' THEN
        RETURN 'No active workflows found with quote.created triggers';
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
