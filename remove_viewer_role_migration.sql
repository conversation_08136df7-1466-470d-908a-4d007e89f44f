-- Remove Viewer Role Migration
-- Run this in Supabase Dashboard > SQL Editor
-- This removes the viewer role and updates all existing viewers to editors

-- 1. First, drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- 2. Update any existing 'viewer' roles to 'editor' (since we're removing viewer role)
UPDATE profiles
SET role = 'editor'
WHERE role = 'viewer';

-- 3. Update <PERSON>'s role to owner (ensure it's set correctly)
UPDATE profiles
SET role = 'owner'
WHERE email = '<EMAIL>';

-- 4. Add the new constraint without viewer role
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('owner', 'super_admin', 'admin', 'editor'));

-- 5. Drop ALL existing blog post policies to avoid conflicts
DROP POLICY IF EXISTS "Users can create posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Anyone can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can view their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Authenticated users can create posts" ON blog_posts;
DROP POLICY IF EXISTS "Authors can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Authors can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Published posts are viewable by everyone" ON blog_posts;

-- 6. Create comprehensive blog post policies with proper role hierarchy

-- Policy 1: Anyone can view published posts
CREATE POLICY "Anyone can view published posts" ON blog_posts
  FOR SELECT USING (status = 'published');

-- Policy 2: Users can view their own posts + Admins can view all posts
CREATE POLICY "Users can view their own posts" ON blog_posts
  FOR SELECT USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 3: Users with editor+ role can create posts
CREATE POLICY "Users can create posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('editor', 'admin', 'super_admin', 'owner')
    )
  );

-- Policy 4: Users can update their own posts + Admins can update any post
CREATE POLICY "Users can update their own posts" ON blog_posts
  FOR UPDATE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 5: Users can delete their own posts + Admins can delete any post
CREATE POLICY "Users can delete their own posts" ON blog_posts
  FOR DELETE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- 7. Verify the changes
SELECT
    'Updated Profiles' as check_type,
    email,
    full_name,
    role,
    created_at
FROM profiles
ORDER BY
    CASE role
        WHEN 'owner' THEN 1
        WHEN 'super_admin' THEN 2
        WHEN 'admin' THEN 3
        WHEN 'editor' THEN 4
    END,
    created_at;

-- 8. Verify blog post policies are correctly set
SELECT
    'Blog Post Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd,
    qual
FROM pg_policies
WHERE tablename = 'blog_posts'
ORDER BY policyname;
