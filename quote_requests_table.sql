-- Quote Requests Table
-- Run this in Supabase Dashboard > SQL Editor

-- Create quote_requests table
CREATE TABLE IF NOT EXISTS quote_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Project Discovery
  project_type TEXT NOT NULL,
  industry TEXT NOT NULL,
  target_audience TEXT,
  
  -- Requirements
  key_features TEXT[] DEFAULT '{}',
  integrations TEXT[] DEFAULT '{}',
  design_preference TEXT,
  
  -- Timeline & Budget
  timeline TEXT,
  budget TEXT,
  team_involvement TEXT,
  
  -- Contact Information
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  company TEXT,
  phone TEXT,
  best_time_to_call TEXT,
  communication_preference TEXT,
  additional_info TEXT,
  
  -- Metadata
  status TEXT DEFAULT 'new' CHECK (status IN ('new', 'reviewed', 'quoted', 'converted', 'declined')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Account creation
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  wants_account BOOLEAN DEFAULT FALSE
);

-- Enable RLS (Row Level Security)
ALTER TABLE quote_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for quote_requests
-- Allow anyone to insert (for public quote requests)
CREATE POLICY "Anyone can submit quote requests" ON quote_requests
  FOR INSERT WITH CHECK (true);

-- Allow users to view their own quote requests
CREATE POLICY "Users can view their own quote requests" ON quote_requests
  FOR SELECT USING (
    auth.uid() = user_id OR 
    auth.email() = email
  );

-- Allow admins to view all quote requests
CREATE POLICY "Admins can view all quote requests" ON quote_requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Allow admins to update quote requests
CREATE POLICY "Admins can update quote requests" ON quote_requests
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_quote_requests_updated_at 
  BEFORE UPDATE ON quote_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quote_requests_status ON quote_requests(status);
CREATE INDEX IF NOT EXISTS idx_quote_requests_created_at ON quote_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_quote_requests_email ON quote_requests(email);
CREATE INDEX IF NOT EXISTS idx_quote_requests_user_id ON quote_requests(user_id);

-- Insert some sample data (optional - remove in production)
-- INSERT INTO quote_requests (
--   project_type, industry, target_audience, name, email, 
--   key_features, timeline, budget, status
-- ) VALUES (
--   'SaaS Platform', 'Technology', 'Small businesses', 
--   'John Doe', '<EMAIL>',
--   ARRAY['User Authentication', 'Payment Processing', 'Admin Dashboard'],
--   '3-4 months', '$50,000 - $100,000', 'new'
-- );
