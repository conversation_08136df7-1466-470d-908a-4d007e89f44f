-- Simple Organization Fix for New Users
-- This creates a basic organization setup for users who don't have one

-- 1. Create a simple organization for the current user (replace with actual user email)
-- First, let's see what users exist without organizations
SELECT 'Current users without organizations:' as info;
SELECT 
  p.id,
  p.email,
  p.full_name,
  p.role
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id
)
ORDER BY p.created_at;

-- 2. Create organizations table if it doesn't exist (basic version)
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  subscription_plan TEXT DEFAULT 'free',
  workflow_credits_used INTEGER DEFAULT 0,
  workflow_credits_limit INTEGER DEFAULT 100,
  ai_credits_used INTEGER DEFAULT 0,
  ai_credits_limit INTEGER DEFAULT 50,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create organization_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'owner',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  UNIQUE(organization_id, user_id)
);

-- 4. Create personal organizations for users without one
DO $$
DECLARE
  user_record RECORD;
  org_id UUID;
BEGIN
  FOR user_record IN 
    SELECT p.* FROM profiles p
    WHERE NOT EXISTS (
      SELECT 1 FROM organization_members om 
      WHERE om.user_id = p.id
    )
  LOOP
    -- Create organization
    INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
    VALUES (
      COALESCE(user_record.full_name, user_record.email) || '''s Workspace',
      'user-' || user_record.id,
      'free',
      100,  -- Free tier: 100 workflow credits
      50    -- Free tier: 50 AI credits
    )
    ON CONFLICT (slug) DO NOTHING
    RETURNING id INTO org_id;
    
    -- Get org ID if it already existed
    IF org_id IS NULL THEN
      SELECT id INTO org_id FROM organizations WHERE slug = 'user-' || user_record.id;
    END IF;
    
    -- Add user as owner
    INSERT INTO organization_members (organization_id, user_id, role, joined_at)
    VALUES (org_id, user_record.id, 'owner', NOW())
    ON CONFLICT (organization_id, user_id) DO NOTHING;
    
    RAISE NOTICE 'Created organization for user: %', user_record.email;
  END LOOP;
END $$;

-- 5. Enable RLS if not already enabled
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;

-- 6. Create basic RLS policies
DROP POLICY IF EXISTS "Users can view their organizations" ON organizations;
CREATE POLICY "Users can view their organizations" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

DROP POLICY IF EXISTS "Users can view their memberships" ON organization_members;
CREATE POLICY "Users can view their memberships" ON organization_members
  FOR SELECT USING (user_id = auth.uid());

-- 7. Show results
SELECT 'Organizations created:' as info;
SELECT 
  o.name,
  o.slug,
  o.subscription_plan,
  o.workflow_credits_limit,
  o.ai_credits_limit,
  p.email as owner_email
FROM organizations o
JOIN organization_members om ON om.organization_id = o.id
JOIN profiles p ON p.id = om.user_id
WHERE o.slug LIKE 'user-%'
ORDER BY o.created_at;

SELECT 'Setup complete! Users should now see their organization dropdown.' as status;
