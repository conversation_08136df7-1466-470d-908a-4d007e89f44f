// Webhook endpoint for new quote creation
// This endpoint can be called by GHL automation or other external services

export default async function handler(req, res) {
  // Set CORS headers for external access
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }
  
  try {
    // Get the webhook payload
    const payload = req.body
    
    // Validate required fields
    if (!payload.data || !payload.data.quote_id) {
      return res.status(400).json({ 
        error: 'Invalid payload', 
        message: 'Missing required quote data' 
      })
    }
    
    // Log the webhook call
    console.log('New quote webhook called:', {
      event: payload.event,
      quote_id: payload.data.quote_id,
      timestamp: payload.timestamp
    })
    
    // Here you can add additional processing:
    // - Send to external CRM
    // - Trigger email notifications  
    // - Update analytics
    // - Call other webhooks
    
    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      quote_id: payload.data.quote_id,
      processed_at: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Webhook processing error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    })
  }
}
