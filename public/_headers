# Security headers for Vercel deployment
/*
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://calendar.aha-innovations.com https://challenges.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; media-src 'self' https:; connect-src 'self' https://kwilluhxhthdrqomkecn.supabase.co wss://kwilluhxhthdrqomkecn.supabase.co; frame-src 'self' https://calendar.aha-innovations.com https://challenges.cloudflare.com; object-src 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests;
  
  # Security headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=()
  
  # HSTS (HTTP Strict Transport Security)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  
  # Cache control for security
  Cache-Control: public, max-age=31536000, immutable

# API routes - more restrictive
/api/*
  Content-Security-Policy: default-src 'none'; script-src 'none'; style-src 'none'; img-src 'none'; connect-src 'self'; frame-src 'none'; object-src 'none'; base-uri 'none'; form-action 'none';
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  
# Static assets - longer cache
/assets/*
  Cache-Control: public, max-age=31536000, immutable
  
# Images - optimized caching
*.jpg
  Cache-Control: public, max-age=31536000, immutable
*.jpeg
  Cache-Control: public, max-age=31536000, immutable
*.png
  Cache-Control: public, max-age=31536000, immutable
*.webp
  Cache-Control: public, max-age=31536000, immutable
*.svg
  Cache-Control: public, max-age=31536000, immutable
*.gif
  Cache-Control: public, max-age=31536000, immutable
