-- Debug blog loading issues
-- Run this to see what's happening with your blog posts

-- 1. Check if blog posts exist at all
SELECT 
    'Total Blog Posts in Database' as check_type,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE status = 'published') as published,
    COUNT(*) FILTER (WHERE status = 'draft') as drafts
FROM blog_posts;

-- 2. Check organization assignments
SELECT 
    'Organization Assignment Status' as check_type,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE organization_id IS NOT NULL) as with_org,
    COUNT(*) FILTER (WHERE organization_id IS NULL) as without_org
FROM blog_posts;

-- 3. Check submission status
SELECT 
    'Submission Status Distribution' as check_type,
    submission_status,
    COUNT(*) as count
FROM blog_posts
GROUP BY submission_status;

-- 4. Check current user's organization membership (replace with your user ID)
-- First, let's see what organizations exist
SELECT 
    'Available Organizations' as check_type,
    id,
    name,
    slug
FROM organizations
ORDER BY created_at;

-- 5. Check if there are any active policies on blog_posts
SELECT 
    'Current RLS Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 6. Simple test - try to select posts without any user context
SELECT 
    'Raw Post Data (First 5)' as check_type,
    id,
    title,
    status,
    submission_status,
    organization_id,
    author_id,
    created_at
FROM blog_posts
ORDER BY created_at DESC
LIMIT 5;

-- 7. Check if RLS is enabled
SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'blog_posts';
