-- Feature Request System Database Schema
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Feature Request Boards (like "Feature Requests", "Integrations", etc.)
CREATE TABLE IF NOT EXISTS feature_request_boards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  slug VARCHAR(100) UNIQUE NOT NULL,
  color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color for board
  icon VARCHAR(50) DEFAULT 'lightbulb', -- Lucide icon name
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Feature Request Statuses (Planned, In Progress, Complete, etc.)
CREATE TABLE IF NOT EXISTS feature_request_statuses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON>HAR(50) NOT NULL,
  slug VARCHAR(50) UNIQUE NOT NULL,
  color VARCHAR(7) NOT NULL, -- Hex color
  icon VARCHAR(50) DEFAULT 'circle', -- Lucide icon name
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Feature Requests
CREATE TABLE IF NOT EXISTS feature_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES feature_request_boards(id) ON DELETE CASCADE,
  status_id UUID REFERENCES feature_request_statuses(id) ON DELETE SET NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  author_email TEXT, -- For guest submissions
  author_name TEXT, -- For guest submissions
  priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
  effort_estimate VARCHAR(20), -- small, medium, large, xl
  target_version VARCHAR(20), -- e.g., "2.0.0"
  upvotes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  is_public BOOLEAN DEFAULT TRUE,
  is_archived BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Feature Request Upvotes
CREATE TABLE IF NOT EXISTS feature_request_upvotes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  feature_request_id UUID NOT NULL REFERENCES feature_requests(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  voter_email TEXT, -- For guest votes
  voter_name TEXT, -- For guest votes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(feature_request_id, user_id), -- Prevent duplicate votes from users
  UNIQUE(feature_request_id, voter_email) -- Prevent duplicate votes from guests
);

-- 5. Feature Request Comments
CREATE TABLE IF NOT EXISTS feature_request_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  feature_request_id UUID NOT NULL REFERENCES feature_requests(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES feature_request_comments(id) ON DELETE CASCADE,
  author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  author_email TEXT, -- For guest comments
  author_name TEXT, -- For guest comments
  content TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT FALSE, -- Internal team comments
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default boards
INSERT INTO feature_request_boards (name, description, slug, color, icon, sort_order) VALUES
('Feature Requests', 'New features and enhancements for the platform', 'feature-requests', '#3B82F6', 'lightbulb', 1),
('Integrations', 'Third-party integrations and API connections', 'integrations', '#10B981', 'plug', 2),
('Bug Reports', 'Bug reports and issues that need fixing', 'bug-reports', '#EF4444', 'bug', 3),
('Improvements', 'General improvements and optimizations', 'improvements', '#8B5CF6', 'trending-up', 4)
ON CONFLICT (slug) DO NOTHING;

-- Insert default statuses
INSERT INTO feature_request_statuses (name, slug, color, icon, sort_order) VALUES
('Planned', 'planned', '#3B82F6', 'clock', 1),
('In Progress', 'in-progress', '#F59E0B', 'play-circle', 2),
('Complete', 'complete', '#10B981', 'check-circle', 3),
('On Hold', 'on-hold', '#6B7280', 'pause-circle', 4),
('Rejected', 'rejected', '#EF4444', 'x-circle', 5)
ON CONFLICT (slug) DO NOTHING;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_feature_requests_board_id ON feature_requests(board_id);
CREATE INDEX IF NOT EXISTS idx_feature_requests_status_id ON feature_requests(status_id);
CREATE INDEX IF NOT EXISTS idx_feature_requests_author_id ON feature_requests(author_id);
CREATE INDEX IF NOT EXISTS idx_feature_requests_created_at ON feature_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_feature_request_upvotes_request_id ON feature_request_upvotes(feature_request_id);
CREATE INDEX IF NOT EXISTS idx_feature_request_comments_request_id ON feature_request_comments(feature_request_id);

-- Enable RLS
ALTER TABLE feature_request_boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_request_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_request_upvotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_request_comments ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Boards: Everyone can view active boards
CREATE POLICY "Anyone can view active boards" ON feature_request_boards 
  FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage boards" ON feature_request_boards 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'admin')));

-- Statuses: Everyone can view active statuses
CREATE POLICY "Anyone can view active statuses" ON feature_request_statuses 
  FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage statuses" ON feature_request_statuses 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'admin')));

-- Feature Requests: Public ones are viewable by all
CREATE POLICY "Anyone can view public requests" ON feature_requests 
  FOR SELECT USING (is_public = true AND is_archived = false);
CREATE POLICY "Anyone can create requests" ON feature_requests 
  FOR INSERT WITH CHECK (true);
CREATE POLICY "Authors can update their requests" ON feature_requests 
  FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can manage all requests" ON feature_requests 
  FOR ALL USING (auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'admin')));

-- Upvotes: Anyone can vote
CREATE POLICY "Anyone can view upvotes" ON feature_request_upvotes 
  FOR SELECT USING (true);
CREATE POLICY "Anyone can create upvotes" ON feature_request_upvotes 
  FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can delete their upvotes" ON feature_request_upvotes 
  FOR DELETE USING (auth.uid() = user_id);

-- Comments: Anyone can view and create
CREATE POLICY "Anyone can view public comments" ON feature_request_comments 
  FOR SELECT USING (is_internal = false);
CREATE POLICY "Team can view internal comments" ON feature_request_comments 
  FOR SELECT USING (is_internal = true AND auth.uid() IN (SELECT id FROM profiles WHERE role IN ('owner', 'admin', 'editor')));
CREATE POLICY "Anyone can create comments" ON feature_request_comments 
  FOR INSERT WITH CHECK (true);
CREATE POLICY "Authors can update their comments" ON feature_request_comments 
  FOR UPDATE USING (auth.uid() = author_id);

-- Triggers to update counts
CREATE OR REPLACE FUNCTION update_feature_request_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update upvotes count
  UPDATE feature_requests 
  SET upvotes_count = (
    SELECT COUNT(*) FROM feature_request_upvotes 
    WHERE feature_request_id = COALESCE(NEW.feature_request_id, OLD.feature_request_id)
  )
  WHERE id = COALESCE(NEW.feature_request_id, OLD.feature_request_id);
  
  -- Update comments count
  UPDATE feature_requests 
  SET comments_count = (
    SELECT COUNT(*) FROM feature_request_comments 
    WHERE feature_request_id = COALESCE(NEW.feature_request_id, OLD.feature_request_id)
    AND is_internal = false
  )
  WHERE id = COALESCE(NEW.feature_request_id, OLD.feature_request_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS update_upvotes_count ON feature_request_upvotes;
CREATE TRIGGER update_upvotes_count
  AFTER INSERT OR DELETE ON feature_request_upvotes
  FOR EACH ROW EXECUTE FUNCTION update_feature_request_counts();

DROP TRIGGER IF EXISTS update_comments_count ON feature_request_comments;
CREATE TRIGGER update_comments_count
  AFTER INSERT OR DELETE ON feature_request_comments
  FOR EACH ROW EXECUTE FUNCTION update_feature_request_counts();
