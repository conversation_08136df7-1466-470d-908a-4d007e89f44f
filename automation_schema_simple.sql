-- Simple Automation Schema for Supabase
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Create automation_rules table
CREATE TABLE IF NOT EXISTS automation_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  active BOOLEAN DEFAULT true,
  trigger_type TEXT NOT NULL,
  trigger_config JSON<PERSON> DEFAULT '{}',
  conditions JSONB DEFAULT '[]',
  actions JSONB DEFAULT '[]',
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_executed TIMESTAMP WITH TIME ZONE,
  execution_count INTEGER DEFAULT 0,
  last_error TEXT,
  error_count INTEGER DEFAULT 0
);

-- 2. Create webhook_templates table
CREATE TABLE IF NOT EXISTS webhook_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT DEFAULT 'custom',
  template_body TEXT NOT NULL,
  sample_data JSONB,
  default_method TEXT DEFAULT 'POST',
  default_headers JSONB DEFAULT '{}',
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  is_public BOOLEAN DEFAULT false
);

-- 3. Create automation_logs table
CREATE TABLE IF NOT EXISTS automation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  rule_id UUID REFERENCES automation_rules(id) ON DELETE CASCADE,
  quote_id UUID REFERENCES quote_requests(id) ON DELETE SET NULL,
  trigger_type TEXT NOT NULL,
  action_type TEXT NOT NULL,
  action_config JSONB,
  rendered_payload TEXT,
  request_url TEXT,
  request_method TEXT,
  request_headers JSONB,
  response_status INTEGER,
  response_body TEXT,
  response_headers JSONB,
  status TEXT NOT NULL DEFAULT 'pending',
  error_message TEXT,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  duration_ms INTEGER,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  next_retry_at TIMESTAMP WITH TIME ZONE
);

-- 4. Create automation_queue table
CREATE TABLE IF NOT EXISTS automation_queue (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  trigger_type TEXT NOT NULL,
  quote_id UUID REFERENCES quote_requests(id) ON DELETE CASCADE,
  old_data JSONB,
  new_data JSONB NOT NULL,
  status TEXT DEFAULT 'pending',
  processed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Enable Row Level Security
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_queue ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS Policies
CREATE POLICY "Admins can manage automation rules" ON automation_rules
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

CREATE POLICY "Users can view public templates" ON webhook_templates
  FOR SELECT USING (is_public = true OR created_by = auth.uid());

CREATE POLICY "Users can manage their own templates" ON webhook_templates
  FOR ALL USING (created_by = auth.uid());

CREATE POLICY "Admins can manage all templates" ON webhook_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

CREATE POLICY "Admins can view automation logs" ON automation_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

CREATE POLICY "System can manage automation queue" ON automation_queue
  FOR ALL USING (true);

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_automation_rules_active ON automation_rules(active);
CREATE INDEX IF NOT EXISTS idx_automation_rules_trigger_type ON automation_rules(trigger_type);
CREATE INDEX IF NOT EXISTS idx_automation_rules_created_by ON automation_rules(created_by);

CREATE INDEX IF NOT EXISTS idx_webhook_templates_category ON webhook_templates(category);
CREATE INDEX IF NOT EXISTS idx_webhook_templates_public ON webhook_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_webhook_templates_created_by ON webhook_templates(created_by);

CREATE INDEX IF NOT EXISTS idx_automation_logs_rule_id ON automation_logs(rule_id);
CREATE INDEX IF NOT EXISTS idx_automation_logs_quote_id ON automation_logs(quote_id);
CREATE INDEX IF NOT EXISTS idx_automation_logs_status ON automation_logs(status);
CREATE INDEX IF NOT EXISTS idx_automation_logs_started_at ON automation_logs(started_at);

CREATE INDEX IF NOT EXISTS idx_automation_queue_status ON automation_queue(status);
CREATE INDEX IF NOT EXISTS idx_automation_queue_scheduled_for ON automation_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_automation_queue_quote_id ON automation_queue(quote_id);

-- 8. Create update timestamp function and triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_automation_rules_updated_at 
  BEFORE UPDATE ON automation_rules 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webhook_templates_updated_at 
  BEFORE UPDATE ON webhook_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Create automation trigger for quote changes
CREATE OR REPLACE FUNCTION trigger_quote_automation()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO automation_queue (
    trigger_type, 
    quote_id, 
    old_data, 
    new_data
  ) VALUES (
    CASE 
      WHEN TG_OP = 'INSERT' THEN 'new_quote'
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN 'status_change'
      ELSE 'quote_update'
    END,
    NEW.id, 
    CASE WHEN TG_OP = 'UPDATE' THEN row_to_json(OLD) ELSE NULL END,
    row_to_json(NEW)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Attach trigger to quote_requests table
DROP TRIGGER IF EXISTS quote_automation_trigger ON quote_requests;
CREATE TRIGGER quote_automation_trigger
  AFTER INSERT OR UPDATE ON quote_requests
  FOR EACH ROW EXECUTE FUNCTION trigger_quote_automation();

-- 10. Insert sample webhook templates
INSERT INTO webhook_templates (name, description, category, template_body, is_public) VALUES
(
  'Slack New Quote Alert',
  'Send a formatted message to Slack when a new quote is received',
  'slack',
  '{"text": "🎯 New quote from {{ quote.name }}", "blocks": [{"type": "section", "text": {"type": "mrkdwn", "text": "*Project:* {{ quote.project_type }}\\n*Industry:* {{ quote.industry }}\\n*Timeline:* {{ quote.timeline }}\\n*Email:* {{ quote.email }}"}}]}',
  true
),
(
  'GoHighLevel Lead Sync',
  'Sync new quotes to GoHighLevel CRM',
  'ghl',
  '{"contact": {"name": "{{ quote.name }}", "email": "{{ quote.email }}", "phone": "{{ quote.phone }}", "company": "{{ quote.company }}"}, "opportunity": {"title": "{{ quote.project_type }} - {{ quote.company }}", "stage": "quote_requested", "source": "Website Quote Form"}}',
  true
);

-- 11. Insert sample automation rule
INSERT INTO automation_rules (name, description, trigger_type, conditions, actions) VALUES
(
  'High-Value Lead Alert',
  'Send Slack notification for quotes over $50,000',
  'new_quote',
  '[{"field": "budget", "operator": "contains", "value": "$50,000"}]',
  '[{"type": "webhook", "name": "Slack Alert", "config": {"url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "method": "POST", "template_id": "slack-template-id"}}]'
);

-- Setup complete! Your automation system is ready.
