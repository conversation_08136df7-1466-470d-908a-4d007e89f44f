# AI Smart Credit System & Multi-Model Setup

## 🎯 Overview
Your AI Assistant now features a smart credit system that only charges for premium content generation, plus support for multiple AI models including the new Qwen Plus model.

## 🆓 Free vs Premium Features

### **Free (No Credits Used)**
- ✅ Support questions and platform help
- ✅ General queries about features, pricing, settings
- ✅ Navigation assistance
- ✅ How-to questions
- ✅ Troubleshooting help

**Keywords that trigger free mode:**
- "how do i", "how to", "what is", "where is"
- "pricing", "plan", "feature", "integration"
- "help", "support", "contact", "issue", "problem"

### **Premium (1 Credit Used)**
- 💎 Blog post generation and content creation
- 💎 Workflow template creation (Zapier-style automations)
- 💎 Custom automation building

**Keywords that trigger credit usage:**
- Blog: "write a blog", "create blog", "blog post", "article about"
- Workflows: "create workflow", "automation template", "zapier template"

## 🤖 Multi-Model AI System

### **Model Selection Logic**
The system automatically chooses the best model based on your request:

#### **Gemini Flash** (Google AI)
- **Use Case:** Support questions, general chat
- **Cost:** Free (no credits charged)
- **Speed:** Very fast
- **Best For:** Quick answers, platform help

#### **Qwen 72B (Free)** (OpenRouter)
- **Use Case:** Blog generation, content creation
- **Cost:** 1 credit per request (but FREE API calls)
- **Speed:** Fast
- **Best For:** High-quality blog posts, creative writing
- **Model ID:** `qwen/qwen-2.5-72b-instruct:free`

#### **DeepSeek R1 (Free)** (OpenRouter)
- **Use Case:** Complex workflow creation, reasoning
- **Cost:** 1 credit per request (but FREE API calls)
- **Speed:** Fast
- **Best For:** Complex automations, technical workflows
- **Model ID:** `deepseek/deepseek-r1:free`

### **Auto-Selection Examples**
```
"How do I create a blog post?" → Gemini Flash (Free)
"Write a blog about AI trends" → Qwen Plus (1 Credit)
"Create a workflow for email automation" → DeepSeek R1 (1 Credit)
"What are my subscription limits?" → Gemini Flash (Free)
```

## 🔧 Technical Implementation

### **Backend Changes**
- Added `isContentGenerationRequest()` function for smart detection
- Updated model selection logic with Qwen Plus support
- Implemented conditional credit charging
- Enhanced analytics tracking

### **Frontend Changes**
- Updated model name mapping
- Added credit system information display
- Enhanced responsive design
- Smart credit warnings

### **API Configuration**
```typescript
interface ChatRequest {
  model?: 'deepseek-r1' | 'gemini-flash' | 'qwen-plus' | 'auto'
  messageType?: 'chat_help' | 'blog_generation' | 'workflow_help'
}
```

## 📊 Credit Usage Analytics

The system now tracks:
- Which requests consumed credits vs free usage
- Model performance and selection patterns
- User behavior patterns
- Cost optimization opportunities

## 🎨 User Experience

### **Visual Indicators**
- Model badges show which AI is being used
- Credit counters display remaining credits
- Smart warnings for low credit situations
- Clear messaging about free vs premium features

### **Smart Notifications**
- Users are informed when credits are charged
- Clear explanations of the credit system
- Helpful tips on maximizing free usage

## 🚀 Benefits

### **For Users**
- **Cost Savings:** Support questions are completely free
- **Quality:** Best model for each task type
- **Transparency:** Clear understanding of when credits are used
- **Flexibility:** Multiple AI models for different needs

### **For Platform**
- **Cost Control:** Reduced API costs for support queries
- **Better UX:** Faster responses for simple questions
- **Scalability:** Efficient resource allocation
- **Analytics:** Better understanding of usage patterns

## 🔮 Future Enhancements

### **Planned Features**
- Custom model preferences per user
- Bulk credit packages for heavy users
- Model performance analytics dashboard
- Advanced workflow templates

### **Potential Models**
- Claude 3.5 Sonnet for advanced reasoning
- GPT-4 Turbo for specialized tasks
- Local models for privacy-sensitive content

## 📝 Usage Examples

### **Free Support Questions**
```
User: "How do I upgrade my subscription plan?"
→ Gemini Flash (Free) → Instant helpful response

User: "What integrations do you support?"
→ Gemini Flash (Free) → Comprehensive integration list
```

### **Premium Content Generation**
```
User: "Write a blog post about digital marketing trends"
→ Qwen Plus (1 Credit) → High-quality, detailed blog post

User: "Create a workflow that sends welcome emails to new customers"
→ DeepSeek R1 (1 Credit) → Detailed automation template
```

## 🎯 Best Practices

### **For Users**
1. Ask support questions freely - they're always free
2. Be specific about content generation needs
3. Use the quick actions for common tasks
4. Monitor credit usage in the dashboard

### **For Admins**
1. Monitor model usage patterns
2. Adjust credit limits based on usage
3. Provide clear documentation about the system
4. Use analytics to optimize model selection

This smart credit system ensures users get the best AI experience while keeping costs manageable and providing clear value for premium features.
