-- Migration: Add author_display column to blog_posts table
-- This migration adds the author_display column to support author anonymity options

-- Add the author_display column to blog_posts table
ALTER TABLE blog_posts 
ADD COLUMN author_display TEXT DEFAULT 'real_name' 
CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team'));

-- Update existing posts to have the default value
UPDATE blog_posts 
SET author_display = 'real_name' 
WHERE author_display IS NULL;

-- Add comment to document the column
COMMENT ON COLUMN blog_posts.author_display IS 'Controls how the author name is displayed: real_name (use actual name), anonymous (show as Anonymous), mbi_team (show as MBI Team)';
