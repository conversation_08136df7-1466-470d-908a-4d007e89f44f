# AI Chat Implementation Setup Guide

## Overview
This guide will help you set up the AI chat functionality using OpenRouter's DeepSeek model. The implementation includes:

- Real AI responses using DeepSeek R1 model
- Credit usage tracking and limits
- Contextual assistance based on current page
- Error handling and user feedback
- Analytics and usage statistics

## Step 1: Get OpenRouter API Key

1. Go to [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for a free account
3. Navigate to [API Keys](https://openrouter.ai/keys)
4. Create a new API key
5. Copy the key (it starts with `sk-or-v1-...`)

## Step 2: Add Environment Variables

### In Supabase Dashboard:
1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add a new environment variable:
   - **Name**: `OPENROUTER_API_KEY`
   - **Value**: Your OpenRouter API key (e.g., `sk-or-v1-your-key-here`)
4. Save the changes

### In Your Local .env File:
Add this line to your `.env` file (not `.env.example`):
```
OPENROUTER_API_KEY=sk-or-v1-your-actual-key-here
```

## Step 3: Run Database Migrations

Execute these SQL files in your Supabase SQL editor:

1. **First, run the blog posts table fix** (if you haven't already):
   ```sql
   -- Run the contents of fix-blog-posts-table.sql
   ```

2. **Then, create the AI interactions table**:
   ```sql
   -- Run the contents of create-ai-interactions-table.sql
   ```

## Step 4: Deploy the Supabase Edge Function

Run this command in your terminal from the project root:

```bash
# Deploy the AI chat function
npx supabase functions deploy ai-chat
```

If you get an error, make sure you're logged in to Supabase:
```bash
npx supabase login
```

## Step 5: Test the Implementation

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Navigate to any admin page** (e.g., `/admin/blog`)

3. **Click the AI Assistant button** (floating purple button in bottom-right)

4. **Try asking questions like**:
   - "Help me write a blog post about AI"
   - "How do I create a workflow?"
   - "What are my credit limits?"

## Step 6: Verify Everything Works

### Check the AI Assistant:
- ✅ AI button appears on admin pages
- ✅ Chat window opens when clicked
- ✅ Shows credit counter in header
- ✅ Responds with real AI answers (not simulated)
- ✅ Shows loading state while thinking
- ✅ Displays error messages if something goes wrong

### Check Credit Usage:
- ✅ Credits decrease after each interaction
- ✅ Warning appears when credits are low
- ✅ Chat is disabled when credits are exhausted

### Check Database:
- ✅ `ai_interactions` table has new records after each chat
- ✅ `organizations` table shows updated `ai_credits_used`

## Troubleshooting

### "OpenRouter API key not configured" Error
- Make sure you added the `OPENROUTER_API_KEY` environment variable in Supabase
- Redeploy the edge function after adding the environment variable

### "Authentication required" Error
- Make sure you're logged in to the application
- Check that your session is valid

### "AI credits limit exceeded" Error
- This is expected behavior when credits are exhausted
- Increase the `ai_credits_limit` in the organizations table for testing

### Network Errors
- Check your internet connection
- Verify the Supabase URL is correct
- Check browser console for detailed error messages

## Testing with SQL

You can test credit limits by updating your organization:

```sql
-- Reset AI credits for testing
UPDATE organizations 
SET ai_credits_used = 0 
WHERE id = 'your-organization-id';

-- Set a low limit for testing
UPDATE organizations 
SET ai_credits_limit = 5 
WHERE id = 'your-organization-id';
```

## Cost Information

DeepSeek R1 via OpenRouter:
- **Input**: $0.14 per 1M tokens
- **Output**: $0.28 per 1M tokens
- **Typical chat**: ~500-1000 tokens = $0.0002-0.0004 per interaction
- **Very cost-effective** for chat assistance!

## Next Steps

Once everything is working:

1. **Monitor usage** in the `ai_interactions` table
2. **Adjust credit limits** based on your needs
3. **Customize the system prompt** in the edge function
4. **Add more AI features** like blog generation assistance
5. **Set up usage analytics** dashboard

## Files Modified/Created

- ✅ `supabase/functions/ai-chat/index.ts` - Edge function for AI requests
- ✅ `src/lib/aiService.ts` - Client-side AI service
- ✅ `src/components/AIAssistant.tsx` - Updated to use real AI
- ✅ `.env.example` - Added OpenRouter API key example
- ✅ `create-ai-interactions-table.sql` - Database table for tracking
- ✅ `AI_CHAT_SETUP_GUIDE.md` - This setup guide

The AI chat is now fully functional with real AI responses! 🎉
