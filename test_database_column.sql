-- Test if author_display column exists and check current data
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Check if the column exists
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
AND column_name = 'author_display';

-- 2. If column exists, check current blog posts data
SELECT id, title, author_id, author_display, created_at
FROM blog_posts 
ORDER BY created_at DESC 
LIMIT 5;

-- 3. Check the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
ORDER BY ordinal_position;
