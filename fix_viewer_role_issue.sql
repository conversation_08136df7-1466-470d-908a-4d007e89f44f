-- Fix Viewer Role Issue - Comprehensive Solution
-- Run this in Supabase Dashboard > SQL Editor
-- This removes the viewer role entirely and ensures all users have proper access

-- 1. First, let's see what we currently have
SELECT 'Current profiles data:' as info;
SELECT id, email, role, subscription_plan FROM profiles ORDER BY email;

-- 2. Drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- 3. Update all 'viewer' role users to 'user' role
-- This gives them access to workflows and email integrations with limited credits
UPDATE profiles
SET role = 'user'
WHERE role = 'viewer';

-- 4. Ensure Stephen has saas_owner role (as per the createOrUpdateProfile function)
UPDATE profiles 
SET role = 'saas_owner'
WHERE email = '<EMAIL>';

-- 5. Add the new constraint without viewer role
-- Based on the latest codebase, the valid roles should be:
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('user', 'editor', 'admin', 'owner', 'super_admin', 'saas_owner'));

-- 6. Ensure subscription_plan column exists and has proper values
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'free';

-- Update any NULL subscription plans
UPDATE profiles 
SET subscription_plan = 'free' 
WHERE subscription_plan IS NULL;

-- Add subscription plan constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_subscription_plan_check;
ALTER TABLE profiles ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise'));

-- 7. Verify the changes
SELECT 'Updated profiles data:' as info;
SELECT id, email, role, subscription_plan FROM profiles ORDER BY email;

-- 8. Show current constraints
SELECT 'Current constraints:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

-- 9. Check for any users without organizations (they should have personal workspaces)
SELECT 'Users without organizations:' as info;
SELECT p.email, p.role
FROM profiles p
LEFT JOIN organization_members om ON p.id = om.user_id AND om.role = 'owner'
WHERE om.user_id IS NULL 
AND p.email != '<EMAIL>';

-- 10. Fix Workflow Template Visibility Issue
-- Problem: Templates created by saas_owner are not visible to regular users
-- Solution: Add policy to allow all users to view workflow templates

SELECT 'Fixing workflow template visibility...' as status;

-- First, check current workflow template policies
SELECT
    'Current Workflow Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE tablename = 'workflows'
ORDER BY policyname;

-- Drop existing admin policy that doesn't include saas_owner
DROP POLICY IF EXISTS "Admins can manage all workflows" ON workflows;

-- Create updated admin policy that includes saas_owner
CREATE POLICY "Admins can manage all workflows" ON workflows
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin', 'saas_owner')
    )
  );

-- Add new policy: All authenticated users can view workflow templates
CREATE POLICY "All users can view workflow templates" ON workflows
  FOR SELECT USING (
    is_template = true AND auth.uid() IS NOT NULL
  );

-- Verify workflow template visibility
SELECT
    'Workflow Template Check' as check_type,
    COUNT(*) as total_workflows,
    COUNT(*) FILTER (WHERE is_template = true) as template_count,
    COUNT(*) FILTER (WHERE is_template = false) as regular_workflow_count
FROM workflows;

-- Show updated workflow policies
SELECT
    'Updated Workflow Policies' as check_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies
WHERE tablename = 'workflows'
ORDER BY policyname;

SELECT 'All issues fixed successfully!' as status;
