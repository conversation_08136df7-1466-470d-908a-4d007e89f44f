-- Instant Organization Fix
-- Run this in Supabase SQL Editor to fix the organization loading issue

-- First, let's check what user ID we're working with
SELECT id, email FROM auth.users WHERE email LIKE '%stephen%' OR email LIKE '%secia%';

-- Create organization for the specific user (replace with actual user ID if needed)
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit, workflow_credits_used, ai_credits_used)
VALUES (
  'Secia Lovino''s Workspace',
  'user-1010b0e4-e6fc-40ea-9b4f-ca47cc2b8ea',
  'free',
  100,
  50,
  0,
  0
)
ON CONFLICT (slug) DO NOTHING;

-- Add user as owner of their organization
INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
SELECT
  o.id,
  '1010b0e4-e6fc-40ea-9b4f-ca47cc2b8ea'::uuid,
  'owner',
  NOW(),
  true
FROM organizations o
WHERE o.slug = 'user-1010b0e4-e6fc-40ea-9b4f-ca47cc2b8ea'
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- Verify the fix
SELECT 'Organization setup completed!' as status;

SELECT 
  p.email,
  o.name as organization_name,
  o.slug,
  o.subscription_plan,
  o.workflow_credits_limit,
  o.ai_credits_limit,
  om.role
FROM profiles p
JOIN organization_members om ON om.user_id = p.id AND om.is_active = true
JOIN organizations o ON o.id = om.organization_id
WHERE p.id = '1010b0e4-e6fc-40ea-9b4f-ca47cc2b8ea';
