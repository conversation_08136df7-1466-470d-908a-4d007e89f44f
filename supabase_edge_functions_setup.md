# Supabase Edge Functions for Webhook Endpoints

## Setup Instructions

### 1. Create Edge Function for Quote Webhooks

```bash
# In your project root, create the edge function
npx supabase functions new quote-webhook
```

### 2. Edge Function Code (supabase/functions/quote-webhook/index.ts)

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { event, data } = await req.json()
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Process different webhook events
    switch (event) {
      case 'quote.created':
        return await handleNewQuote(data, supabase)
      case 'quote.updated':
        return await handleQuoteUpdate(data, supabase)
      case 'quote.status_changed':
        return await handleStatusChange(data, supabase)
      default:
        return new Response(
          JSON.stringify({ error: 'Unknown event type' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function handleNewQuote(data: any, supabase: any) {
  // Log the webhook call
  await supabase.from('webhook_logs').insert({
    event: 'quote.created',
    quote_id: data.quote_id,
    payload: data,
    processed_at: new Date().toISOString()
  })

  // Create GHL-friendly response
  const ghlPayload = {
    contact: {
      firstName: data.name.split(' ')[0],
      lastName: data.name.split(' ').slice(1).join(' ') || '',
      email: data.email,
      phone: data.phone || '',
      companyName: data.company || ''
    },
    customFields: {
      project_type: data.project_type,
      industry: data.industry,
      budget: data.budget,
      timeline: data.timeline,
      key_features: data.key_features?.join(', ') || '',
      wants_blog_account: data.wants_account ? 'Yes' : 'No',
      lead_source: 'Website Quote Form',
      quote_id: data.quote_id
    },
    tags: [
      'website-lead',
      `budget-${data.budget.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`,
      `industry-${data.industry.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`
    ]
  }

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Quote webhook processed',
      ghl_payload: ghlPayload,
      quote_id: data.quote_id,
      processed_at: new Date().toISOString()
    }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function handleQuoteUpdate(data: any, supabase: any) {
  // Similar processing for quote updates
  return new Response(
    JSON.stringify({
      success: true,
      message: 'Quote update processed',
      quote_id: data.quote_id
    }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function handleStatusChange(data: any, supabase: any) {
  // Process status changes
  return new Response(
    JSON.stringify({
      success: true,
      message: 'Status change processed',
      quote_id: data.quote_id,
      new_status: data.status
    }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}
```

### 3. Deploy the Edge Function

```bash
# Deploy to Supabase
npx supabase functions deploy quote-webhook
```

### 4. Your Webhook URLs

After deployment, you'll have these webhook endpoints:

```
https://your-project-id.supabase.co/functions/v1/quote-webhook
```

### 5. GHL Automation Setup

In GoHighLevel, create an automation that:

1. **Trigger**: HTTP POST webhook received
2. **Webhook URL**: `https://your-project-id.supabase.co/functions/v1/quote-webhook`
3. **Action**: Create/Update Contact with the webhook data
4. **Mapping**: Map the webhook fields to GHL contact fields

### 6. Test Payload

Send this test payload to your webhook:

```json
{
  "event": "quote.created",
  "timestamp": "2024-12-19T15:30:00Z",
  "data": {
    "quote_id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "company": "Acme Corp",
    "project_type": "SaaS Platform",
    "industry": "Technology",
    "budget": "$50,000 - $100,000",
    "timeline": "3-4 months",
    "key_features": ["User Authentication", "Payment Processing"],
    "wants_account": true,
    "status": "new"
  }
}
```

## Benefits of This Approach

✅ **GHL-Friendly**: Provides clean, structured data for GHL automation
✅ **Reliable**: Supabase Edge Functions are highly available
✅ **Secure**: Built-in authentication and CORS handling
✅ **Scalable**: Handles high webhook volumes
✅ **Flexible**: Easy to modify webhook responses
✅ **Logged**: All webhook calls are tracked in your database
