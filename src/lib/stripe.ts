import { loadStripe, Stripe } from '@stripe/stripe-js'
import { supabase } from './supabase'

// Initialize Stripe
let stripePromise: Promise<Stripe | null>

export const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY
    if (!publishableKey) {
      console.error('Missing Stripe publishable key')
      return null
    }
    stripePromise = loadStripe(publishableKey)
  }
  return stripePromise
}

// Stripe configuration
export const STRIPE_CONFIG = {
  publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
  secretKey: import.meta.env.STRIPE_SECRET_KEY,
  webhookSecret: import.meta.env.STRIPE_WEBHOOK_SECRET,
}

// Subscription plan configurations
export const STRIPE_PLANS = {
  basic: {
    priceId: 'price_1RgfpuL5UMPPQRhsR8m38Rrk', // Basic Monthly $5
    priceIdAnnual: 'price_1RgfqfL5UMPPQRhsBQOLQf0U', // Basic Annual $50
    name: 'Basic Plan',
    amount: 500, // $5.00 in cents
    amountAnnual: 5000, // $50.00 in cents
    interval: 'month',
    features: {
      blog_posts: 'unlimited',
      workflow_credits: 1000,
      ai_credits: 200,
      email_integrations: 3,
      storage: '5GB'
    }
  },
  pro: {
    priceId: 'price_1RgfrnL5UMPPQRhsSBk994eR', // Pro Monthly $8
    priceIdAnnual: 'price_1RgfsML5UMPPQRhsRtNMhjGn', // Pro Annual $80
    name: 'Pro Plan',
    amount: 800, // $8.00 in cents
    amountAnnual: 8000, // $80.00 in cents
    interval: 'month',
    features: {
      blog_posts: 'unlimited',
      workflow_credits: 3000,
      ai_credits: 500,
      email_integrations: 5,
      storage: '20GB'
    }
  },
  enterprise: {
    priceId: 'price_enterprise_monthly', // Replace with actual Stripe price ID
    name: 'Enterprise Plan',
    amount: 4900, // $49.00 in cents
    interval: 'month',
    features: {
      blog_posts: 'unlimited',
      workflow_credits: 25000,
      ai_credits: -1, // unlimited
      email_integrations: 'unlimited',
      storage: '1TB'
    }
  }
}

// Create checkout session
export const createCheckoutSession = async (
  priceId: string,
  customerId?: string,
  organizationId?: string
) => {
  try {
    const { data, error } = await supabase.functions.invoke('create-checkout-session', {
      body: {
        priceId,
        customerId,
        organizationId,
        successUrl: `${window.location.origin}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${window.location.origin}/pricing`
      }
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error creating checkout session:', error)
    throw error
  }
}

// Create customer portal session
export const createCustomerPortalSession = async (customerId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('create-portal-session', {
      body: {
        customerId,
        returnUrl: `${window.location.origin}/billing`
      }
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error creating portal session:', error)
    throw error
  }
}

// Get subscription details
export const getSubscriptionDetails = async (subscriptionId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('get-subscription', {
      body: { subscriptionId }
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error getting subscription details:', error)
    throw error
  }
}

// Cancel subscription
export const cancelSubscription = async (subscriptionId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('cancel-subscription', {
      body: { subscriptionId }
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error canceling subscription:', error)
    throw error
  }
}

// Update subscription
export const updateSubscription = async (subscriptionId: string, newPriceId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('update-subscription', {
      body: { subscriptionId, newPriceId }
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error updating subscription:', error)
    throw error
  }
}

// Utility function to format currency
export const formatCurrency = (amount: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount / 100)
}

// Get plan by price ID
export const getPlanByPriceId = (priceId: string) => {
  return Object.values(STRIPE_PLANS).find(plan => plan.priceId === priceId)
}

// Check if user has active subscription
export const hasActiveSubscription = (subscriptionStatus?: string) => {
  return subscriptionStatus === 'active' || subscriptionStatus === 'trialing'
}
