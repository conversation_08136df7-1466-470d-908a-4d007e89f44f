// Product Knowledge Base for AI Assistant
// This contains all the information about MBI's services, features, and platform

export const PRODUCT_KNOWLEDGE = {
  company: {
    name: "Millennial Business Innovations (MBI)",
    description: "A comprehensive business platform that helps entrepreneurs and businesses build, automate, and scale their operations.",
    mission: "Empowering the next generation of entrepreneurs with cutting-edge technology and innovative solutions.",
    website: "https://mbi-platform.com",
    contact: {
      email: "<EMAIL>",
      phone: "+****************"
    }
  },

  services: {
    mvp_development: {
      name: "MVP Development",
      description: "Rapid prototyping and minimum viable product development for startups and entrepreneurs.",
      features: [
        "Quick 2-4 week development cycles",
        "Modern tech stack (React, Node.js, Supabase)",
        "User authentication and basic admin panel",
        "Responsive design for all devices",
        "Basic analytics and user management"
      ],
      pricing: "Starting at $5,000",
      timeline: "2-4 weeks"
    },
    saas_platforms: {
      name: "SaaS Platform Development",
      description: "Full-featured Software as a Service platforms with subscription management, multi-tenancy, and advanced features.",
      features: [
        "Multi-tenant architecture",
        "Subscription and billing integration (Stripe)",
        "Advanced user roles and permissions",
        "API development and documentation",
        "Automated workflows and integrations",
        "Analytics and reporting dashboards",
        "Email automation and marketing tools"
      ],
      pricing: "Starting at $15,000",
      timeline: "6-12 weeks"
    },
    web_applications: {
      name: "Custom Web Applications",
      description: "Tailored web applications for specific business needs and workflows.",
      features: [
        "Custom business logic and workflows",
        "Database design and optimization",
        "Third-party integrations (CRM, ERP, etc.)",
        "Advanced reporting and analytics",
        "Mobile-responsive design",
        "SEO optimization"
      ],
      pricing: "Starting at $8,000",
      timeline: "4-8 weeks"
    },
    consulting: {
      name: "Technical Consulting",
      description: "Strategic technology consulting to help businesses make informed decisions about their tech stack and digital transformation.",
      features: [
        "Technology stack recommendations",
        "Architecture and scalability planning",
        "Code reviews and optimization",
        "Team training and mentorship",
        "Digital transformation strategy"
      ],
      pricing: "$150-250/hour",
      timeline: "Ongoing"
    }
  },

  platform_features: {
    blog_system: {
      name: "Blog Management System",
      description: "Comprehensive blog platform with rich text editing, SEO optimization, and social features.",
      features: [
        "Rich text editor with media support",
        "SEO optimization tools",
        "Comment system with moderation",
        "Social sharing integration",
        "Analytics and engagement tracking",
        "Multi-author support with roles"
      ]
    },
    workflow_automation: {
      name: "Workflow Automation",
      description: "Visual workflow builder for automating business processes and integrations.",
      features: [
        "Drag-and-drop workflow builder",
        "Trigger-based automation (webhooks, forms, time-based)",
        "Email automation and notifications",
        "Third-party integrations (Zapier, webhooks)",
        "Conditional logic and branching",
        "Execution history and monitoring"
      ]
    },
    email_integrations: {
      name: "Email Integrations",
      description: "Connect and manage multiple email providers for automated communications.",
      features: [
        "Multiple provider support (Gmail, Outlook, SendGrid, etc.)",
        "Template management",
        "Automated email sequences",
        "Delivery tracking and analytics",
        "SMTP and API-based sending",
        "Bounce and unsubscribe handling"
      ]
    },
    analytics: {
      name: "Analytics & Reporting",
      description: "Comprehensive analytics dashboard for tracking performance and user engagement.",
      features: [
        "Real-time visitor tracking",
        "Blog post performance metrics",
        "Workflow execution analytics",
        "User engagement insights",
        "Custom report generation",
        "Export capabilities"
      ]
    },
    user_management: {
      name: "User & Organization Management",
      description: "Multi-tenant user management with role-based access control.",
      features: [
        "Organization/workspace management",
        "Role-based permissions (Owner, Admin, Editor, Viewer)",
        "User invitations and onboarding",
        "Profile management",
        "Activity logging",
        "Bulk user operations"
      ]
    }
  },

  subscription_plans: {
    free: {
      name: "Free Plan",
      price: "$0/month",
      features: [
        "Unlimited blog posts",
        "Basic workflow automation (100 executions/month)",
        "50 AI credits/month",
        "Gmail integration",
        "Basic analytics",
        "Community support"
      ],
      limits: {
        blog_posts: "unlimited",
        workflow_credits: 100,
        ai_credits: 50,
        email_integrations: 1,
        storage: "1GB"
      }
    },
    basic: {
      name: "Basic Plan",
      price: "$5/month",
      features: [
        "Everything in Free",
        "Advanced workflows (1,000 executions/month)",
        "200 AI credits/month",
        "Advanced analytics",
        "Email support",
        "Priority processing"
      ],
      limits: {
        blog_posts: "unlimited",
        workflow_credits: 1000,
        ai_credits: 200,
        email_integrations: 3,
        storage: "5GB"
      }
    },
    pro: {
      name: "Pro Plan",
      price: "$8/month",
      features: [
        "Everything in Basic",
        "Priority workflows (3,000 executions/month)",
        "500 AI credits/month",
        "Advanced AI features",
        "Priority support",
        "Custom domains",
        "Team collaboration (3 users)"
      ],
      limits: {
        blog_posts: "unlimited",
        workflow_credits: 3000,
        ai_credits: 500,
        email_integrations: 5,
        storage: "20GB"
      }
    },
    enterprise: {
      name: "Enterprise Plan",
      price: "Custom pricing",
      features: [
        "Everything in Pro",
        "Unlimited everything",
        "Custom integrations",
        "Dedicated support",
        "SLA guarantees",
        "Custom development",
        "On-premise deployment options"
      ],
      limits: {
        blog_posts: "unlimited",
        workflow_credits: "unlimited",
        ai_credits: "unlimited",
        email_integrations: "unlimited",
        storage: "unlimited"
      }
    }
  },

  common_questions: {
    getting_started: [
      {
        q: "How do I create my first blog post?",
        a: "Go to Admin → Blog → New Post. Use the rich text editor to write your content, add images, and publish when ready."
      },
      {
        q: "How do I set up a workflow?",
        a: "Navigate to Admin → Automations → Create Workflow. Choose a trigger (form submit, webhook, etc.), add actions (send email, webhook call), and test before activating."
      },
      {
        q: "How do I connect my email provider?",
        a: "Go to Admin → Email Integrations → Add Integration. Choose your provider (Gmail, Outlook, etc.) and follow the setup wizard."
      }
    ],
    billing_credits: [
      {
        q: "What are workflow credits?",
        a: "Workflow credits are consumed each time a workflow executes. Each trigger activation uses 1 credit. You can see your usage in Admin → Credits."
      },
      {
        q: "What are AI credits?",
        a: "AI credits are used for AI assistant interactions and content generation. Each chat message or AI-generated content uses 1-3 credits depending on complexity."
      },
      {
        q: "How do I upgrade my plan?",
        a: "Go to Admin → System Settings → Subscription to view and upgrade your plan. Changes take effect immediately."
      }
    ],
    technical: [
      {
        q: "Can I use custom domains?",
        a: "Yes! Custom domains are available on Basic plan and above. Contact support for setup assistance."
      },
      {
        q: "Do you provide API access?",
        a: "API access is available on Pro and Enterprise plans. Documentation is available in your dashboard."
      },
      {
        q: "Is there a mobile app?",
        a: "The platform is fully responsive and works great on mobile browsers. Native apps are planned for the future."
      }
    ]
  },

  integrations: {
    supported: [
      "Zapier (webhook triggers and actions)",
      "Google Workspace (Gmail, Drive, Calendar)",
      "Microsoft 365 (Outlook, OneDrive)",
      "Stripe (payment processing)",
      "SendGrid (email delivery)",
      "Mailgun (email delivery)",
      "Resend (email delivery)",
      "Webhooks (custom integrations)",
      "REST APIs (custom connections)"
    ],
    coming_soon: [
      "Slack integration",
      "Discord webhooks",
      "HubSpot CRM",
      "Salesforce integration",
      "Shopify connector"
    ]
  }
}

// Helper function to search knowledge base
export function searchKnowledge(query: string): string[] {
  const results: string[] = []
  const searchTerm = query.toLowerCase()
  
  // Search through common questions
  Object.values(PRODUCT_KNOWLEDGE.common_questions).forEach(category => {
    category.forEach(qa => {
      if (qa.q.toLowerCase().includes(searchTerm) || qa.a.toLowerCase().includes(searchTerm)) {
        results.push(`Q: ${qa.q}\nA: ${qa.a}`)
      }
    })
  })
  
  return results
}
