import { EmailIntegration } from './supabase'

export interface EmailMessage {
  to: string
  subject: string
  body: string
  from?: string
}

export interface EmailSendResult {
  success: boolean
  messageId?: string
  error?: string
}

/**
 * Email service that handles sending emails through various providers
 */
export class EmailService {
  private integration: EmailIntegration

  constructor(integration: EmailIntegration) {
    this.integration = integration
  }

  /**
   * Send an email using the configured integration
   */
  async sendEmail(message: EmailMessage): Promise<EmailSendResult> {
    try {
      switch (this.integration.provider) {
        case 'gmail':
          return await this.sendGmailEmail(message)
        case 'outlook':
          return await this.sendOutlookEmail(message)
        case 'resend':
          return await this.sendResendEmail(message)
        case 'postmark':
          return await this.sendPostmarkEmail(message)
        case 'sendgrid':
          return await this.sendSendGridEmail(message)
        case 'mailgun':
          return await this.sendMailgunEmail(message)
        case 'smtp':
          return await this.sendSMTPEmail(message)
        default:
          return {
            success: false,
            error: `Unsupported email provider: ${this.integration.provider}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Test the email integration by sending a test email
   */
  async testIntegration(testEmail: string): Promise<EmailSendResult> {
    const testMessage: EmailMessage = {
      to: testEmail,
      subject: 'Test Email from MBI Workflow System',
      body: `
        <h2>Email Integration Test</h2>
        <p>This is a test email from your MBI workflow system.</p>
        <p><strong>Integration:</strong> ${this.integration.name}</p>
        <p><strong>Provider:</strong> ${this.integration.provider}</p>
        <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        <p>If you received this email, your integration is working correctly!</p>
      `
    }

    return await this.sendEmail(testMessage)
  }

  private async sendGmailEmail(message: EmailMessage): Promise<EmailSendResult> {
    // Gmail implementation using server-side SMTP
    return await this.sendServerSideEmail(message)
  }

  private async sendOutlookEmail(message: EmailMessage): Promise<EmailSendResult> {
    // Outlook implementation using server-side SMTP
    return await this.sendServerSideEmail(message)
  }

  private async sendResendEmail(message: EmailMessage): Promise<EmailSendResult> {
    // Resend implementation using server-side function
    return await this.sendServerSideEmail(message)
  }

  private async sendPostmarkEmail(message: EmailMessage): Promise<EmailSendResult> {
    // Postmark API implementation
    const config = this.integration.config
    
    try {
      const response = await fetch('https://api.postmarkapp.com/email', {
        method: 'POST',
        headers: {
          'X-Postmark-Server-Token': config.serverToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          From: config.fromEmail,
          To: message.to,
          Subject: message.subject,
          HtmlBody: message.body
        })
      })

      if (!response.ok) {
        const error = await response.text()
        return {
          success: false,
          error: `Postmark API error: ${error}`
        }
      }

      const result = await response.json()
      return {
        success: true,
        messageId: result.MessageID
      }
    } catch (error) {
      return {
        success: false,
        error: `Postmark error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private async sendSendGridEmail(message: EmailMessage): Promise<EmailSendResult> {
    // SendGrid API implementation
    const config = this.integration.config
    
    try {
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: message.to }]
          }],
          from: { email: config.fromEmail },
          subject: message.subject,
          content: [{
            type: 'text/html',
            value: message.body
          }]
        })
      })

      if (!response.ok) {
        const error = await response.text()
        return {
          success: false,
          error: `SendGrid API error: ${error}`
        }
      }

      return {
        success: true,
        messageId: `sendgrid_${Date.now()}`
      }
    } catch (error) {
      return {
        success: false,
        error: `SendGrid error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private async sendMailgunEmail(message: EmailMessage): Promise<EmailSendResult> {
    // Mailgun API implementation
    const config = this.integration.config
    
    try {
      const formData = new FormData()
      formData.append('from', config.fromEmail)
      formData.append('to', message.to)
      formData.append('subject', message.subject)
      formData.append('html', message.body)

      const response = await fetch(`https://api.mailgun.net/v3/${config.domain}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${btoa(`api:${config.apiKey}`)}`
        },
        body: formData
      })

      if (!response.ok) {
        const error = await response.text()
        return {
          success: false,
          error: `Mailgun API error: ${error}`
        }
      }

      const result = await response.json()
      return {
        success: true,
        messageId: result.id
      }
    } catch (error) {
      return {
        success: false,
        error: `Mailgun error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private async sendSMTPEmail(message: EmailMessage): Promise<EmailSendResult> {
    // SMTP implementation using server-side function
    return await this.sendServerSideEmail(message)
  }

  /**
   * Send email using server-side Supabase Edge Function
   */
  private async sendServerSideEmail(message: EmailMessage): Promise<EmailSendResult> {
    try {
      // Use the production Supabase URL
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://kwilluhxhthdrqomkecn.supabase.co'
      const functionUrl = `${supabaseUrl}/functions/v1/send-email`

      console.log('🚀 Sending email via server-side function:', {
        functionUrl,
        integrationId: this.integration.id,
        to: message.to,
        subject: message.subject,
        provider: this.integration.provider
      })

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          integrationId: this.integration.id,
          to: message.to,
          subject: message.subject,
          body: message.body,
          testEmail: true
        })
      })

      console.log('📡 Function response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ Function error:', errorData)
        return {
          success: false,
          error: errorData.message || errorData.error || `Server error: ${response.status}`
        }
      }

      const result = await response.json()
      console.log('✅ Function success:', result)

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.success ? undefined : result.message
      }
    } catch (error) {
      console.error('🔥 Network error:', error)
      return {
        success: false,
        error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}

/**
 * Create an email service instance from an integration
 */
export const createEmailService = (integration: EmailIntegration): EmailService => {
  return new EmailService(integration)
}

/**
 * Process Liquid template variables in email content
 */
export const processEmailTemplate = (
  template: string, 
  variables: Record<string, any>
): string => {
  let processed = template
  
  // Simple Liquid-style variable replacement
  // In a real implementation, you'd use a proper Liquid template engine
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
    processed = processed.replace(regex, String(value))
  })
  
  return processed
}
