import { supabase } from './supabase'

export interface ChatRequest {
  message: string
  context?: string
  userId: string
  organizationId: string
  model?: 'deepseek-r1' | 'gemini-flash' | 'qwen-plus' | 'auto'
  messageType?: 'chat_help' | 'blog_generation' | 'workflow_help' | 'code_assistance'
}

export interface ChatResponse {
  success: boolean
  response?: string
  error?: string
  creditsUsed?: number
  creditsLimit?: number
  tokensUsed?: number
  modelUsed?: string
  chargedCredits?: boolean
}

export class AIService {
  private static instance: AIService
  private baseUrl: string

  private constructor() {
    // Use the Supabase URL from environment
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
    if (!supabaseUrl) {
      throw new Error('VITE_SUPABASE_URL environment variable is required')
    }
    this.baseUrl = `${supabaseUrl}/functions/v1`
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService()
    }
    return AIService.instance
  }

  /**
   * Send a chat message to the AI assistant
   */
  async sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
    try {
      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        return {
          success: false,
          error: 'Authentication required'
        }
      }

      const response = await fetch(`${this.baseUrl}/ai-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(request)
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Failed to get AI response',
          creditsUsed: data.creditsUsed,
          creditsLimit: data.creditsLimit
        }
      }

      return {
        success: true,
        response: data.response,
        creditsUsed: data.creditsUsed,
        creditsLimit: data.creditsLimit,
        tokensUsed: data.tokensUsed
      }

    } catch (error) {
      console.error('AI Service Error:', error)
      return {
        success: false,
        error: 'Network error. Please check your connection and try again.'
      }
    }
  }

  /**
   * Get contextual help based on current page/location
   */
  getContextualPrompt(pathname: string): string {
    if (pathname.includes('/blog')) {
      return 'User is currently working on blog content and may need help with writing, editing, or content strategy.'
    } else if (pathname.includes('/automations') || pathname.includes('/workflow')) {
      return 'User is working on workflows and automations and may need help with triggers, actions, or workflow setup.'
    } else if (pathname.includes('/email-integrations')) {
      return 'User is setting up email integrations and may need help with email providers or configuration.'
    } else if (pathname.includes('/credits')) {
      return 'User is checking their usage and credits and may need help understanding limits or upgrading.'
    } else if (pathname.includes('/analytics')) {
      return 'User is viewing analytics and may need help interpreting data or setting up tracking.'
    } else if (pathname.includes('/admin')) {
      return 'User is in the admin area and may need help with platform management or configuration.'
    }

    return 'User is navigating the MBI platform and may need general assistance.'
  }

  /**
   * Determine the best AI model for the request
   */
  selectBestModel(message: string, messageType?: string): 'deepseek-r1' | 'gemini-flash' | 'qwen-plus' {
    // Use Gemini Flash for quick support questions (free)
    if (messageType === 'chat_help' || this.isQuickSupportQuestion(message)) {
      return 'gemini-flash'
    }

    // Use Qwen Plus for blog generation (good at content creation)
    if (messageType === 'blog_generation') {
      return 'qwen-plus'
    }

    // Use DeepSeek for complex workflow tasks (best reasoning)
    if (messageType === 'workflow_help' || messageType === 'code_assistance') {
      return 'deepseek-r1'
    }

    // Default to Gemini for general chat (free)
    return 'gemini-flash'
  }

  /**
   * Check if message is a quick support question
   */
  private isQuickSupportQuestion(message: string): boolean {
    const quickQuestionKeywords = [
      'how do i', 'how to', 'what is', 'where is', 'can i', 'do you support',
      'pricing', 'plan', 'credit', 'limit', 'feature', 'integration'
    ]

    const lowerMessage = message.toLowerCase()
    return quickQuestionKeywords.some(keyword => lowerMessage.includes(keyword))
  }

  /**
   * Check if user has sufficient AI credits
   */
  async checkCreditsAvailable(organizationId: string): Promise<{
    available: boolean
    used: number
    limit: number
    remaining: number
  }> {
    try {
      const { data: organization, error } = await supabase
        .from('organizations')
        .select('ai_credits_used, ai_credits_limit')
        .eq('id', organizationId)
        .single()

      if (error || !organization) {
        return {
          available: false,
          used: 0,
          limit: 0,
          remaining: 0
        }
      }

      const used = organization.ai_credits_used || 0
      const limit = organization.ai_credits_limit || 50
      const remaining = Math.max(0, limit - used)

      return {
        available: remaining > 0,
        used,
        limit,
        remaining
      }

    } catch (error) {
      console.error('Error checking credits:', error)
      return {
        available: false,
        used: 0,
        limit: 0,
        remaining: 0
      }
    }
  }

  /**
   * Get AI usage statistics for an organization
   */
  async getUsageStats(organizationId: string, days: number = 30): Promise<{
    totalInteractions: number
    totalTokens: number
    totalCredits: number
    dailyUsage: Array<{
      date: string
      interactions: number
      tokens: number
      credits: number
    }>
  }> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const { data: interactions, error } = await supabase
        .from('ai_interactions')
        .select('*')
        .eq('organization_id', organizationId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true })

      if (error) {
        throw error
      }

      const totalInteractions = interactions?.length || 0
      const totalTokens = interactions?.reduce((sum, i) => sum + (i.total_tokens || 0), 0) || 0
      const totalCredits = interactions?.reduce((sum, i) => sum + (i.credits_used || 0), 0) || 0

      // Group by date for daily usage
      const dailyUsage = new Map<string, { interactions: number, tokens: number, credits: number }>()
      
      interactions?.forEach(interaction => {
        const date = new Date(interaction.created_at).toISOString().split('T')[0]
        const existing = dailyUsage.get(date) || { interactions: 0, tokens: 0, credits: 0 }
        
        dailyUsage.set(date, {
          interactions: existing.interactions + 1,
          tokens: existing.tokens + (interaction.total_tokens || 0),
          credits: existing.credits + (interaction.credits_used || 0)
        })
      })

      const dailyUsageArray = Array.from(dailyUsage.entries()).map(([date, usage]) => ({
        date,
        ...usage
      }))

      return {
        totalInteractions,
        totalTokens,
        totalCredits,
        dailyUsage: dailyUsageArray
      }

    } catch (error) {
      console.error('Error getting usage stats:', error)
      return {
        totalInteractions: 0,
        totalTokens: 0,
        totalCredits: 0,
        dailyUsage: []
      }
    }
  }
}

// Export singleton instance
export const aiService = AIService.getInstance()
