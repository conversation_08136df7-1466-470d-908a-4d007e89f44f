// Optimized Analytics API Functions
// These functions use the optimized database functions for much faster loading

import { supabase } from './supabase'

export interface AnalyticsOverview {
  totalViews: number
  totalUniqueViews: number
  totalClicks: number
  avgReadingTime: number
  avgBounceRate: number
  totalPosts: number
  publishedPosts: number
}

export interface ChartDataPoint {
  date: string
  views: number
  uniqueViews: number
  clicks: number
  readingTime: number
}

export interface TrafficSource {
  referrer: string
  views: number
  percentage: number
}

export interface DeviceStats {
  device_type: string
  views: number
  percentage: number
}

export interface TopPost {
  post_id: string
  title: string
  slug: string
  view_count: number
  unique_view_count: number
  click_count: number
  avg_reading_time: number
  bounce_rate: number
  reaction_count: number
  comment_count: number
  share_count: number
  published_at: string
}

// Get analytics overview using optimized function
export const getAnalyticsOverview = async (
  organizationId?: string,
  timeRange: string = '30d'
): Promise<AnalyticsOverview> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const { data, error } = await supabase.rpc('get_analytics_overview', {
    p_organization_id: organizationId || null,
    p_start_date: startDate.toISOString(),
    p_end_date: new Date().toISOString()
  })

  if (error) {
    console.error('Error fetching analytics overview:', error)
    throw error
  }

  const result = data?.[0] || {}
  return {
    totalViews: parseInt(result.total_views) || 0,
    totalUniqueViews: parseInt(result.total_unique_views) || 0,
    totalClicks: parseInt(result.total_clicks) || 0,
    avgReadingTime: parseInt(result.avg_reading_time) || 0,
    avgBounceRate: parseFloat(result.avg_bounce_rate) || 0,
    totalPosts: parseInt(result.total_posts) || 0,
    publishedPosts: parseInt(result.published_posts) || 0
  }
}

// Get chart data using optimized function
export const getAnalyticsChartData = async (
  organizationId?: string,
  timeRange: string = '30d'
): Promise<ChartDataPoint[]> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365

  const { data, error } = await supabase.rpc('get_analytics_chart_data', {
    p_organization_id: organizationId || null,
    p_days: days
  })

  if (error) {
    console.error('Error fetching chart data:', error)
    throw error
  }

  return (data || []).map((item: any) => ({
    date: item.date_label,
    views: parseInt(item.views) || 0,
    uniqueViews: parseInt(item.unique_views) || 0,
    clicks: parseInt(item.clicks) || 0,
    readingTime: parseInt(item.avg_reading_time) || 0
  }))
}

// Get traffic sources using optimized function
export const getTrafficSources = async (
  organizationId?: string,
  timeRange: string = '30d'
): Promise<TrafficSource[]> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const { data, error } = await supabase.rpc('get_traffic_sources', {
    p_organization_id: organizationId || null,
    p_start_date: startDate.toISOString(),
    p_limit: 10
  })

  if (error) {
    console.error('Error fetching traffic sources:', error)
    throw error
  }

  return (data || []).map((item: any) => ({
    referrer: item.referrer,
    views: parseInt(item.views) || 0,
    percentage: parseFloat(item.percentage) || 0
  }))
}

// Get device stats using optimized function
export const getDeviceStats = async (
  organizationId?: string,
  timeRange: string = '30d'
): Promise<DeviceStats[]> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const { data, error } = await supabase.rpc('get_device_stats', {
    p_organization_id: organizationId || null,
    p_start_date: startDate.toISOString()
  })

  if (error) {
    console.error('Error fetching device stats:', error)
    throw error
  }

  return (data || []).map((item: any) => ({
    device_type: item.device_type,
    views: parseInt(item.views) || 0,
    percentage: parseFloat(item.percentage) || 0
  }))
}

// Get top posts using optimized function
export const getTopPosts = async (
  organizationId?: string,
  timeRange: string = '30d',
  sortBy: string = 'view_count',
  limit: number = 10
): Promise<TopPost[]> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const { data, error } = await supabase.rpc('get_top_posts', {
    p_organization_id: organizationId || null,
    p_start_date: startDate.toISOString(),
    p_sort_by: sortBy,
    p_limit: limit
  })

  if (error) {
    console.error('Error fetching top posts:', error)
    throw error
  }

  return (data || []).map((item: any) => ({
    post_id: item.post_id,
    title: item.title,
    slug: item.slug,
    view_count: parseInt(item.view_count) || 0,
    unique_view_count: parseInt(item.unique_view_count) || 0,
    click_count: parseInt(item.click_count) || 0,
    avg_reading_time: parseInt(item.avg_reading_time) || 0,
    bounce_rate: parseFloat(item.bounce_rate) || 0,
    reaction_count: parseInt(item.reaction_count) || 0,
    comment_count: parseInt(item.comment_count) || 0,
    share_count: parseInt(item.share_count) || 0,
    published_at: item.published_at
  }))
}

// Enhanced traffic source detection for paid plans
export const getEnhancedTrafficSources = async (
  organizationId?: string,
  timeRange: string = '30d',
  includeUTMParams: boolean = false
): Promise<TrafficSource[]> => {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  // For paid plans, we can include more detailed tracking
  if (includeUTMParams) {
    const { data, error } = await supabase
      .from('blog_post_views')
      .select('referrer, user_agent, created_at')
      .gte('created_at', startDate.toISOString())
      .eq('organization_id', organizationId)

    if (error) {
      console.error('Error fetching enhanced traffic sources:', error)
      throw error
    }

    // Process UTM parameters and detailed referrer analysis
    const sourceMap = new Map<string, number>()
    
    data?.forEach(view => {
      let source = 'Direct'
      
      if (view.referrer) {
        const url = new URL(view.referrer)
        const utmSource = url.searchParams.get('utm_source')
        const utmMedium = url.searchParams.get('utm_medium')
        const utmCampaign = url.searchParams.get('utm_campaign')
        
        if (utmSource) {
          source = `${utmSource}${utmMedium ? ` (${utmMedium})` : ''}${utmCampaign ? ` - ${utmCampaign}` : ''}`
        } else {
          // Enhanced domain detection
          const hostname = url.hostname.toLowerCase()
          if (hostname.includes('facebook')) source = 'Facebook'
          else if (hostname.includes('twitter') || hostname.includes('t.co')) source = 'Twitter'
          else if (hostname.includes('linkedin')) source = 'LinkedIn'
          else if (hostname.includes('instagram')) source = 'Instagram'
          else if (hostname.includes('youtube')) source = 'YouTube'
          else if (hostname.includes('reddit')) source = 'Reddit'
          else if (hostname.includes('github')) source = 'GitHub'
          else if (hostname.includes('google')) source = 'Google'
          else if (hostname.includes('bing')) source = 'Bing'
          else if (hostname.includes('yahoo')) source = 'Yahoo'
          else source = hostname
        }
      }
      
      sourceMap.set(source, (sourceMap.get(source) || 0) + 1)
    })

    const totalViews = Array.from(sourceMap.values()).reduce((sum, count) => sum + count, 0)
    
    return Array.from(sourceMap.entries())
      .map(([referrer, views]) => ({
        referrer,
        views,
        percentage: Math.round((views / totalViews) * 100 * 10) / 10
      }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 15) // More detailed for paid plans
  }

  // Fallback to basic traffic sources for free plans
  return getTrafficSources(organizationId, timeRange)
}

// Utility function to check if user has access to enhanced analytics
export const hasEnhancedAnalyticsAccess = (userRole?: string, subscriptionPlan?: string): boolean => {
  // Enhanced analytics for paid plans or admin users
  return (
    ['admin', 'super_admin', 'owner', 'saas_owner'].includes(userRole || '') ||
    ['basic', 'pro', 'enterprise'].includes(subscriptionPlan || '')
  )
}
