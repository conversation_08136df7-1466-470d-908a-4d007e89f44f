import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase, Profile, getUserProfile, createOrUpdateProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const refreshProfile = useCallback(async () => {
    if (!user || refreshing) return

    try {
      setRefreshing(true)
      console.log('AuthContext: Refreshing profile for user:', user.email, 'user ID:', user.id)
      const { data, error } = await getUserProfile(user.id)
      console.log('AuthContext: Profile data received:', data, 'error:', error)

      if (data && !error) {
        console.log('AuthContext: Profile loaded successfully:', data)
        setProfile(data)
      } else if (error) {
        console.error('AuthContext: Error loading profile:', error)

        // If profile doesn't exist, try to create it
        if (error.code === 'PGRST116' || error.message.includes('No rows')) {
          console.log('AuthContext: Profile not found, attempting to create...')
          try {
            const { data: newProfile, error: createError } = await createOrUpdateProfile(user)
            if (newProfile && !createError) {
              console.log('AuthContext: Profile created successfully:', newProfile)
              setProfile(newProfile)
            } else {
              console.error('AuthContext: Failed to create profile:', createError)
            }
          } catch (createErr) {
            console.error('AuthContext: Exception creating profile:', createErr)
          }
        }
      }
    } catch (err) {
      console.error('AuthContext: Exception in refreshProfile:', err)
    } finally {
      setRefreshing(false)
    }
  }, [user, refreshing])

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setUser(session?.user ?? null)
      
      if (session?.user) {
        await refreshProfile()
      }
      
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null)
        
        if (session?.user) {
          await refreshProfile()
        } else {
          setProfile(null)
        }
        
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  useEffect(() => {
    if (user && !profile && !loading && !refreshing) {
      console.log('AuthContext: User exists but no profile, refreshing...')
      refreshProfile()
    }
  }, [user, profile, loading, refreshing]) // Added refreshing to prevent multiple calls

  // Add a timeout to force profile refresh if it's taking too long - but only once
  useEffect(() => {
    if (user && !profile && !loading && !refreshing) {
      console.log('AuthContext: Profile still not loaded after auth complete, forcing refresh...')
      const timeout = setTimeout(() => {
        if (!profile && !refreshing) { // Only refresh if profile is still null and not already refreshing
          refreshProfile()
        }
      }, 2000)
      return () => clearTimeout(timeout)
    }
  }, [user, loading, refreshing]) // Added refreshing to prevent multiple calls

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    setUser(null)
    setProfile(null)
  }

  const value = {
    user,
    profile,
    loading,
    signOut: handleSignOut,
    refreshProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
