
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import ContactIcon from './ContactIcon';
import { Input } from '@/components/ui/input';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleNewsletterSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !isValidEmail(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsSubscribing(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/newsletter-subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          email: email.toLowerCase().trim(),
          source: 'website_footer'
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message);
        setEmail(''); // Clear the input
      } else {
        toast.error(result.error || 'Failed to subscribe');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Failed to subscribe. Please try again.');
    } finally {
      setIsSubscribing(false);
    }
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <footer className="bg-gray-100 dark:bg-black border-t border-gray-200 dark:border-white/10">
      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="grid sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
          {/* Company Info */}
          <div className="space-y-3 sm:space-y-4 sm:col-span-2 md:col-span-1">
            <div className="flex items-center">
              <img
                src="/Primary-Logo.png"
                alt="Millennial Business Innovations"
                className="h-8 sm:h-10 w-auto"
              />
            </div>
            <p className="font-poppins text-gray-600 dark:text-gray-400 text-sm">
              Transforming ideas into digital reality. Empowering businesses with innovative solutions.
            </p>
            <div className="flex space-x-2 sm:space-x-3">
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-400 hover:text-primary p-1 sm:p-2"
                asChild
              >
                <a
                  href="https://www.youtube.com/@MillennialBusinessInnovations"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Visit our YouTube channel"
                >
                  <ContactIcon type="youtube" className="w-4 h-4" />
                </a>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-400 hover:text-primary p-1 sm:p-2"
                asChild
              >
                <a
                  href="https://www.instagram.com/millennialbusinessinnovations/profilecard/#"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Follow us on Instagram"
                >
                  <ContactIcon type="instagram" className="w-4 h-4" />
                </a>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-400 hover:text-primary p-1 sm:p-2"
                asChild
              >
                <a
                  href="https://www.linkedin.com/company/millennial-business-innovations"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Connect with us on LinkedIn"
                >
                  <ContactIcon type="linkedin" className="w-4 h-4" />
                </a>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-400 hover:text-primary p-1 sm:p-2"
                asChild
              >
                <a
                  href="https://x.com/Millennial43828"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Follow us on X (Twitter)"
                >
                  <ContactIcon type="x" className="w-4 h-4" />
                </a>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-400 hover:text-primary p-1 sm:p-2"
                asChild
              >
                <a
                  href="https://www.facebook.com/people/Millennial-Business-Innovations/61555569486854/"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Like us on Facebook"
                >
                  <ContactIcon type="facebook" className="w-4 h-4" />
                </a>
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-montserrat font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Quick Links</h3>
            <ul className="space-y-1 sm:space-y-2">
              {[
                { label: 'Home', id: 'home' },
                { label: 'About', id: 'about' },
                { label: 'Services', id: 'services' },
                { label: 'Portfolio', id: 'portfolio' }
              ].map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.id)}
                    className="font-poppins text-gray-600 dark:text-gray-400 hover:text-primary transition-colors text-sm"
                  >
                    {link.label}
                  </button>
                </li>
              ))}
              <li>
                <Link
                  to="/testimonials"
                  className="font-poppins text-gray-600 dark:text-gray-400 hover:text-primary transition-colors text-sm"
                >
                  Success Stories
                </Link>
              </li>
              <li>
                <Link
                  to="/blog"
                  className="font-poppins text-gray-600 dark:text-gray-400 hover:text-primary transition-colors text-sm"
                >
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-montserrat font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Services</h3>
            <ul className="space-y-1 sm:space-y-2">
              {[
                'MVP Development',
                'Custom Web Solutions',
                'Landing Pages',
                'SaaS Platforms',
                'Digital Strategy'
              ].map((service, index) => (
                <li key={index} className="font-poppins text-gray-600 dark:text-gray-400 text-sm">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div className="space-y-3 sm:space-y-4 sm:col-span-2 md:col-span-1">
            <h3 className="font-montserrat font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Stay Updated</h3>
            <p className="font-poppins text-gray-600 dark:text-gray-400 text-sm">
              Subscribe to our newsletter for the latest insights and updates.
            </p>
            <form onSubmit={handleNewsletterSubscribe} className="flex flex-col sm:flex-row gap-2">
              <Input
                type="email"
                placeholder="Your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isSubscribing}
                className="bg-gray-50 dark:bg-white/10 border-gray-300 dark:border-white/20 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-primary text-sm flex-1"
                required
              />
              <Button
                type="submit"
                size="sm"
                disabled={isSubscribing || !email}
                className="bg-primary hover:bg-primary/80 text-white px-3 sm:px-4 text-sm disabled:opacity-50"
              >
                {isSubscribing ? (
                  <>
                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                    Subscribing...
                  </>
                ) : (
                  'Subscribe'
                )}
              </Button>
            </form>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-white/10 mt-8 sm:mt-12 pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="font-poppins text-gray-600 dark:text-gray-400 text-xs sm:text-sm text-center sm:text-left">
            © 2024 Millennial Business Innovations. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center sm:justify-end gap-4 sm:gap-6">
            <Link
              to="/privacy-policy"
              className="font-poppins text-gray-600 dark:text-gray-400 hover:text-primary text-xs sm:text-sm transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              to="/terms-and-conditions"
              className="font-poppins text-gray-600 dark:text-gray-400 hover:text-primary text-xs sm:text-sm transition-colors"
            >
              Terms & Conditions
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
