
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, Calendar } from 'lucide-react';
import CalendarModal from '@/components/CalendarModal';
import { TextShimmer } from '@/components/ui/text-shimmer';

const HeroSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const techStack = [
    {
      name: 'React',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
      color: '#61DAFB'
    },
    {
      name: 'Next.js',
      icon: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/nextdotjs.svg',
      color: 'currentColor'
    },
    {
      name: 'TypeScript',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
      color: '#3178C6'
    },
    {
      name: 'Node.js',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
      color: '#339933'
    },
    {
      name: 'Python',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',
      color: '#3776AB'
    },
    {
      name: 'MongoDB',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg',
      color: '#47A248'
    },
    {
      name: 'PostgreSQL',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
      color: '#336791'
    },
    {
      name: 'AWS',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original-wordmark.svg',
      color: '#FF9900'
    },
    {
      name: 'Docker',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',
      color: '#2496ED'
    },
    {
      name: 'Figma',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg',
      color: '#F24E1E'
    },
    {
      name: 'Tableau',
      icon: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/tableau.svg',
      color: '#E97627'
    },
    {
      name: 'SQL',
      icon: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/databricks.svg',
      color: '#FF3621'
    },
    {
      name: 'MySQL',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg',
      color: '#4479A1'
    },
    {
      name: 'Oracle',
      icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/oracle/oracle-original.svg',
      color: '#F80000'
    }
  ];

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20 sm:pt-24">
      {/* Glassmorphism Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-cyan-50 via-blue-50 to-teal-50 dark:from-gray-900 dark:via-cyan-900/20 dark:to-blue-900/20"></div>

      {/* Glassmorphism floating elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-full blur-xl animate-float backdrop-blur-sm"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/15 to-teal-400/15 rounded-full blur-2xl animate-float backdrop-blur-sm" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-gradient-to-r from-teal-400/10 to-cyan-400/10 rounded-full blur-xl animate-float backdrop-blur-sm" style={{animationDelay: '2s'}}></div>


      </div>

      {/* Glassmorphism glow at bottom */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-24 bg-gradient-to-t from-white/30 to-transparent rounded-full blur-2xl backdrop-blur-sm"></div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Glassmorphism badge */}
        <div
          className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 dark:bg-white/10 backdrop-blur-md border border-white/30 dark:border-white/20 text-gray-800 dark:text-white text-sm font-poppins mb-8 animate-fade-in-up cursor-pointer hover:bg-white/30 dark:hover:bg-white/15 transition-all duration-300 shadow-lg"
          onClick={() => window.open('https://site.aha-innovations.com', '_blank')}
        >
          <span className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-2 py-1 rounded-full text-xs mr-3 shadow-sm">New</span>
          Aha-Innovations, your all in one platform is now live
          <ArrowRight className="ml-2 w-4 h-4" />
        </div>
        
        <h1 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl mb-6 animate-fade-in-up leading-tight" style={{animationDelay: '0.2s'}}>
          <TextShimmer
            as="span"
            duration={3}
            className="text-gray-900 dark:text-white [--base-color:theme(colors.gray.900)] [--base-gradient-color:theme(colors.cyan.500)] dark:[--base-color:theme(colors.white)] dark:[--base-gradient-color:theme(colors.cyan.400)]"
          >
            You dream it.
          </TextShimmer>{' '}
          <TextShimmer
            as="span"
            duration={2.5}
            className="[--base-color:theme(colors.cyan.600)] [--base-gradient-color:theme(colors.blue.400)] dark:[--base-color:theme(colors.cyan.400)] dark:[--base-gradient-color:theme(colors.blue.300)]"
          >
            We build it.
          </TextShimmer>
        </h1>

        <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 max-w-4xl mx-auto animate-fade-in-up px-4 sm:px-0 italic" style={{animationDelay: '0.4s'}}>
          Helping founders launch MVPs and{' '}
          <TextShimmer
            as="span"
            duration={2.8}
            className="font-semibold [--base-color:theme(colors.cyan.600)] [--base-gradient-color:theme(colors.blue.500)] dark:[--base-color:theme(colors.cyan.400)] dark:[--base-gradient-color:theme(colors.blue.300)]"
          >
            digital solutions
          </TextShimmer>{' '}
          without the tech overwhelm.
        </p>

        <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-8 sm:mb-12 max-w-3xl mx-auto animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.5s'}}>
          👋 Hello, we're <span className="text-gray-900 dark:text-white font-semibold">Millennial Business Innovations</span> your full-stack product team.
        </p>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.6s'}}>
          <Button
            onClick={() => setIsCalendarOpen(true)}
            className="w-full sm:w-auto bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-poppins px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-cyan-500/25 backdrop-blur-md border border-white/20 shadow-lg"
          >
            Let's Connect
            <ArrowRight className="ml-2 w-4 sm:w-5 h-4 sm:h-5" />
          </Button>

          <Button
            variant="ghost"
            onClick={() => scrollToSection('services')}
            className="w-full sm:w-auto text-gray-900 dark:text-white bg-white/20 dark:bg-white/10 border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-white/15 font-poppins px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg backdrop-blur-md shadow-lg transition-all duration-300"
          >
            <Calendar className="mr-2 w-4 sm:w-5 h-4 sm:h-5" />
            View Our Services
          </Button>
        </div>

        {/* Tech Stack Section */}
        <div className="animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.8s'}}>
          <p className="text-xs sm:text-sm font-poppins mb-4 sm:mb-6 uppercase tracking-wider">
            <TextShimmer
              as="span"
              duration={4}
              className="text-gray-500 dark:text-gray-400 [--base-color:theme(colors.gray.500)] [--base-gradient-color:theme(colors.gray.700)] dark:[--base-color:theme(colors.gray.400)] dark:[--base-gradient-color:theme(colors.gray.200)]"
            >
              Built with the tools you love
            </TextShimmer>
          </p>
          <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto">
            {techStack.map((tech, index) => (
              <div
                key={tech.name}
                className="flex flex-col items-center group cursor-pointer"
                style={{animationDelay: `${0.9 + index * 0.1}s`}}
              >
                <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-lg animate-fade-in-up bg-white/40 dark:bg-gray-800/60 backdrop-blur-md rounded-xl border border-white/50 dark:border-gray-600/50 shadow-lg group-hover:bg-white/60 dark:group-hover:bg-gray-700/70">
                  <img
                    src={tech.icon}
                    alt={`${tech.name} icon`}
                    className="w-full h-full object-contain drop-shadow-sm"
                    style={{
                      filter: tech.color === 'currentColor' ? 'brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0,0,0,0.3))' : 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))'
                    }}
                    onError={(e) => {
                      // Fallback to a simple placeholder if icon fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.parentElement!.innerHTML = `<div class="w-full h-full bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center text-xs font-semibold text-gray-800 dark:text-gray-200 shadow-inner">${tech.name.slice(0, 2).toUpperCase()}</div>`;
                    }}
                  />
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 sm:mt-2 font-poppins group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-300">
                  {tech.name}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Social Proof & Urgency */}
        <div className="mt-12 sm:mt-16 mb-16 sm:mb-20 md:mb-24 animate-fade-in-up" style={{animationDelay: '1.2s'}}>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 dark:text-gray-400 px-4 sm:px-0">
            <span className="flex items-center gap-2 bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-full border border-red-200 dark:border-red-800">
              <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
              <strong className="text-red-600 dark:text-red-400">Limited:</strong> Only 3 spots left for Q1 2025
            </span>
            <span className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              50+ successful launches
            </span>
            <span className="flex items-center gap-2">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              $100M+ funding raised
            </span>
          </div>
        </div>
      </div>

      {/* Calendar Modal */}
      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />
    </section>
  );
};

export default HeroSection;
