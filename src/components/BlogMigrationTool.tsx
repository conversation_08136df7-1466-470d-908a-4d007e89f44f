import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Database, CheckCircle, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'

const BlogMigrationTool = () => {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)

  const runMigration = async () => {
    setIsRunning(true)
    setError(null)
    setResults([])

    try {
      // Step 1: Check current state
      const { data: beforeData, error: beforeError } = await supabase
        .from('blog_posts')
        .select('id, title, status, submission_status, organization_id')

      if (beforeError) throw beforeError

      setResults(prev => [...prev, {
        step: 'Before Migration',
        data: {
          total: beforeData.length,
          private: beforeData.filter(p => p.submission_status === 'private').length,
          mbi: beforeData.filter(p => p.submission_status === 'published_mbi').length,
          with_org: beforeData.filter(p => p.organization_id).length
        }
      }])

      // Step 2: Update all posts to be MBI published
      const { error: updateError } = await supabase
        .from('blog_posts')
        .update({ 
          submission_status: 'published_mbi',
          updated_at: new Date().toISOString()
        })
        .neq('submission_status', 'published_mbi')

      if (updateError) throw updateError

      // Step 3: Fix organization assignments
      const { data: postsWithoutOrg } = await supabase
        .from('blog_posts')
        .select('id, author_id')
        .is('organization_id', null)

      if (postsWithoutOrg && postsWithoutOrg.length > 0) {
        for (const post of postsWithoutOrg) {
          const { data: orgMember } = await supabase
            .from('organization_members')
            .select('organization_id')
            .eq('user_id', post.author_id)
            .eq('is_active', true)
            .limit(1)
            .single()

          if (orgMember) {
            await supabase
              .from('blog_posts')
              .update({ organization_id: orgMember.organization_id })
              .eq('id', post.id)
          }
        }
      }

      // Step 4: Check final state
      const { data: afterData, error: afterError } = await supabase
        .from('blog_posts')
        .select('id, title, status, submission_status, organization_id')

      if (afterError) throw afterError

      setResults(prev => [...prev, {
        step: 'After Migration',
        data: {
          total: afterData.length,
          private: afterData.filter(p => p.submission_status === 'private').length,
          mbi: afterData.filter(p => p.submission_status === 'published_mbi').length,
          with_org: afterData.filter(p => p.organization_id).length
        }
      }])

      toast.success('Migration completed successfully!')

    } catch (err: any) {
      console.error('Migration error:', err)
      setError(err.message || 'An error occurred during migration')
      toast.error('Migration failed')
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Blog Migration Tool
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            This tool will migrate all existing blog posts to the public MBI blog to fix loading issues.
            All old posts will become visible again as public content.
          </AlertDescription>
        </Alert>

        <Button 
          onClick={runMigration} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Running Migration...
            </>
          ) : (
            <>
              <Database className="h-4 w-4 mr-2" />
              Run Migration
            </>
          )}
        </Button>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              Migration Results
            </h3>
            {results.map((result, index) => (
              <Card key={index} className="p-3">
                <h4 className="font-medium mb-2">{result.step}</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Total Posts: {result.data.total}</div>
                  <div>With Organization: {result.data.with_org}</div>
                  <div>Private Posts: {result.data.private}</div>
                  <div>MBI Posts: {result.data.mbi}</div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default BlogMigrationTool
