import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'

interface RoleBasedRouteProps {
  children: React.ReactNode
  requiredRoles?: string[]
  adminOnly?: boolean
  redirectTo?: string
}

const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  requiredRoles = ['user'],
  adminOnly = false,
  redirectTo
}) => {
  const { user, profile, loading } = useAuth()
  const location = useLocation()

  // Debug logging
  console.log('RoleBasedRoute: Current path:', location.pathname)
  console.log('RoleBasedRoute: Loading:', loading)
  console.log('RoleBasedRoute: User:', user?.email)
  console.log('RoleBasedRoute: Profile:', profile)
  console.log('RoleBasedRoute: AdminOnly:', adminOnly)
  console.log('RoleBasedRoute: RequiredRoles:', requiredRoles)

  // Show loading while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to home if not authenticated
  if (!user) {
    return <Navigate to="/" state={{ from: location }} replace />
  }

  // If profile is still loading, show loading state
  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Setting up your workspace...</p>
        </div>
      </div>
    )
  }

  // Check if user has required role
  const userRole = profile.role || 'user'
  const adminRoles = ['admin', 'super_admin', 'owner', 'saas_owner']

  // Admin-only routes
  if (adminOnly) {
    if (!adminRoles.includes(userRole)) {
      console.log('RoleBasedRoute: User not admin, redirecting to dashboard')
      // Redirect regular users from admin routes to dashboard
      const dashboardPath = location.pathname.replace('/admin', '/dashboard')
      return <Navigate to={dashboardPath} replace />
    }
    // For admin-only routes, if user is admin, allow access regardless of requiredRoles
    console.log('RoleBasedRoute: Admin user accessing admin route, allowing access')
    return <>{children}</>
  }

  // For non-admin routes, check required roles
  const hasRequiredRole = requiredRoles.includes(userRole)
  if (!hasRequiredRole) {
    console.log('RoleBasedRoute: User does not have required role, redirecting')
    // Determine appropriate redirect
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />
    }

    // Default redirects based on role
    if (adminRoles.includes(userRole)) {
      return <Navigate to="/admin/blog" replace />
    } else {
      return <Navigate to="/dashboard" replace />
    }
  }

  // User has required role for non-admin route
  console.log('RoleBasedRoute: User has required role, allowing access')
  return <>{children}</>
}

export default RoleBasedRoute
