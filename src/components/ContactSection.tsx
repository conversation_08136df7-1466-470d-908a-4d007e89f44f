
import React, { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ContactIcon from './ContactIcon';

const ContactSection = () => {
  // Load GHL form script and apply custom styling
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://calendar.aha-innovations.com/js/form_embed.js';
    script.async = true;
    document.body.appendChild(script);

    // Add custom CSS for GHL form button styling
    const style = document.createElement('style');
    style.textContent = `
      /* GHL Form Button Styling */
      #inline-TfvZBTrnRPxQF20XyV3A button[type="submit"],
      #inline-TfvZBTrnRPxQF20XyV3A .form-submit-button,
      #inline-TfvZBTrnRPxQF20XyV3A .submit-button,
      #inline-TfvZBTrnRPxQF20XyV3A input[type="submit"] {
        background: linear-gradient(to right, #06b6d4, #3b82f6) !important;
        color: white !important;
        border: none !important;
        padding: 12px 24px !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
      }

      #inline-TfvZBTrnRPxQF20XyV3A button[type="submit"]:hover,
      #inline-TfvZBTrnRPxQF20XyV3A .form-submit-button:hover,
      #inline-TfvZBTrnRPxQF20XyV3A .submit-button:hover,
      #inline-TfvZBTrnRPxQF20XyV3A input[type="submit"]:hover {
        background: linear-gradient(to right, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8)) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3) !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Cleanup script and styles on component unmount
      const existingScript = document.querySelector('script[src="https://calendar.aha-innovations.com/js/form_embed.js"]');
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    };
  }, []);

  return (
    <section id="contact" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-teal-50 via-cyan-50 to-blue-50 dark:from-black dark:via-teal-900/10 dark:to-cyan-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-44 h-44 bg-gradient-to-r from-teal-300/20 to-cyan-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-10 w-56 h-56 bg-gradient-to-r from-cyan-300/15 to-blue-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 right-1/3 w-28 h-28 bg-gradient-to-r from-blue-300/10 to-teal-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Let's <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Connect</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            Ready to transform your ideas into digital reality? Get in touch with us today and let's discuss your project.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 max-w-6xl mx-auto">
          {/* Contact Information */}
          <div className="space-y-6 sm:space-y-8">
            <Card className="bg-white/5 border border-white/10 backdrop-blur-lg">
              <CardContent className="p-4 sm:p-6 md:p-8">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-gray-900 dark:text-white mb-4 sm:mb-6">
                  Get in Touch
                </h3>

                <div className="space-y-4 sm:space-y-6">
                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="email" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Email</h4>
                      <p className="font-poppins text-gray-600 dark:text-gray-300 text-xs sm:text-sm break-all"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="phone" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Phone</h4>
                      <p className="font-poppins text-gray-600 dark:text-gray-300 text-xs sm:text-sm">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="social" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Social Media</h4>
                      <div className="flex flex-wrap gap-2 sm:gap-3 mt-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3"
                          asChild
                        >
                          <a
                            href="https://www.linkedin.com/company/millennial-business-innovations"
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label="Connect with us on LinkedIn"
                          >
                            <ContactIcon type="linkedin" className="w-4 h-4 mr-1" />
                            LinkedIn
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3"
                          asChild
                        >
                          <a
                            href="https://x.com/Millennial43828"
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label="Follow us on X (Twitter)"
                          >
                            <ContactIcon type="x" className="w-4 h-4 mr-1" />
                            Twitter
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3"
                          asChild
                        >
                          <a
                            href="https://www.instagram.com/millennialbusinessinnovations/profilecard/#"
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label="Follow us on Instagram"
                          >
                            <ContactIcon type="instagram" className="w-4 h-4 mr-1" />
                            Instagram
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3"
                          asChild
                        >
                          <a
                            href="https://www.facebook.com/people/Millennial-Business-Innovations/61555569486854/"
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label="Like us on Facebook"
                          >
                            <ContactIcon type="facebook" className="w-4 h-4 mr-1" />
                            Facebook
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3"
                          asChild
                        >
                          <a
                            href="https://www.youtube.com/@MillennialBusinessInnovations"
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label="Visit our YouTube channel"
                          >
                            <ContactIcon type="youtube" className="w-4 h-4 mr-1" />
                            YouTube
                          </a>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg">
              <CardContent className="p-4 sm:p-6 md:p-8">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-gray-900 dark:text-white mb-3 sm:mb-4">
                  Why Choose Us?
                </h3>
                <ul className="space-y-2 sm:space-y-3">
                  {[
                    'Experienced team of developers and designers',
                    'Proven track record with 50+ successful projects',
                    'Fast turnaround times without compromising quality',
                    'Ongoing support and maintenance',
                    'Transparent pricing and communication'
                  ].map((item, index) => (
                    <li key={index} className="flex items-center text-gray-600 dark:text-gray-300 font-poppins text-sm sm:text-base">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form - GHL Embedded */}
          <Card className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg">
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white">
                Send us a Message
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              <div className="w-full" style={{ minHeight: '432px' }}>
                <iframe
                  src="https://calendar.aha-innovations.com/widget/form/TfvZBTrnRPxQF20XyV3A"
                  style={{
                    width: '100%',
                    height: '432px',
                    border: 'none',
                    borderRadius: '3px'
                  }}
                  id="inline-TfvZBTrnRPxQF20XyV3A"
                  data-layout="{'id':'INLINE'}"
                  data-trigger-type="alwaysShow"
                  data-trigger-value=""
                  data-activation-type="alwaysActivated"
                  data-activation-value=""
                  data-deactivation-type="neverDeactivate"
                  data-deactivation-value=""
                  data-form-name="Form 20"
                  data-height="432"
                  data-layout-iframe-id="inline-TfvZBTrnRPxQF20XyV3A"
                  data-form-id="TfvZBTrnRPxQF20XyV3A"
                  title="Form 20"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
