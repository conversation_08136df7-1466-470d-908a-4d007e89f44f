import React from 'react';
import { InfiniteSlider } from '@/components/ui/infinite-slider';
import { ProgressiveBlur } from '@/components/ui/progressive-blur';

const CompaniesSection = () => {
  const companies = [
    {
      id: "dbmedia",
      name: 'DBmedia',
      logo: '/DBmedia.png',
      alt: 'DBmedia'
    },
    {
      id: "juan-creatives",
      name: '<PERSON>s',
      logo: '/JuanCreatives.png',
      alt: 'Juan Creatives'
    },
    {
      id: "lovino-web",
      name: 'Lovino Web Creations',
      logo: '/LovinoWebCreations.png',
      alt: 'Lovino Web Creations'
    },
    {
      id: "mba",
      name: 'MBA',
      logo: '/MBA.png',
      alt: 'MBA'
    },
    {
      id: "mtech",
      name: 'MTech',
      logo: '/MTech.png',
      alt: 'MTech'
    },
    {
      id: "rb-collabs",
      name: 'RB Collabs',
      logo: '/RBcollabs.png',
      alt: 'RB Collabs'
    },
    {
      id: "savvytech",
      name: 'Savvytech',
      logo: '/Savvytech.png',
      alt: 'Savvytech'
    },
    {
      id: "triedge",
      name: '<PERSON><PERSON><PERSON>',
      logo: '/TriEdge.png',
      alt: 'TriEdge'
    }
  ];

  return (
    <section className="py-8 sm:py-12 bg-gradient-to-r from-cyan-50/30 via-blue-50/30 to-teal-50/30 dark:from-black/30 dark:via-blue-900/10 dark:to-teal-900/10 border-y border-gray-200/30 dark:border-white/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-4 right-10 w-32 h-32 bg-gradient-to-r from-cyan-300/10 to-blue-300/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-4 left-10 w-40 h-40 bg-gradient-to-r from-blue-300/8 to-teal-300/8 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-6 sm:mb-8">
          <p className="font-poppins text-gray-600 dark:text-gray-400 text-sm sm:text-base">
            Trusted by innovative companies
          </p>
        </div>

        {/* Companies Infinite Slider */}
        <div className='relative h-[120px] w-full overflow-hidden'>
          <InfiniteSlider
            className='flex h-full w-full items-center'
            duration={30}
            gap={64}
          >
            {companies.map((company) => (
              <div
                key={company.id}
                className='flex w-40 items-center justify-center'
              >
                <img
                  src={company.logo}
                  alt={company.alt}
                  className="h-16 w-auto object-contain opacity-60 hover:opacity-100 transition-opacity duration-300 filter grayscale hover:grayscale-0"
                  loading="lazy"
                />
              </div>
            ))}
          </InfiniteSlider>
          <ProgressiveBlur
            className='pointer-events-none absolute top-0 left-0 h-full w-[200px]'
            direction='left'
            blurIntensity={1}
          />
          <ProgressiveBlur
            className='pointer-events-none absolute top-0 right-0 h-full w-[200px]'
            direction='right'
            blurIntensity={1}
          />
        </div>
      </div>
    </section>
  );
};

export default CompaniesSection;
