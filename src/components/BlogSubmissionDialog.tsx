import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Send, Globe, Building2, AlertCircle, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'

interface BlogPost {
  id: string
  title: string
  excerpt?: string
  status: string
  submission_status?: string
  organization_id?: string
  category?: string
}

interface BlogSubmissionDialogProps {
  isOpen: boolean
  onClose: () => void
  post: BlogPost | null
  onSubmissionSuccess: () => void
}

export const BlogSubmissionDialog: React.FC<BlogSubmissionDialogProps> = ({
  isOpen,
  onClose,
  post,
  onSubmissionSuccess
}) => {
  const [submissionMessage, setSubmissionMessage] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!post) return

    setIsSubmitting(true)
    try {
      // Call the database function to submit for approval
      const { data, error } = await supabase.rpc('submit_blog_for_approval', {
        post_id: post.id,
        submission_message: submissionMessage.trim() || null
      })

      if (error) {
        console.error('Submission error:', error)
        toast.error('Failed to submit blog post for approval')
        return
      }

      toast.success('Blog post submitted for MBI approval!', {
        description: 'We\'ll review your post and get back to you soon.'
      })

      onSubmissionSuccess()
      onClose()
      setSubmissionMessage('')
    } catch (error) {
      console.error('Error submitting blog post:', error)
      toast.error('An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    onClose()
    setSubmissionMessage('')
  }

  if (!post) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-500" />
            {post.category === 'testimonials' ? 'Submit to MBI Stories' : 'Submit to MBI Public Blog'}
          </DialogTitle>
          <DialogDescription>
            {post.category === 'testimonials'
              ? 'Submit your success story to be featured on the main Millennial Business Innovations testimonials page. Our team will review it for quality and relevance.'
              : 'Submit your blog post to be featured on the main Millennial Business Innovations blog. Our team will review it for quality and relevance.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Post Preview */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-2">{post.title}</h3>
                  {post.excerpt && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">
                      {post.excerpt}
                    </p>
                  )}
                </div>
                <Badge variant="outline" className="ml-4">
                  {post.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Submission Process Info */}
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-blue-900 dark:text-blue-100">
                  How the submission process works:
                </h4>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li className="flex items-center gap-2">
                    <Building2 className="h-3 w-3" />
                    Your post stays in your private workspace
                  </li>
                  <li className="flex items-center gap-2">
                    <Send className="h-3 w-3" />
                    We review it for quality and brand alignment
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3" />
                    If approved, it appears on millennialbusinessinnovations.com
                  </li>
                  <li className="flex items-center gap-2">
                    <Globe className="h-3 w-3" />
                    You get credit as the author with your organization mentioned
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Submission Message */}
          <div className="space-y-2">
            <Label htmlFor="submission-message">
              Message to MBI Team (Optional)
            </Label>
            <Textarea
              id="submission-message"
              placeholder={post.category === 'testimonials'
                ? "Tell us why this success story would be valuable for the MBI testimonials page, any special context, or questions you have..."
                : "Tell us why this post would be valuable for the MBI blog audience, any special context, or questions you have..."
              }
              value={submissionMessage}
              onChange={(e) => setSubmissionMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              This message will help our team understand your post better during review.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Submitting...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Send className="h-4 w-4" />
                  Submit for Review
                </div>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default BlogSubmissionDialog
