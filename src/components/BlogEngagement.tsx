import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { 
  Heart, 
  MessageCircle, 
  Bookmark, 
  Share2, 
  Twitter, 
  Facebook, 
  Linkedin,
  Link as LinkIcon,
  BookmarkCheck
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  toggleReaction, 
  getUserReaction, 
  toggleSave, 
  isPostSaved, 
  recordShare 
} from '@/lib/supabase'

interface BlogEngagementProps {
  postId: string
  postTitle: string
  postUrl: string
  reactionCount?: number
  commentCount?: number
  saveCount?: number
  shareCount?: number
  showCounts?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'horizontal' | 'vertical'
  onShare?: (platform: string) => void
}

const BlogEngagement: React.FC<BlogEngagementProps> = ({
  postId,
  postTitle,
  postUrl,
  reactionCount = 0,
  commentCount = 0,
  saveCount = 0,
  shareCount = 0,
  showCounts = true,
  size = 'md',
  variant = 'horizontal',
  onShare
}) => {
  const { user } = useAuth()
  const [hasReacted, setHasReacted] = useState(false)
  const [hasSaved, setHasSaved] = useState(false)
  const [localReactionCount, setLocalReactionCount] = useState(reactionCount)
  const [localSaveCount, setLocalSaveCount] = useState(saveCount)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user) {
      checkUserEngagement()
    }
  }, [user, postId])

  const checkUserEngagement = async () => {
    try {
      // Check if user has reacted (works for both authenticated and anonymous)
      const { data: reaction } = await getUserReaction(postId, user?.id)
      setHasReacted(!!reaction)

      // Check if user has saved (only for authenticated users)
      if (user) {
        const { data: saved } = await isPostSaved(postId, user.id)
        setHasSaved(saved)
      }
    } catch (error) {
      console.error('Error checking user engagement:', error)
    }
  }

  const handleReaction = async () => {
    setLoading(true)
    try {
      const { data, error } = await toggleReaction(postId, 'like')
      if (error) {
        toast.error('Failed to update reaction')
        return
      }

      const wasAdded = data?.action === 'added'
      setHasReacted(wasAdded)
      setLocalReactionCount(prev => wasAdded ? prev + 1 : prev - 1)

      if (wasAdded) {
        toast.success('❤️ Liked!')
        if (!user) {
          // Show a gentle reminder about signing up
          setTimeout(() => {
            toast.info('💡 Sign up to save your reactions and get personalized content!', {
              duration: 5000,
              action: {
                label: 'Sign Up',
                onClick: () => {
                  // You can trigger auth modal here
                  console.log('Open auth modal')
                }
              }
            })
          }, 2000)
        }
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!user) {
      toast.error('Please sign in to save posts')
      return
    }

    setLoading(true)
    try {
      const { data, error } = await toggleSave(postId)
      if (error) {
        toast.error('Failed to update save')
        return
      }

      const wasAdded = data?.action === 'added'
      setHasSaved(wasAdded)
      setLocalSaveCount(prev => wasAdded ? prev + 1 : prev - 1)
      
      toast.success(wasAdded ? '🔖 Saved!' : 'Removed from saved')
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleShare = async (shareType: string, url?: string) => {
    try {
      await recordShare(postId, shareType)

      // Call analytics tracking callback if provided
      if (onShare) {
        onShare(shareType)
      }

      if (url) {
        window.open(url, '_blank', 'width=600,height=400')
      } else if (shareType === 'link') {
        await navigator.clipboard.writeText(window.location.origin + postUrl)
        toast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      toast.error('Failed to share')
    }
  }

  const getShareUrls = () => {
    const fullUrl = encodeURIComponent(window.location.origin + postUrl)
    const text = encodeURIComponent(postTitle)
    
    return {
      twitter: `https://twitter.com/intent/tweet?text=${text}&url=${fullUrl}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${fullUrl}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${fullUrl}`
    }
  }

  const shareUrls = getShareUrls()

  const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'sm'
  const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5'
  const containerClass = variant === 'vertical' ? 'flex flex-col gap-2' : 'flex items-center gap-2'

  return (
    <div className={containerClass}>
      {/* Like/Heart Button */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size={buttonSize}
          onClick={handleReaction}
          disabled={loading}
          className={`${hasReacted ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'} transition-colors`}
        >
          <Heart className={`${iconSize} ${hasReacted ? 'fill-current' : ''}`} />
        </Button>
        {showCounts && localReactionCount > 0 && (
          <span className="text-sm text-gray-500">{localReactionCount}</span>
        )}
      </div>

      {/* Comment Button */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size={buttonSize}
          className="text-gray-400 hover:text-blue-500 transition-colors"
          onClick={() => {
            // Scroll to comments section or open comments modal
            const commentsSection = document.getElementById('comments-section')
            if (commentsSection) {
              commentsSection.scrollIntoView({ behavior: 'smooth' })
            }
          }}
        >
          <MessageCircle className={iconSize} />
        </Button>
        {showCounts && commentCount > 0 && (
          <span className="text-sm text-gray-500">{commentCount}</span>
        )}
      </div>

      {/* Save/Bookmark Button */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size={buttonSize}
          onClick={handleSave}
          disabled={loading}
          className={`${hasSaved ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-blue-500'} transition-colors`}
        >
          {hasSaved ? (
            <BookmarkCheck className={`${iconSize} fill-current`} />
          ) : (
            <Bookmark className={iconSize} />
          )}
        </Button>
        {showCounts && localSaveCount > 0 && (
          <span className="text-sm text-gray-500">{localSaveCount}</span>
        )}
      </div>

      {/* Share Button */}
      <div className="flex items-center gap-1">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size={buttonSize}
              className="text-gray-400 hover:text-green-500 transition-colors"
            >
              <Share2 className={iconSize} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => handleShare('link')}>
              <LinkIcon className="h-4 w-4 mr-2" />
              Copy link
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleShare('twitter', shareUrls.twitter)}>
              <Twitter className="h-4 w-4 mr-2" />
              Share on Twitter
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleShare('facebook', shareUrls.facebook)}>
              <Facebook className="h-4 w-4 mr-2" />
              Share on Facebook
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleShare('linkedin', shareUrls.linkedin)}>
              <Linkedin className="h-4 w-4 mr-2" />
              Share on LinkedIn
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {showCounts && shareCount > 0 && (
          <span className="text-sm text-gray-500">{shareCount}</span>
        )}
      </div>
    </div>
  )
}

export default BlogEngagement
