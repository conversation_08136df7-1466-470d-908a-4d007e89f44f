import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Star, Plus, Settings } from 'lucide-react'
import { Link } from 'react-router-dom'
import { getTestimonials, BlogPost, getDisplayAuthor } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { TestimonialsSection } from '@/components/ui/testimonials-with-marquee'
import { TestimonialAuthor } from '@/components/ui/testimonial-card'

const TestimonialSection = () => {
  const [testimonials, setTestimonials] = useState<Array<{
    author: TestimonialAuthor
    text: string
    href?: string
  }>>([])
  const [loading, setLoading] = useState(true)
  const { user, profile } = useAuth()

  const canCreateTestimonial = user && profile
  const isAdmin = profile && ['owner', 'super_admin', 'admin'].includes(profile.role)

  useEffect(() => {
    fetchTestimonials()
  }, [])

  // Fallback testimonials with professional data (no avatars - will be handled by component)
  const fallbackTestimonials = [
    {
      author: {
        name: "<PERSON>",
        handle: "@sarahchen_ceo"
      },
      text: "MBI transformed our startup idea into a fully functional SaaS platform in just 4 months. Six months post-launch, we've raised $2.3M in Series A funding.",
      href: "/testimonials"
    },
    {
      author: {
        name: "Marcus Rodriguez",
        handle: "@localeatsfound"
      },
      text: "Working with MBI was a game-changer. We went from concept to 500+ restaurant partners in 8 months.",
      href: "/testimonials"
    },
    {
      author: {
        name: "Jennifer Park",
        handle: "@healthsync_cto"
      },
      text: "The technical expertise at MBI is outstanding. They built our HIPAA-compliant patient portal with enterprise-grade security from day one.",
      href: "/testimonials"
    },
    {
      author: {
        name: "David Kim",
        handle: "@davidkim_tech"
      },
      text: "We've reduced our development time by 60% since working with MBI. Their attention to detail made all the difference.",
      href: "/testimonials"
    },
    {
      author: {
        name: "Lisa Thompson",
        handle: "@lisatech_mvp"
      },
      text: "Finally, a team that actually understands MVP development! We launched 3 months ahead of schedule and under budget.",
      href: "/testimonials"
    },
    {
      author: {
        name: "Alex Johnson",
        handle: "@alexj_startup"
      },
      text: "Our platform now serves thousands of users daily with 99.9% uptime thanks to MBI's full-stack expertise.",
      href: "/testimonials"
    }
  ]

  const fetchTestimonials = async () => {
    try {
      const { data, error } = await getTestimonials()

      if (error) {
        console.error('Error fetching testimonials:', error)
        setTestimonials(fallbackTestimonials)
        return
      }

      if (data && data.length > 0) {
        // Transform blog post testimonials to marquee format
        const transformedTestimonials = data.slice(0, 8).map((post: BlogPost) => {
          const displayAuthor = getDisplayAuthor(post)
          const testimonialText = extractTestimonialText(post.content)
          const company = extractCompany(post.content)

          // Create author object - only include avatar if it exists (Google avatar from user profile)
          const authorData: { name: string; handle: string; avatar?: string } = {
            name: displayAuthor.name,
            handle: company ? `@${company.toLowerCase().replace(/\s+/g, '')}` : `@${displayAuthor.name.toLowerCase().replace(/\s+/g, '')}`
          }

          // Only add avatar if the user has a Google avatar (avatar_url from their profile)
          if (displayAuthor.avatar_url) {
            authorData.avatar = displayAuthor.avatar_url
          }

          return {
            author: authorData,
            text: testimonialText.length > 180 ? testimonialText.substring(0, 180) + '...' : testimonialText, // Increased length for larger cards
            href: `/blog/${post.slug}`
          }
        })

        setTestimonials(transformedTestimonials.length > 0 ? transformedTestimonials : fallbackTestimonials)
      } else {
        setTestimonials(fallbackTestimonials)
      }
    } catch (error) {
      console.error('Error fetching testimonials:', error)
      setTestimonials(fallbackTestimonials)
    } finally {
      setLoading(false)
    }
  }

  // Extract testimonial text from blog post content
  const extractTestimonialText = (content: string | object): string => {
    const contentStr = typeof content === 'string' ? content : JSON.stringify(content)

    // Look for "My Experience with MBI" section
    const experienceMatch = contentStr.match(/<h2>My Experience with MBI<\/h2>\s*<p>([^<]+)/i)
    if (experienceMatch) {
      return experienceMatch[1].trim()
    }

    // Fallback to first paragraph
    const paragraphMatch = contentStr.match(/<p>([^<]+)<\/p>/i)
    if (paragraphMatch) {
      return paragraphMatch[1].trim()
    }

    return 'Great experience working with MBI!'
  }

  // Extract company from blog post content
  const extractCompany = (content: string | object): string => {
    const contentStr = typeof content === 'string' ? content : JSON.stringify(content)

    const companyMatch = contentStr.match(/<h2>About\s+([^<]+)<\/h2>/i)
    return companyMatch ? companyMatch[1].trim() : ''
  }

  if (loading) {
    return (
      <section className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-50 via-blue-50/30 to-cyan-50/30 dark:from-transparent dark:via-transparent dark:to-transparent">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }



  return (
    <section className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-50 via-blue-50/30 to-cyan-50/30 dark:from-transparent dark:via-transparent dark:to-transparent relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-r from-blue-300/10 to-cyan-300/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/10 to-teal-300/10 rounded-full blur-xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Section Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                Trusted by 50+ Startups
              </span>
            </div>
            <div className="flex items-center justify-center gap-4 mb-4">
              <h2 className="font-montserrat font-bold text-2xl sm:text-3xl md:text-4xl text-gray-900 dark:text-white">
                Success Stories That <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Speak Volumes</span>
              </h2>
              {isAdmin && (
                <Link to="/admin/blog?filter=testimonials">
                  <Button variant="ghost" size="sm" className="text-gray-500 hover:text-primary">
                    <Settings className="w-4 h-4" />
                  </Button>
                </Link>
              )}
            </div>
          </div>

          {/* Marquee Testimonials */}
          <div className="mb-6 sm:mb-8">
            <TestimonialsSection
              title=""
              description=""
              testimonials={testimonials}
              className="py-0"
            />
          </div>

          {/* Bottom CTA */}
          <div className="mt-8 sm:mt-12">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link to="/testimonials">
                <Button
                  variant="outline"
                  className="border-primary text-primary hover:bg-primary/10 hover:border-primary/50 font-poppins px-6 py-2"
                >
                  View All Success Stories
                </Button>
              </Link>

              {canCreateTestimonial && (
                <Link to="/admin/blog/testimonial">
                  <Button
                    className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 py-2"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Share Your Story
                  </Button>
                </Link>
              )}
            </div>

            {!user && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                <Link to="/blog" className="text-primary hover:underline">Join our blog community</Link> to share your success story with MBI
              </p>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
