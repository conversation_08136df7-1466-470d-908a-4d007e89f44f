import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { signUpWithEmail, signInWithGoogle } from '@/lib/supabase'
import { createQuoteRequest } from '@/lib/quote-requests'
import { toast } from 'sonner'
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Loader2, 
  Rocket,
  Users,
  Calendar,
  DollarSign,
  Mail,
  User,
  Sparkles
} from 'lucide-react'

interface QuoteRequestModalProps {
  isOpen: boolean
  onClose: () => void
}

interface ProjectData {
  // Step 1: Project Discovery
  projectType: string
  industry: string
  targetAudience: string
  
  // Step 2: Requirements
  keyFeatures: string[]
  integrations: string[]
  designPreference: string
  
  // Step 3: Timeline & Budget
  timeline: string
  budget: string
  teamInvolvement: string
  
  // Step 4: Contact
  name: string
  email: string
  company: string
  phone: string
  bestTimeToCall: string
  communicationPreference: string
  additionalInfo: string
  
  // Account creation
  wantsAccount: boolean
  password: string
}

const QuoteRequestModal: React.FC<QuoteRequestModalProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  
  const [projectData, setProjectData] = useState<ProjectData>({
    projectType: '',
    industry: '',
    targetAudience: '',
    keyFeatures: [],
    integrations: [],
    designPreference: '',
    timeline: '',
    budget: '',
    teamInvolvement: '',
    name: '',
    email: '',
    company: '',
    phone: '',
    bestTimeToCall: '',
    communicationPreference: '',
    additionalInfo: '',
    wantsAccount: false,
    password: ''
  })

  const totalSteps = 4
  const progress = (currentStep / totalSteps) * 100

  const projectTypes = [
    'MVP Development',
    'Web Application',
    'SaaS Platform',
    'E-commerce Site',
    'Mobile App',
    'API Development',
    'Database Design',
    'Other'
  ]

  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'E-commerce',
    'Education',
    'Real Estate',
    'Manufacturing',
    'Consulting',
    'Other'
  ]

  const budgetRanges = [
    '$10,000 - $25,000',
    '$25,000 - $50,000',
    '$50,000 - $100,000',
    '$100,000+',
    'Not sure yet'
  ]

  const timelines = [
    '1-2 months',
    '3-4 months',
    '5-6 months',
    '6+ months',
    'Flexible'
  ]

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleFeatureToggle = (feature: string) => {
    setProjectData(prev => ({
      ...prev,
      keyFeatures: prev.keyFeatures.includes(feature)
        ? prev.keyFeatures.filter(f => f !== feature)
        : [...prev.keyFeatures, feature]
    }))
  }

  const handleIntegrationToggle = (integration: string) => {
    setProjectData(prev => ({
      ...prev,
      integrations: prev.integrations.includes(integration)
        ? prev.integrations.filter(i => i !== integration)
        : [...prev.integrations, integration]
    }))
  }

  const handleSubmit = async () => {
    setLoading(true)
    try {
      // Submit quote request to Supabase
      const { data, error } = await createQuoteRequest({
        project_type: projectData.projectType,
        industry: projectData.industry,
        target_audience: projectData.targetAudience,
        key_features: projectData.keyFeatures,
        integrations: projectData.integrations,
        design_preference: projectData.designPreference,
        timeline: projectData.timeline,
        budget: projectData.budget,
        team_involvement: projectData.teamInvolvement,
        name: projectData.name,
        email: projectData.email,
        company: projectData.company,
        phone: projectData.phone,
        best_time_to_call: projectData.bestTimeToCall,
        communication_preference: projectData.communicationPreference,
        additional_info: projectData.additionalInfo,
        wants_account: projectData.wantsAccount
      })

      if (error) {
        throw new Error('Failed to submit quote request')
      }

      // If user wants an account, create it
      if (projectData.wantsAccount && projectData.password) {
        const { error } = await signUpWithEmail(
          projectData.email, 
          projectData.password, 
          projectData.name
        )
        
        if (error) {
          toast.error('Quote submitted but account creation failed. You can create an account later.')
        } else {
          toast.success('Quote submitted and account created! Check your email to verify.')
        }
      } else {
        toast.success('Quote request submitted successfully! We\'ll get back to you within 24 hours.')
      }

      setSubmitted(true)
    } catch (error) {
      toast.error('Failed to submit quote request. Please try again.')
      console.error('Quote submission error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignUp = async () => {
    setLoading(true)
    try {
      const { error } = await signInWithGoogle()
      if (error) {
        toast.error('Failed to sign up with Google')
      } else {
        setProjectData(prev => ({ ...prev, wantsAccount: true }))
        toast.success('Google account connected!')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const resetModal = () => {
    setCurrentStep(1)
    setSubmitted(false)
    setProjectData({
      projectType: '',
      industry: '',
      targetAudience: '',
      keyFeatures: [],
      integrations: [],
      designPreference: '',
      timeline: '',
      budget: '',
      teamInvolvement: '',
      name: '',
      email: '',
      company: '',
      phone: '',
      bestTimeToCall: '',
      communicationPreference: '',
      additionalInfo: '',
      wantsAccount: false,
      password: ''
    })
  }

  const handleClose = () => {
    resetModal()
    onClose()
  }

  if (submitted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Quote Request Submitted!
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              We'll review your project details and get back to you within 24 hours with a detailed proposal.
            </p>
            {projectData.wantsAccount && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                <Sparkles className="h-5 w-5 text-blue-500 mx-auto mb-2" />
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Your blog account has been created! Check your email to verify and start blogging.
                </p>
              </div>
            )}
            <Button onClick={handleClose} className="w-full">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-montserrat">
            Get Your Custom Quote
          </DialogTitle>
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-500 mb-2">
              <span>Step {currentStep} of {totalSteps}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </DialogHeader>

        <div className="mt-6">
          {/* Step 1: Project Discovery */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Rocket className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Tell us about your project</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Help us understand what you want to build
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>What type of project are you looking to build?</Label>
                  <Select 
                    value={projectData.projectType} 
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, projectType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select project type" />
                    </SelectTrigger>
                    <SelectContent>
                      {projectTypes.map(type => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>What industry is your business in?</Label>
                  <Select 
                    value={projectData.industry} 
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, industry: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      {industries.map(industry => (
                        <SelectItem key={industry} value={industry}>{industry}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Who is your target audience?</Label>
                  <Textarea
                    value={projectData.targetAudience}
                    onChange={(e) => setProjectData(prev => ({ ...prev, targetAudience: e.target.value }))}
                    placeholder="Describe your target users/customers..."
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Requirements */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Users className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Project Requirements</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  What features and integrations do you need?
                </p>
              </div>

              <div className="space-y-6">
                <div>
                  <Label className="text-base font-medium mb-3 block">Key Features (select all that apply)</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      'User Authentication',
                      'Payment Processing',
                      'Admin Dashboard',
                      'Mobile Responsive',
                      'Search Functionality',
                      'Real-time Updates',
                      'File Upload/Storage',
                      'Email Notifications',
                      'Analytics/Reporting',
                      'API Integration',
                      'Multi-language Support',
                      'Social Media Integration'
                    ].map(feature => (
                      <div key={feature} className="flex items-center space-x-2">
                        <Checkbox
                          id={feature}
                          checked={projectData.keyFeatures.includes(feature)}
                          onCheckedChange={() => handleFeatureToggle(feature)}
                        />
                        <Label htmlFor={feature} className="text-sm">{feature}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-base font-medium mb-3 block">Third-party Integrations</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      'Stripe/PayPal',
                      'Google Analytics',
                      'Mailchimp/SendGrid',
                      'Slack/Discord',
                      'Zapier',
                      'CRM Systems',
                      'Social Media APIs',
                      'Cloud Storage (AWS/GCP)'
                    ].map(integration => (
                      <div key={integration} className="flex items-center space-x-2">
                        <Checkbox
                          id={integration}
                          checked={projectData.integrations.includes(integration)}
                          onCheckedChange={() => handleIntegrationToggle(integration)}
                        />
                        <Label htmlFor={integration} className="text-sm">{integration}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Design Preference</Label>
                  <Select
                    value={projectData.designPreference}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, designPreference: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select design style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="modern-minimal">Modern & Minimal</SelectItem>
                      <SelectItem value="corporate-professional">Corporate & Professional</SelectItem>
                      <SelectItem value="creative-bold">Creative & Bold</SelectItem>
                      <SelectItem value="match-existing">Match Existing Brand</SelectItem>
                      <SelectItem value="not-sure">Not Sure Yet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Timeline & Budget */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Calendar className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Timeline & Budget</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Help us plan your project timeline and investment
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Desired Timeline</Label>
                  <Select
                    value={projectData.timeline}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, timeline: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select timeline" />
                    </SelectTrigger>
                    <SelectContent>
                      {timelines.map(timeline => (
                        <SelectItem key={timeline} value={timeline}>{timeline}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Budget Range</Label>
                  <Select
                    value={projectData.budget}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, budget: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget range" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetRanges.map(budget => (
                        <SelectItem key={budget} value={budget}>{budget}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Team Involvement</Label>
                  <Select
                    value={projectData.teamInvolvement}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, teamInvolvement: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="How involved will your team be?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-service">Full-service (we handle everything)</SelectItem>
                      <SelectItem value="collaborative">Collaborative (regular input/feedback)</SelectItem>
                      <SelectItem value="hands-on">Hands-on (active participation)</SelectItem>
                      <SelectItem value="not-sure">Not sure yet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Contact & Account Creation */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Mail className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Contact Information</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  How can we reach you with your custom quote?
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Full Name *</Label>
                  <Input
                    value={projectData.name}
                    onChange={(e) => setProjectData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Your full name"
                    required
                  />
                </div>

                <div>
                  <Label>Email Address *</Label>
                  <Input
                    type="email"
                    value={projectData.email}
                    onChange={(e) => setProjectData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <Label>Company/Organization</Label>
                  <Input
                    value={projectData.company}
                    onChange={(e) => setProjectData(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="Company name (optional)"
                  />
                </div>

                <div>
                  <Label>Phone Number</Label>
                  <Input
                    value={projectData.phone}
                    onChange={(e) => setProjectData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="(*************"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Best Time to Call</Label>
                  <Select
                    value={projectData.bestTimeToCall}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, bestTimeToCall: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select time preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="morning">Morning (9AM - 12PM)</SelectItem>
                      <SelectItem value="afternoon">Afternoon (12PM - 5PM)</SelectItem>
                      <SelectItem value="evening">Evening (5PM - 8PM)</SelectItem>
                      <SelectItem value="anytime">Anytime</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Preferred Communication</Label>
                  <Select
                    value={projectData.communicationPreference}
                    onValueChange={(value) => setProjectData(prev => ({ ...prev, communicationPreference: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="How should we contact you?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="phone">Phone Call</SelectItem>
                      <SelectItem value="video">Video Call</SelectItem>
                      <SelectItem value="any">Any method</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Additional Information</Label>
                <Textarea
                  value={projectData.additionalInfo}
                  onChange={(e) => setProjectData(prev => ({ ...prev, additionalInfo: e.target.value }))}
                  placeholder="Anything else you'd like us to know about your project?"
                  rows={3}
                />
              </div>

              {/* Account Creation Section */}
              <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-3">
                    <Sparkles className="h-6 w-6 text-blue-500 mt-1" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎁 Bonus: Free Blog Platform Access
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300 text-sm mb-4">
                        Create an account to track your project progress and get access to our free business blogging platform with AI assistance!
                      </p>

                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="wantsAccount"
                            checked={projectData.wantsAccount}
                            onCheckedChange={(checked) => setProjectData(prev => ({ ...prev, wantsAccount: !!checked }))}
                          />
                          <Label htmlFor="wantsAccount" className="text-sm text-blue-800 dark:text-blue-200">
                            Yes, create my free account
                          </Label>
                        </div>

                        {projectData.wantsAccount && (
                          <div className="space-y-3">
                            <div>
                              <Label className="text-sm">Create Password</Label>
                              <Input
                                type="password"
                                value={projectData.password}
                                onChange={(e) => setProjectData(prev => ({ ...prev, password: e.target.value }))}
                                placeholder="Choose a secure password"
                                className="bg-white dark:bg-gray-800"
                              />
                            </div>

                            <div className="text-center">
                              <p className="text-xs text-blue-600 dark:text-blue-400 mb-2">Or sign up with:</p>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={handleGoogleSignUp}
                                disabled={loading}
                                className="w-full bg-white hover:bg-gray-50"
                              >
                                <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Continue with Google
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <Button 
              variant="outline" 
              onClick={handleBack}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            
            {currentStep < totalSteps ? (
              <Button 
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && (!projectData.projectType || !projectData.industry)) ||
                  (currentStep === 2 && projectData.keyFeatures.length === 0) ||
                  (currentStep === 3 && (!projectData.timeline || !projectData.budget))
                }
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit}
                disabled={loading || !projectData.name || !projectData.email}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Submit Quote Request
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default QuoteRequestModal
