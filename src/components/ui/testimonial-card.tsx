import { cn } from "@/lib/utils"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Quote } from "lucide-react"

export interface TestimonialAuthor {
  name: string
  handle: string
  avatar?: string // Make avatar optional
}

export interface TestimonialCardProps {
  author: TestimonialAuthor
  text: string
  href?: string
  className?: string
}

export function TestimonialCard({ 
  author,
  text,
  href,
  className
}: TestimonialCardProps) {
  const Card = href ? 'a' : 'div'
  
  return (
    <Card
      {...(href ? { href } : {})}
      className={cn(
        "flex flex-col rounded-lg border-t",
        "bg-gradient-to-b from-muted/50 to-muted/10",
        "dark:from-white/10 dark:to-white/5", // Better dark mode gradient
        "border-border/40 dark:border-white/20", // Improved border visibility
        "backdrop-blur-sm", // Add subtle backdrop blur
        "p-6 text-start sm:p-8", // Increased padding
        "hover:from-muted/60 hover:to-muted/20",
        "dark:hover:from-white/15 dark:hover:to-white/8", // Better dark mode hover
        "max-w-[400px] sm:max-w-[420px]", // Increased max width
        "min-h-[200px]", // Added minimum height
        "transition-colors duration-300",
        "shadow-sm dark:shadow-white/5", // Subtle shadow for depth
        className
      )}
    >
      {/* Quote Icon */}
      <div className="flex justify-start mb-4">
        <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
          <Quote className="w-4 h-4 text-white" />
        </div>
      </div>

      {/* Testimonial Text with Quote Marks */}
      <blockquote className="text-base sm:text-lg text-muted-foreground dark:text-gray-300 leading-relaxed mb-6 italic">
        "{text}"
      </blockquote>

      {/* Author Info - Only show if avatar exists */}
      {author.avatar ? (
        <div className="flex items-center gap-3 mt-auto">
          <Avatar className="h-12 w-12">
            <AvatarImage src={author.avatar} alt={author.name} />
            <AvatarFallback>
              {author.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start">
            <h3 className="text-md font-semibold leading-none dark:text-white">
              {author.name}
            </h3>
            <p className="text-sm text-muted-foreground dark:text-gray-400">
              {author.handle}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-start mt-auto">
          <h3 className="text-md font-semibold leading-none dark:text-white">
            {author.name}
          </h3>
          <p className="text-sm text-muted-foreground dark:text-gray-400">
            {author.handle}
          </p>
        </div>
      )}
    </Card>
  )
}
