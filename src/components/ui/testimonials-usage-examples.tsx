import React from 'react'
import { TestimonialsSection } from "@/components/ui/testimonials-with-marquee"
import TestimonialsMarqueeSection from '@/components/TestimonialsMarqueeSection'

// Example 1: Basic usage with static data
export function BasicTestimonialsExample() {
  const testimonials = [
    {
      author: {
        name: "<PERSON>",
        handle: "@sarah<PERSON>_ceo",
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face"
      },
      text: "MBI transformed our startup idea into a fully functional SaaS platform in just 4 months.",
      href: "/testimonials"
    },
    {
      author: {
        name: "<PERSON>", 
        handle: "@localeatsfound",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
      },
      text: "Working with <PERSON><PERSON> was a game-changer. We went from concept to 500+ restaurant partners.",
      href: "/testimonials"
    }
  ]

  return (
    <TestimonialsSection
      title="What Our Clients Say"
      description="Real feedback from real founders"
      testimonials={testimonials}
    />
  )
}

// Example 2: Integrated with your existing testimonial system
export function IntegratedTestimonialsExample() {
  return (
    <TestimonialsMarqueeSection
      title="Success Stories from Our Community"
      description="Join hundreds of entrepreneurs who have transformed their ideas into successful digital products"
      className="bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800"
    />
  )
}

// Example 3: Custom styling and faster animation
export function FastMarqueeExample() {
  const testimonials = [
    {
      author: {
        name: "Jennifer Park",
        handle: "@healthsync_cto", 
        avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face"
      },
      text: "The technical expertise at MBI is outstanding. HIPAA-compliant from day one.",
      href: "/case-studies"
    },
    {
      author: {
        name: "David Kim",
        handle: "@davidkim_tech",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
      },
      text: "We've reduced our development time by 60% since working with MBI.",
      href: "/case-studies"
    }
  ]

  return (
    <div className="[--duration:20s]"> {/* Faster animation */}
      <TestimonialsSection
        title="Lightning Fast Results"
        description="See what happens when you work with the right team"
        testimonials={testimonials}
        className="py-16 bg-blue-50 dark:bg-blue-950/20"
      />
    </div>
  )
}

// Example 4: Compact version for smaller sections
export function CompactTestimonialsExample() {
  const testimonials = [
    {
      author: {
        name: "Lisa T.",
        handle: "@startup_mvp",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
      },
      text: "Launched 3 months ahead of schedule!",
    },
    {
      author: {
        name: "Alex J.",
        handle: "@tech_founder",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"
      },
      text: "99.9% uptime with thousands of users.",
    }
  ]

  return (
    <TestimonialsSection
      title="Quick Wins"
      description="Fast results that matter"
      testimonials={testimonials}
      className="py-8"
    />
  )
}

// Usage Instructions Component
export function TestimonialsUsageGuide() {
  return (
    <div className="max-w-4xl mx-auto p-8 space-y-8">
      <div className="prose dark:prose-invert">
        <h1>Testimonials with Marquee - Usage Guide</h1>
        
        <h2>Components Available</h2>
        <ul>
          <li><code>TestimonialsSection</code> - Basic marquee testimonials with static data</li>
          <li><code>TestimonialsMarqueeSection</code> - Integrated with your existing testimonial system</li>
          <li><code>TestimonialCard</code> - Individual testimonial card component</li>
        </ul>

        <h2>Props</h2>
        <h3>TestimonialsSection</h3>
        <ul>
          <li><code>title: string</code> - Section title</li>
          <li><code>description: string</code> - Section description</li>
          <li><code>testimonials: Array</code> - Array of testimonial objects</li>
          <li><code>className?: string</code> - Additional CSS classes</li>
        </ul>

        <h3>Testimonial Object Structure</h3>
        <pre><code>{`{
  author: {
    name: string,
    handle: string,
    avatar: string
  },
  text: string,
  href?: string
}`}</code></pre>

        <h2>Customization</h2>
        <ul>
          <li>Animation speed: Add <code>[--duration:20s]</code> class to parent</li>
          <li>Gap between cards: Modify <code>[--gap:1rem]</code> class</li>
          <li>Pause on hover: Built-in with <code>group-hover:[animation-play-state:paused]</code></li>
        </ul>

        <h2>Integration Examples</h2>
        <p>See the examples above for different use cases and styling options.</p>
      </div>
    </div>
  )
}

export default {
  BasicTestimonialsExample,
  IntegratedTestimonialsExample,
  FastMarqueeExample,
  CompactTestimonialsExample,
  TestimonialsUsageGuide
}
