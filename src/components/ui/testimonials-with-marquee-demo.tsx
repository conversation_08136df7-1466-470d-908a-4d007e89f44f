import { TestimonialsSection } from "@/components/ui/testimonials-with-marquee"

// Real testimonial data based on your database schema
const testimonials = [
  {
    author: {
      name: "<PERSON>",
      handle: "@sarah<PERSON>_ceo",
      avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face"
    },
    text: "MBI transformed our startup idea into a fully functional SaaS platform in just 4 months. Their team didn't just build what we asked for—they anticipated our needs and built for scale. Six months post-launch, we've raised $2.3M in Series A funding.",
    href: "/testimonials"
  },
  {
    author: {
      name: "<PERSON>",
      handle: "@localeatsfound",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    text: "Working with MBI was a game-changer. They took our complex restaurant management idea and turned it into an intuitive platform that our clients actually love using. We went from concept to 500+ restaurant partners in 8 months.",
    href: "/testimonials"
  },
  {
    author: {
      name: "<PERSON>",
      handle: "@healthsync_cto",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face"
    },
    text: "The technical expertise at MBI is outstanding. They built our HIPAA-compliant patient portal with enterprise-grade security from day one. No shortcuts, no compromises—just solid, scalable architecture that grows with us.",
    href: "/testimonials"
  },
  {
    author: {
      name: "David Kim",
      handle: "@davidkim_tech",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    text: "The API integration is flawless. We've reduced our development time by 60% since implementing this solution. MBI's attention to detail and scalable architecture made all the difference.",
    href: "/testimonials"
  },
  {
    author: {
      name: "Lisa Thompson",
      handle: "@lisatech_mvp",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    },
    text: "Finally, a team that actually understands MVP development! The speed and quality of delivery exceeded our expectations. We launched 3 months ahead of schedule and under budget.",
    href: "/testimonials"
  },
  {
    author: {
      name: "Alex Johnson",
      handle: "@alexj_startup",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"
    },
    text: "MBI's full-stack expertise is unmatched. They handled everything from UI/UX design to backend architecture and deployment. Our platform now serves thousands of users daily with 99.9% uptime.",
    href: "/testimonials"
  }
]

export function TestimonialsSectionDemo() {
  return (
    <TestimonialsSection
      title="Trusted by founders worldwide"
      description="Join hundreds of entrepreneurs who have transformed their ideas into successful digital products with MBI"
      testimonials={testimonials}
    />
  )
}

export default TestimonialsSectionDemo
