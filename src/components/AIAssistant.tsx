import React, { useState, useRef, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import {
  MessageCircle,
  X,
  Send,
  Loader2,
  Sparkles,
  HelpCircle,
  Zap,
  FileText,
  Settings,
  AlertCircle,
  Maximize2,
  Minimize2,
  ExternalLink,
  Sidebar,
  Monitor,
  ArrowRight
} from 'lucide-react'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { aiService } from '@/lib/aiService'
import { supabase } from '@/lib/supabase'
import AIAssistantDetached from './AIAssistantDetached'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
  canCreateContent?: boolean
  contentType?: 'blog_generation' | 'workflow_help' | 'chat_help' | 'code_assistance'
  originalContent?: string
}

type ViewMode = 'compact' | 'sidebar' | 'fullscreen' | 'detached'

const AIAssistant = () => {
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const location = useLocation()
  const navigate = useNavigate()
  const [isOpen, setIsOpen] = useState(false)
  const [viewMode, setViewMode] = useState<ViewMode>('compact')
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Get contextual welcome message based on current page
  const getWelcomeMessage = () => {
    const path = location.pathname
    let contextualHelp = ''

    if (path.includes('/blog')) {
      contextualHelp = '\n\n🎯 I see you\'re working on blog content! I can help you write, edit, or brainstorm ideas.'
    } else if (path.includes('/automations') || path.includes('/workflow')) {
      contextualHelp = '\n\n⚡ Working on workflows? I can help you set up automations, triggers, and actions.'
    } else if (path.includes('/email-integrations')) {
      contextualHelp = '\n\n📧 Setting up email integrations? I can guide you through connecting your email providers.'
    } else if (path.includes('/credits')) {
      contextualHelp = '\n\n💳 Checking your usage? I can explain your credit limits and help you optimize usage.'
    } else if (path.includes('/dashboard')) {
      contextualHelp = '\n\n🏠 Welcome to your workspace! I can help you create content, set up workflows, or navigate features.'
    } else if (path.includes('/admin')) {
      contextualHelp = '\n\n⚙️ In admin mode? I can help with content management, user administration, and system settings.'
    }

    return `Hi ${profile?.full_name || 'there'}! 👋 I'm your AI assistant. I can help you with:\n\n• Creating blog posts and content\n• Setting up workflows and automations\n• Navigating the platform\n• General questions about MBI features${contextualHelp}\n\nWhat would you like help with today?`
  }

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setMessages([{
        id: '1',
        content: getWelcomeMessage(),
        isUser: false,
        timestamp: new Date()
      }])
    }
  }, [isOpen, profile?.full_name, location.pathname])

  const quickActions = [
    { icon: FileText, label: 'Write a blog post', prompt: 'Write a complete blog post about digital marketing trends with title, introduction, main points, and conclusion' },
    { icon: Zap, label: 'Create workflow', prompt: 'Create a workflow template for automated email welcome series for new customers' },
    { icon: Settings, label: 'Platform help', prompt: 'How do I' },
    { icon: HelpCircle, label: 'General question', prompt: 'I have a question about' }
  ]

  const handleQuickAction = (prompt: string) => {
    setInputValue(prompt + ' ')
  }

  // Enhanced content creation functions
  const createBlogPost = async (aiContent: string, extractedTitle: string) => {
    if (!user || !currentOrganization) return

    try {
      // Parse AI content to extract structured blog data
      const blogData = parseAIBlogContent(aiContent, extractedTitle)

      const { data, error } = await supabase
        .from('blog_posts')
        .insert({
          title: blogData.title,
          slug: generateSlug(blogData.title),
          content: blogData.content, // Rich HTML content
          excerpt: blogData.excerpt,
          featured_image: blogData.featured_image,
          thumbnail_url: blogData.thumbnail_url,
          status: 'draft',
          author_id: user.id,
          author_display: 'real_name',
          category: blogData.category,
          tags: blogData.tags,
          is_featured: false,
          display_order: 0,
          organization_id: currentOrganization.id,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error

      // Show success message with link
      const successMessage: Message = {
        id: Date.now().toString(),
        content: `✅ Complete blog post "${blogData.title}" created!\n\n📝 [Edit & Publish →](/admin/blog/edit/${data.id})\n\n🎯 Ready to publish with:\n• SEO-optimized title\n• Rich formatted content\n• Auto-generated excerpt\n• Relevant tags`,
        isUser: false,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, successMessage])

      toast.success('Complete blog post created! Ready to publish.')

      return data.id
    } catch (error) {
      console.error('Error creating blog post:', error)
      toast.error('Failed to create blog post')
      return null
    }
  }

  // Helper function to generate URL-friendly slug
  const generateSlug = (title: string) => {
    const baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/(^-|-$)/g, '')
      .trim()

    const timestamp = Date.now()
    return `${baseSlug}-${timestamp}`
  }

  // Parse AI content into structured blog data
  const parseAIBlogContent = (aiContent: string, fallbackTitle: string) => {
    const lines = aiContent.split('\n').filter(line => line.trim())

    // Extract title (look for # heading or "Title:" pattern)
    let title = fallbackTitle
    const titleMatch = aiContent.match(/^#\s+(.+)$/m) || aiContent.match(/^Title:\s*(.+)$/mi)
    if (titleMatch) {
      title = titleMatch[1].trim()
    }

    // Convert markdown-style content to HTML
    let htmlContent = aiContent
      .replace(/^#\s+(.+)$/gm, '<h1>$1</h1>')
      .replace(/^##\s+(.+)$/gm, '<h2>$1</h2>')
      .replace(/^###\s+(.+)$/gm, '<h3>$1</h3>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/^-\s+(.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(?!<[h|u|l])(.+)$/gm, '<p>$1</p>')

    // Clean up HTML
    htmlContent = htmlContent
      .replace(/<p><\/p>/g, '')
      .replace(/<p>(<[h|u])/g, '$1')
      .replace(/(<\/[h|u]>)<\/p>/g, '$1')

    // Generate excerpt (first 150 characters of clean text)
    const cleanText = htmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
    const excerpt = cleanText.length > 150 ? cleanText.substring(0, 150) + '...' : cleanText

    // Extract or generate tags based on content
    const tags = extractTagsFromContent(aiContent)

    // Determine category based on content
    const category = determineCategoryFromContent(aiContent)

    return {
      title,
      content: htmlContent,
      excerpt,
      featured_image: null,
      thumbnail_url: null,
      category,
      tags
    }
  }

  // Extract relevant tags from content
  const extractTagsFromContent = (content: string): string[] => {
    const lowerContent = content.toLowerCase()
    const possibleTags = [
      'ai', 'artificial intelligence', 'machine learning', 'technology', 'business',
      'marketing', 'digital marketing', 'seo', 'content marketing', 'social media',
      'automation', 'workflow', 'productivity', 'innovation', 'strategy',
      'entrepreneurship', 'startup', 'growth', 'analytics', 'data',
      'customer experience', 'user experience', 'design', 'development'
    ]

    return possibleTags.filter(tag =>
      lowerContent.includes(tag.toLowerCase())
    ).slice(0, 5) // Limit to 5 tags
  }

  // Determine category from content
  const determineCategoryFromContent = (content: string): string => {
    const lowerContent = content.toLowerCase()

    if (lowerContent.includes('ai') || lowerContent.includes('artificial intelligence')) return 'technology'
    if (lowerContent.includes('marketing') || lowerContent.includes('seo')) return 'marketing'
    if (lowerContent.includes('business') || lowerContent.includes('strategy')) return 'business'
    if (lowerContent.includes('tutorial') || lowerContent.includes('how to')) return 'tutorial'

    return 'blog' // default category
  }

  const createWorkflowTemplate = async (aiContent: string) => {
    if (!user || !currentOrganization) return

    try {
      // Parse AI content to create a complete workflow template
      const workflowData = parseAIWorkflowContent(aiContent)

      const { data, error } = await supabase
        .from('workflows')
        .insert({
          name: workflowData.name,
          description: workflowData.description,
          nodes: workflowData.nodes,
          active: false, // Start as inactive template
          is_template: true, // Mark as template
          created_by: user.id,
          organization_id: currentOrganization.id, // Associate with current organization
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error

      // Show success message with detailed info
      const successMessage: Message = {
        id: Date.now().toString(),
        content: `⚡ Complete workflow "${workflowData.name}" created!\n\n🔧 [Configure & Activate →](/admin/automations/edit/${data.id})\n\n🎯 Ready to use with:\n• ${workflowData.nodeCount} pre-configured steps\n• Smart trigger conditions\n• Email templates included\n• Ready to activate`,
        isUser: false,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, successMessage])

      toast.success('Complete workflow template created! Ready to activate.')

      return data.id
    } catch (error) {
      console.error('Error creating workflow:', error)
      toast.error('Failed to create workflow template')
      return null
    }
  }

  // Parse AI content into complete workflow template
  const parseAIWorkflowContent = (aiContent: string) => {
    const lowerContent = aiContent.toLowerCase()

    // Determine workflow type and create appropriate template
    if (lowerContent.includes('welcome') || lowerContent.includes('onboard')) {
      return createWelcomeWorkflowTemplate(aiContent)
    } else if (lowerContent.includes('email') && lowerContent.includes('series')) {
      return createEmailSeriesTemplate(aiContent)
    } else if (lowerContent.includes('notification') || lowerContent.includes('alert')) {
      return createNotificationTemplate(aiContent)
    } else if (lowerContent.includes('follow up') || lowerContent.includes('followup')) {
      return createFollowUpTemplate(aiContent)
    } else {
      return createGenericWorkflowTemplate(aiContent)
    }
  }

  // Create welcome/onboarding workflow template (Zapier-style)
  const createWelcomeWorkflowTemplate = (aiContent: string) => {
    const name = extractWorkflowName(aiContent) || 'Customer Welcome Series'
    const description = extractWorkflowDescription(aiContent) || 'Automated welcome sequence for new customers'

    const nodes = [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 200 },
        data: {
          triggerType: 'user_signup',
          label: 'New User Signup',
          description: 'Triggers when a new user signs up or registers'
        },
        connections: ['delay-1']
      },
      {
        id: 'delay-1',
        type: 'action',
        position: { x: 380, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 5 minutes',
          description: 'Brief delay to ensure user setup is complete',
          config: {
            delayMs: 300000,
            delayType: 'fixed',
            delayUnit: 'minutes',
            delayValue: 5
          }
        },
        connections: ['email-1']
      },
      {
        id: 'email-1',
        type: 'action',
        position: { x: 660, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Welcome Email',
          description: 'Send personalized welcome message',
          config: {
            to: '{{ user.email }}',
            from: '{{ organization.email }}',
            subject: 'Welcome to {{ organization.name }}! 🎉',
            body: generateWelcomeEmailBody(aiContent),
            template: 'welcome',
            personalization: {
              userName: '{{ user.name }}',
              organizationName: '{{ organization.name }}',
              loginUrl: '{{ app.loginUrl }}'
            }
          }
        },
        connections: ['delay-2']
      },
      {
        id: 'delay-2',
        type: 'action',
        position: { x: 940, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 24 hours',
          description: 'Give user time to explore before follow-up',
          config: {
            delayMs: 86400000,
            delayType: 'fixed',
            delayUnit: 'hours',
            delayValue: 24
          }
        },
        connections: ['email-2']
      },
      {
        id: 'email-2',
        type: 'action',
        position: { x: 1220, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Getting Started Guide',
          description: 'Send helpful onboarding resources',
          config: {
            to: '{{ user.email }}',
            from: '{{ organization.email }}',
            subject: 'Your getting started guide is here! 📚',
            body: generateGettingStartedEmailBody(aiContent),
            template: 'onboarding',
            personalization: {
              userName: '{{ user.name }}',
              dashboardUrl: '{{ app.dashboardUrl }}',
              supportUrl: '{{ app.supportUrl }}'
            }
          }
        },
        connections: ['delay-3']
      },
      {
        id: 'delay-3',
        type: 'action',
        position: { x: 1500, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 3 days',
          description: 'Allow time for user engagement',
          config: {
            delayMs: 259200000,
            delayType: 'fixed',
            delayUnit: 'days',
            delayValue: 3
          }
        },
        connections: ['email-3']
      },
      {
        id: 'email-3',
        type: 'action',
        position: { x: 1780, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Check-in Email',
          description: 'Follow up on user experience',
          config: {
            to: '{{ user.email }}',
            from: '{{ organization.email }}',
            subject: 'How are things going? 💬',
            body: generateCheckInEmailBody(aiContent),
            template: 'checkin',
            personalization: {
              userName: '{{ user.name }}',
              feedbackUrl: '{{ app.feedbackUrl }}'
            }
          }
        },
        connections: []
      }
    ]

    return { name, description, nodes, nodeCount: nodes.length }
  }

  // Create email series template (Zapier-style)
  const createEmailSeriesTemplate = (aiContent: string) => {
    const name = extractWorkflowName(aiContent) || 'Email Marketing Series'
    const description = extractWorkflowDescription(aiContent) || 'Automated email sequence for engagement and nurturing'

    const nodes = [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 200 },
        data: {
          triggerType: 'contact_added',
          label: 'Contact Added to List',
          description: 'Triggers when contact is added to email list'
        },
        connections: ['email-1']
      },
      {
        id: 'email-1',
        type: 'action',
        position: { x: 380, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Email 1: Welcome & Introduction',
          description: 'Introduce your brand and set expectations',
          config: {
            to: '{{ contact.email }}',
            from: '{{ organization.email }}',
            subject: 'Welcome to our community! 🌟',
            body: generateSeriesEmailBody(aiContent, 1),
            template: 'series_intro',
            personalization: {
              contactName: '{{ contact.name }}',
              listName: '{{ list.name }}',
              unsubscribeUrl: '{{ app.unsubscribeUrl }}'
            }
          }
        },
        connections: ['delay-1']
      },
      {
        id: 'delay-1',
        type: 'action',
        position: { x: 660, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 3 days',
          description: 'Optimal spacing for engagement',
          config: {
            delayMs: 259200000,
            delayType: 'fixed',
            delayUnit: 'days',
            delayValue: 3
          }
        },
        connections: ['email-2']
      },
      {
        id: 'email-2',
        type: 'action',
        position: { x: 940, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Email 2: Value & Education',
          description: 'Provide valuable content and insights',
          config: {
            to: '{{ contact.email }}',
            from: '{{ organization.email }}',
            subject: 'Here\'s something valuable for you 💎',
            body: generateSeriesEmailBody(aiContent, 2),
            template: 'series_value',
            personalization: {
              contactName: '{{ contact.name }}',
              resourceUrl: '{{ content.resourceUrl }}'
            }
          }
        },
        connections: ['delay-2']
      },
      {
        id: 'delay-2',
        type: 'action',
        position: { x: 1220, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 5 days',
          description: 'Allow time to digest content',
          config: {
            delayMs: 432000000,
            delayType: 'fixed',
            delayUnit: 'days',
            delayValue: 5
          }
        },
        connections: ['email-3']
      },
      {
        id: 'email-3',
        type: 'action',
        position: { x: 1500, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Email 3: Social Proof',
          description: 'Share testimonials and success stories',
          config: {
            to: '{{ contact.email }}',
            from: '{{ organization.email }}',
            subject: 'See what others are saying... 🗣️',
            body: generateSeriesEmailBody(aiContent, 3),
            template: 'series_social_proof',
            personalization: {
              contactName: '{{ contact.name }}',
              testimonialsUrl: '{{ content.testimonialsUrl }}'
            }
          }
        },
        connections: ['delay-3']
      },
      {
        id: 'delay-3',
        type: 'action',
        position: { x: 1780, y: 200 },
        data: {
          actionType: 'delay',
          label: 'Wait 7 days',
          description: 'Build anticipation for final email',
          config: {
            delayMs: 604800000,
            delayType: 'fixed',
            delayUnit: 'days',
            delayValue: 7
          }
        },
        connections: ['email-4']
      },
      {
        id: 'email-4',
        type: 'action',
        position: { x: 2060, y: 200 },
        data: {
          actionType: 'send_email',
          label: 'Email 4: Call to Action',
          description: 'Drive conversion with clear CTA',
          config: {
            to: '{{ contact.email }}',
            from: '{{ organization.email }}',
            subject: 'Ready to take the next step? 🚀',
            body: generateSeriesEmailBody(aiContent, 4),
            template: 'series_cta',
            personalization: {
              contactName: '{{ contact.name }}',
              ctaUrl: '{{ content.ctaUrl }}',
              offerCode: '{{ offer.code }}'
            }
          }
        },
        connections: []
      }
    ]

    return { name, description, nodes, nodeCount: nodes.length }
  }

  // Create notification template (Zapier-style)
  const createNotificationTemplate = (aiContent: string) => {
    const name = extractWorkflowName(aiContent) || 'Smart Notification System'
    const description = extractWorkflowDescription(aiContent) || 'Automated notification and alert system'

    const nodes = [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 200 },
        data: {
          triggerType: 'event_based',
          label: 'Event Trigger',
          description: 'Triggers when specific events occur in the system'
        },
        connections: ['condition-1']
      },
      {
        id: 'condition-1',
        type: 'condition',
        position: { x: 380, y: 200 },
        data: {
          conditionType: 'status_check',
          label: 'Check Priority Level',
          description: 'Filter notifications by importance',
          field: 'priority',
          operator: 'equals',
          value: 'high',
          conditions: [
            { field: 'status', operator: 'equals', value: 'active' },
            { field: 'priority', operator: 'in', value: ['high', 'urgent'] }
          ]
        },
        connections: ['notification-1', 'email-1']
      },
      {
        id: 'notification-1',
        type: 'action',
        position: { x: 660, y: 120 },
        data: {
          actionType: 'send_notification',
          label: 'In-App Notification',
          description: 'Send immediate in-app notification',
          config: {
            type: 'in_app',
            title: 'Important Update 🔔',
            message: generateNotificationMessage(aiContent),
            priority: 'high',
            recipients: ['{{ event.userId }}', '{{ event.assignedTo }}'],
            actionUrl: '{{ event.detailsUrl }}',
            actionText: 'View Details'
          }
        },
        connections: []
      },
      {
        id: 'email-1',
        type: 'action',
        position: { x: 660, y: 280 },
        data: {
          actionType: 'send_email',
          label: 'Email Alert',
          description: 'Send email notification for important events',
          config: {
            to: '{{ event.notificationEmail }}',
            from: '{{ organization.email }}',
            subject: '🚨 Alert: {{ event.title }}',
            body: generateAlertEmailBody(aiContent),
            priority: 'high',
            template: 'alert',
            personalization: {
              eventTitle: '{{ event.title }}',
              eventTime: '{{ event.timestamp }}',
              actionUrl: '{{ event.actionUrl }}'
            }
          }
        },
        connections: ['delay-1']
      },
      {
        id: 'delay-1',
        type: 'action',
        position: { x: 940, y: 280 },
        data: {
          actionType: 'delay',
          label: 'Wait 15 minutes',
          description: 'Delay before follow-up check',
          config: {
            delayMs: 900000,
            delayType: 'fixed',
            delayUnit: 'minutes',
            delayValue: 15
          }
        },
        connections: ['condition-2']
      },
      {
        id: 'condition-2',
        type: 'condition',
        position: { x: 1220, y: 280 },
        data: {
          conditionType: 'status_check',
          label: 'Check if Resolved',
          description: 'Check if issue was addressed',
          field: 'status',
          operator: 'not_equals',
          value: 'resolved'
        },
        connections: ['escalation-1']
      },
      {
        id: 'escalation-1',
        type: 'action',
        position: { x: 1500, y: 280 },
        data: {
          actionType: 'send_email',
          label: 'Escalation Email',
          description: 'Escalate to management if unresolved',
          config: {
            to: '{{ organization.adminEmail }}',
            from: '{{ organization.email }}',
            subject: '⚠️ ESCALATION: {{ event.title }}',
            body: generateEscalationEmailBody(aiContent),
            priority: 'urgent',
            template: 'escalation'
          }
        },
        connections: []
      }
    ]

    return { name, description, nodes, nodeCount: nodes.length }
  }

  // Create follow-up template
  const createFollowUpTemplate = (aiContent: string) => {
    const name = extractWorkflowName(aiContent) || 'Follow-up Sequence'
    const description = extractWorkflowDescription(aiContent) || 'Automated follow-up workflow'

    const nodes = [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 100 },
        data: {
          triggerType: 'time_based',
          label: 'Time-based Trigger',
          description: 'Triggers after specific time period'
        },
        connections: ['email-1']
      },
      {
        id: 'email-1',
        type: 'action',
        position: { x: 350, y: 100 },
        data: {
          actionType: 'send_email',
          label: 'Follow-up Email',
          config: {
            to: '{{ contact.email }}',
            subject: 'Following up on our conversation',
            body: generateFollowUpEmailBody(aiContent)
          }
        },
        connections: []
      }
    ]

    return { name, description, nodes, nodeCount: nodes.length }
  }

  // Create generic workflow template
  const createGenericWorkflowTemplate = (aiContent: string) => {
    const name = extractWorkflowName(aiContent) || 'Custom Workflow'
    const description = extractWorkflowDescription(aiContent) || 'AI-generated workflow template'

    const nodes = [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 100 },
        data: {
          triggerType: 'manual',
          label: 'Manual Start',
          description: 'Start workflow manually'
        },
        connections: ['action-1']
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 350, y: 100 },
        data: {
          actionType: 'send_email',
          label: 'Send Email',
          config: {
            to: '{{ recipient.email }}',
            subject: 'Workflow Notification',
            body: generateGenericEmailBody(aiContent)
          }
        },
        connections: []
      }
    ]

    return { name, description, nodes, nodeCount: nodes.length }
  }

  // Helper functions for extracting workflow data
  const extractWorkflowName = (content: string): string | null => {
    const nameMatch = content.match(/(?:workflow|automation)(?:\s+name)?:\s*(.+)/i) ||
                     content.match(/^#\s+(.+)$/m) ||
                     content.match(/create\s+(?:a\s+)?(?:workflow|automation)\s+(?:for\s+)?(.+?)(?:\.|$)/i)
    return nameMatch ? nameMatch[1].trim().replace(/[^\w\s-]/g, '').trim() : null
  }

  const extractWorkflowDescription = (content: string): string | null => {
    const descMatch = content.match(/(?:description|purpose):\s*(.+)/i)
    if (descMatch) return descMatch[1].trim()

    // Extract first sentence as description
    const sentences = content.split(/[.!?]+/)
    return sentences[0] ? sentences[0].trim() + '.' : null
  }

  // Email content generators
  const generateWelcomeEmailBody = (aiContent: string): string => {
    return `Hi {{ user.name || "there" }},

Welcome to our platform! We're excited to have you on board.

${extractKeyPoints(aiContent)}

Here's what you can expect:
• Access to all platform features
• 24/7 customer support
• Regular updates and improvements

If you have any questions, don't hesitate to reach out.

Best regards,
The Team`
  }

  const generateGettingStartedEmailBody = (aiContent: string): string => {
    return `Hi {{ user.name }},

Ready to get the most out of your account? Here's your getting started guide:

${extractActionableSteps(aiContent)}

Quick Start Tips:
• Complete your profile setup
• Explore the main dashboard
• Try creating your first project

Need help? Our support team is here for you.

Best regards,
The Team`
  }

  const generateCheckInEmailBody = (aiContent: string): string => {
    return `Hi {{ user.name }},

It's been a few days since you joined us, and I wanted to personally check in to see how things are going!

${extractKeyPoints(aiContent)}

Here's what you can do next:
• Explore your dashboard and try out the main features
• Check out our help center for tips and tutorials
• Join our community forum to connect with other users

Is there anything specific you'd like help with? Just reply to this email - I read every response personally.

Looking forward to hearing from you!

Best regards,
{{ sender.name }}
{{ organization.name }} Team

P.S. If you have 2 minutes, I'd love to hear about your experience so far: {{ app.feedbackUrl }}`
  }

  const generateSeriesEmailBody = (aiContent: string, emailNumber: number): string => {
    const emailTemplates = {
      1: `Hi {{ contact.name }},

Welcome to our community! I'm thrilled you've decided to join us.

${extractKeyPoints(aiContent)}

Over the next few days, I'll be sharing some valuable insights and resources to help you get the most out of your experience with us.

What to expect:
• Practical tips and strategies
• Exclusive content and resources
• Real success stories from our community
• Direct access to our team for support

Ready to get started? Let's do this together!

Best regards,
The Team`,

      2: `Hi {{ contact.name }},

I hope you're settling in well! Today I want to share something really valuable with you.

${extractRelevantContent(aiContent, 2)}

Here's what successful people in our community do:
• They take action on what they learn
• They engage with our content regularly
• They don't hesitate to ask questions

Want to dive deeper? Check out this resource: {{ content.resourceUrl }}

Keep up the great momentum!

Best regards,
The Team`,

      3: `Hi {{ contact.name }},

You know what I love most about our community? The incredible success stories.

${extractRelevantContent(aiContent, 3)}

Here's what some of our members are saying:

"This has completely transformed how I approach my business!" - Sarah M.

"The strategies actually work - I've seen real results in just weeks." - Mike T.

"Best decision I made this year was joining this community." - Lisa K.

Want to see more testimonials? Visit: {{ content.testimonialsUrl }}

Your success story could be next!

Best regards,
The Team`,

      4: `Hi {{ contact.name }},

We've covered a lot of ground together over the past few weeks, and I hope you've found real value in what we've shared.

${extractRelevantContent(aiContent, 4)}

Now it's time to take the next step. I've put together something special just for our community members:

🎯 Exclusive offer: {{ offer.code }}
🚀 Ready to level up? {{ content.ctaUrl }}

This opportunity won't last long, and I'd hate for you to miss out.

Questions? Just reply to this email - I'm here to help!

Best regards,
The Team`
    }

    return emailTemplates[emailNumber as keyof typeof emailTemplates] || emailTemplates[1]
  }

  const generateNotificationMessage = (aiContent: string): string => {
    const keyPoint = extractKeyPoints(aiContent)
    return keyPoint || 'Important update: Please check your account for new information.'
  }

  const generateFollowUpEmailBody = (aiContent: string): string => {
    return `Hi {{ contact.name }},

I wanted to follow up on our recent conversation.

${extractKeyPoints(aiContent)}

Is there anything specific I can help you with? I'm here to support your success.

Best regards,
{{ sender.name }}`
  }

  const generateAlertEmailBody = (aiContent: string): string => {
    return `ALERT NOTIFICATION

Event: {{ event.title }}
Time: {{ event.timestamp }}
Priority: HIGH

${extractKeyPoints(aiContent)}

IMMEDIATE ACTION REQUIRED:
${extractActionableSteps(aiContent)}

For more details, click here: {{ event.actionUrl }}

This is an automated alert from your notification system.
If you believe this is an error, please contact support.

---
{{ organization.name }} Alert System`
  }

  const generateEscalationEmailBody = (aiContent: string): string => {
    return `ESCALATION NOTICE

An important issue requires management attention:

Event: {{ event.title }}
Original Alert Time: {{ event.timestamp }}
Escalation Time: {{ current.timestamp }}
Status: UNRESOLVED after 15 minutes

${extractKeyPoints(aiContent)}

REQUIRED ACTIONS:
${extractActionableSteps(aiContent)}

This issue was automatically escalated because it remained unresolved after the initial alert.

View full details: {{ event.actionUrl }}

---
Automated Escalation System
{{ organization.name }}`
  }

  const generateGenericEmailBody = (aiContent: string): string => {
    return `Hi {{ recipient.name }},

${extractKeyPoints(aiContent)}

${extractActionableSteps(aiContent)}

If you have any questions, please don't hesitate to reach out.

Best regards,
The Team`
  }

  // Content extraction helpers
  const extractKeyPoints = (content: string): string => {
    const lines = content.split('\n').filter(line => line.trim())
    const keyLines = lines.slice(0, 3).join('\n\n')
    return keyLines || 'Thank you for your interest in our services.'
  }

  const extractActionableSteps = (content: string): string => {
    const stepMatches = content.match(/(?:step|action|task)\s*\d*[:.]\s*(.+)/gi)
    if (stepMatches) {
      return stepMatches.map((step, index) => `${index + 1}. ${step.replace(/^(?:step|action|task)\s*\d*[:.]\s*/i, '')}`).join('\n')
    }
    return 'Follow the instructions provided to get started.'
  }

  const extractRelevantContent = (content: string, emailNumber: number): string => {
    const paragraphs = content.split('\n\n').filter(p => p.trim())
    const relevantParagraph = paragraphs[emailNumber - 1] || paragraphs[0] || content.substring(0, 200)
    return relevantParagraph.trim()
  }

  // Handlers for creating content from AI messages
  const handleCreateBlogFromMessage = async (message: Message) => {
    if (!message.originalContent) return

    // Extract title from AI response
    const content = message.originalContent
    const titleMatch = content.match(/^#\s+(.+)$/m) || content.match(/^Title:\s*(.+)$/mi)
    const title = titleMatch ? titleMatch[1].trim() : 'AI Generated Blog Post'

    await createBlogPost(content, title)
  }

  const handleCreateWorkflowFromMessage = async (message: Message) => {
    if (!message.originalContent) return

    await createWorkflowTemplate(message.originalContent)
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !user || !currentOrganization) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isUser: true,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      // Get contextual information based on current page
      const context = aiService.getContextualPrompt(location.pathname)

      // Determine message type and model
      const messageType = determineMessageType(userMessage.content, location.pathname)
      const selectedModel = aiService.selectBestModel(userMessage.content, messageType)

      // Call the real AI service
      const response = await aiService.sendChatMessage({
        message: userMessage.content,
        context,
        userId: user.id,
        organizationId: currentOrganization.id,
        model: selectedModel,
        messageType
      })

      if (response.success && response.response) {
        // Update last used model
        if (response.modelUsed) {
          const modelNames = {
            'gemini-flash': 'Gemini Flash',
            'deepseek-r1': 'DeepSeek R1 (Free)',
            'qwen-plus': 'Qwen 72B (Free)'
          }
          setLastUsedModel(modelNames[response.modelUsed as keyof typeof modelNames] || 'Auto')
        }

        // Check if this is content that can be created directly
        const messageType = determineMessageType(userMessage.content, location.pathname)
        const canCreateContent = messageType === 'blog_generation' || messageType === 'workflow_help'

        let aiResponseContent = response.response

        // Add action prompt for content creation
        if (canCreateContent && response.chargedCredits) {
          if (messageType === 'blog_generation') {
            aiResponseContent += '\n\n🎯 **Ready to create this blog post?**'
          } else if (messageType === 'workflow_help') {
            aiResponseContent += '\n\n⚡ **Ready to create this workflow template?**'
          }
        }

        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: aiResponseContent,
          isUser: false,
          timestamp: new Date(),
          canCreateContent: canCreateContent && !!response.chargedCredits,
          contentType: messageType,
          originalContent: response.response
        }

        setMessages(prev => [...prev, aiResponse])

        // Show credit usage info if provided
        if (response.creditsUsed && response.creditsLimit) {
          const remaining = response.creditsLimit - response.creditsUsed
          if (remaining <= 5) {
            toast.warning(`AI Credits: ${remaining} remaining out of ${response.creditsLimit}`)
          }
        }
      } else {
        // Handle errors
        const errorMessage = response.error || 'Failed to get AI response'

        if (errorMessage.includes('credits limit exceeded')) {
          toast.error('AI credits limit exceeded. Please upgrade your plan or wait for next month.')
        } else if (errorMessage.includes('Authentication required')) {
          toast.error('Please log in to use the AI assistant.')
        } else {
          toast.error(errorMessage)
        }

        // Add error message to chat
        const errorResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: `❌ ${errorMessage}`,
          isUser: false,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, errorResponse])
      }
    } catch (error) {
      console.error('AI Chat Error:', error)
      toast.error('Network error. Please check your connection and try again.')

      // Add error message to chat
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: '❌ Network error. Please check your connection and try again.',
        isUser: false,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorResponse])
    } finally {
      setIsLoading(false)
    }
  }

  // Check if user has AI credits available
  const [creditsInfo, setCreditsInfo] = useState<{
    available: boolean
    used: number
    limit: number
    remaining: number
  } | null>(null)

  // Track last used model
  const [lastUsedModel, setLastUsedModel] = useState<string>('Auto')

  // Mobile detection and auto-adjust view mode
  useEffect(() => {
    const checkMobile = () => {
      const isMobile = window.innerWidth < 640 // sm breakpoint

      // Auto-switch to fullscreen on mobile when opening
      if (isMobile && isOpen && viewMode === 'compact') {
        setViewMode('fullscreen')
      }

      // Auto-switch sidebar to fullscreen on mobile
      if (isMobile && viewMode === 'sidebar') {
        setViewMode('fullscreen')
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [isOpen, viewMode])

  // Check credits when component opens
  useEffect(() => {
    if (isOpen && currentOrganization) {
      aiService.checkCreditsAvailable(currentOrganization.id).then(setCreditsInfo)
    }
  }, [isOpen, currentOrganization])

  // Helper function to determine message type
  const determineMessageType = (message: string, pathname: string): 'chat_help' | 'blog_generation' | 'workflow_help' | 'code_assistance' => {
    const lowerMessage = message.toLowerCase()

    // Check both admin and dashboard routes for context
    if (pathname.includes('/blog') || lowerMessage.includes('blog') || lowerMessage.includes('write') || lowerMessage.includes('content')) {
      return 'blog_generation'
    } else if (pathname.includes('/automations') || pathname.includes('/workflow') || lowerMessage.includes('workflow') || lowerMessage.includes('automation')) {
      return 'workflow_help'
    } else if (lowerMessage.includes('code') || lowerMessage.includes('api') || lowerMessage.includes('integration')) {
      return 'code_assistance'
    }

    return 'chat_help'
  }

  // View mode switching functions
  const switchViewMode = (mode: ViewMode) => {
    setViewMode(mode)

    // For detached mode, open in new window
    if (mode === 'detached') {
      openDetachedWindow()
      setIsOpen(false)
    }
  }

  const openDetachedWindow = () => {
    // For now, we'll show the detached component in the same window
    // In a real implementation, you'd want to render this in a new window
    setViewMode('detached')
  }

  // Cycling view mode functions
  const cycleViewMode = () => {
    const modes: ViewMode[] = ['compact', 'sidebar', 'fullscreen', 'detached']
    const currentIndex = modes.indexOf(viewMode)
    const nextIndex = (currentIndex + 1) % modes.length
    switchViewMode(modes[nextIndex])
  }

  const getViewModeIcon = () => {
    switch (viewMode) {
      case 'compact':
        return <MessageCircle className="h-4 w-4" />
      case 'sidebar':
        return <Sidebar className="h-4 w-4" />
      case 'fullscreen':
        return <Maximize2 className="h-4 w-4" />
      case 'detached':
        return <ExternalLink className="h-4 w-4" />
      default:
        return <MessageCircle className="h-4 w-4" />
    }
  }

  const getViewModeTooltip = () => {
    switch (viewMode) {
      case 'compact':
        return 'Current: Compact • Click for Sidebar'
      case 'sidebar':
        return 'Current: Sidebar • Click for Fullscreen'
      case 'fullscreen':
        return 'Current: Fullscreen • Click for Detached'
      case 'detached':
        return 'Current: Detached • Click for Compact'
      default:
        return 'Click to cycle view modes'
    }
  }

  // Get container styles based on view mode with responsive design
  const getContainerStyles = () => {
    switch (viewMode) {
      case 'compact':
        return 'fixed bottom-4 right-4 z-50 w-80 h-96 sm:w-96 sm:h-[500px] sm:bottom-6 sm:right-6'
      case 'sidebar':
        return 'fixed top-0 right-0 z-50 w-full h-full sm:w-80 md:w-96'
      case 'fullscreen':
        return 'fixed inset-0 z-50 w-full h-full'
      default:
        return 'fixed bottom-4 right-4 z-50 w-80 h-96 sm:w-96 sm:h-[500px] sm:bottom-6 sm:right-6'
    }
  }

  // Get rounded corners based on view mode with responsive considerations
  const getRoundedStyles = () => {
    switch (viewMode) {
      case 'compact':
        return 'rounded-lg'
      case 'sidebar':
        return 'sm:rounded-l-lg' // No rounding on mobile (full width)
      case 'fullscreen':
        return ''
      default:
        return 'rounded-lg'
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Keyboard shortcuts for view modes
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault()
            switchViewMode('compact')
            break
          case '2':
            e.preventDefault()
            switchViewMode('sidebar')
            break
          case '3':
            e.preventDefault()
            switchViewMode('fullscreen')
            break
          case '4':
            e.preventDefault()
            switchViewMode('detached')
            break
          case 'Escape':
            if (viewMode === 'fullscreen') {
              e.preventDefault()
              switchViewMode('compact')
            }
            break
        }
      }
    }

    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown)
      return () => window.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, viewMode])

  // Show for all authenticated users on admin and dashboard pages
  if (!user || (!location.pathname.startsWith('/admin') && !location.pathname.startsWith('/dashboard'))) return null

  return (
    <>
      {/* Floating AI Assistant Button */}
      <div className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-50">
        {!isOpen && (
          <Button
            onClick={() => setIsOpen(true)}
            className="h-12 w-12 sm:h-14 sm:w-14 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 shadow-lg hover:shadow-xl transition-all duration-300 group"
          >
            <div className="relative">
              <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              <div className="absolute -top-1 -right-1 w-2 h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </Button>
        )}
      </div>

      {/* AI Assistant Chat Window */}
      {isOpen && viewMode !== 'detached' && (
        <div className={cn(
          getContainerStyles(),
          'bg-white dark:bg-gray-800 shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col',
          getRoundedStyles()
        )}>
          {/* Header */}
          <div className={cn(
            "flex items-center justify-between p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-500 to-blue-500",
            viewMode === 'compact' && 'rounded-t-lg',
            viewMode === 'sidebar' && 'sm:rounded-tl-lg'
          )}>
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 text-white flex-shrink-0" />
              <div className="flex flex-col min-w-0">
                <h3 className="font-semibold text-white text-sm sm:text-base truncate">AI Assistant</h3>
                {viewMode !== 'compact' && (
                  <span className="text-xs text-white/80 capitalize hidden sm:block">
                    {viewMode} Mode
                  </span>
                )}
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs hidden sm:inline-flex">
                {lastUsedModel}
              </Badge>
              {creditsInfo && (
                <Badge
                  variant="secondary"
                  className={cn(
                    "bg-white/20 text-white border-white/30 text-xs",
                    creditsInfo.remaining <= 5 && "bg-red-500/20 border-red-300"
                  )}
                >
                  {creditsInfo.remaining}/{creditsInfo.limit}
                </Badge>
              )}
            </div>

            {/* Single Cycling View Mode Control */}
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={cycleViewMode}
                className="text-white hover:bg-white/20 p-1"
                title={getViewModeTooltip()}
              >
                {getViewModeIcon()}
              </Button>

              {/* Help Button - Desktop only */}
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 p-1 hidden sm:flex"
                title="Keyboard Shortcuts: Ctrl+1 (Compact), Ctrl+2 (Sidebar), Ctrl+3 (Fullscreen), Ctrl+4 (Detached), Esc (Exit Fullscreen)"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>

              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-white/20 p-1 ml-1 sm:ml-2"
                title="Close"
              >
                <X className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </div>
          </div>

          {/* Quick Actions */}
          {messages.length <= 1 && (
            <div className="p-2 sm:p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAction(action.prompt)}
                    className="flex items-center gap-1 text-xs h-8 justify-start"
                  >
                    <action.icon className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{action.label}</span>
                  </Button>
                ))}
              </div>

              {/* Credit System Info */}
              <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                <div className="text-xs text-blue-800 dark:text-blue-200">
                  <div className="font-medium mb-1">💡 Smart Credit System</div>
                  <div className="space-y-1">
                    <div>• <strong>Free:</strong> Support questions, platform help</div>
                    <div>• <strong>1 Credit:</strong> Blog posts, workflow templates</div>
                    <div>• <strong>✨ New:</strong> Direct creation with edit links!</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Messages */}
          <div className={cn(
            "flex-1 overflow-y-auto space-y-3 sm:space-y-4",
            viewMode === 'fullscreen' ? 'p-4 sm:p-8' : 'p-3 sm:p-4'
          )}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex flex-col gap-2',
                  message.isUser ? 'items-end' : 'items-start'
                )}
              >
                <div
                  className={cn(
                    'rounded-lg p-2 sm:p-3 whitespace-pre-wrap',
                    // Responsive text sizing and max width
                    viewMode === 'fullscreen'
                      ? 'max-w-[85%] sm:max-w-[70%] text-sm sm:text-base'
                      : 'max-w-[90%] sm:max-w-[80%] text-xs sm:text-sm',
                    message.isUser
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                  )}
                >
                  {message.content}
                </div>

                {/* Action buttons for content creation */}
                {message.canCreateContent && !message.isUser && (
                  <div className="flex gap-2 mt-1">
                    {message.contentType === 'blog_generation' && (
                      <Button
                        size="sm"
                        onClick={() => handleCreateBlogFromMessage(message)}
                        className="bg-green-500 hover:bg-green-600 text-white text-xs"
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        Create Blog Post
                      </Button>
                    )}
                    {message.contentType === 'workflow_help' && (
                      <Button
                        size="sm"
                        onClick={() => handleCreateWorkflowFromMessage(message)}
                        className="bg-purple-500 hover:bg-purple-600 text-white text-xs"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        Create Workflow
                      </Button>
                    )}
                  </div>
                )}
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-2 sm:p-3 flex items-center gap-2">
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                  <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Thinking...</span>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className={cn(
            "border-t border-gray-200 dark:border-gray-700",
            viewMode === 'fullscreen' ? 'p-4 sm:p-8' : 'p-3 sm:p-4'
          )}>
            {/* Credits warning */}
            {creditsInfo && creditsInfo.remaining <= 5 && (
              <div className="mb-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                <div className="flex items-center gap-2 text-xs sm:text-sm text-yellow-800 dark:text-yellow-200">
                  <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="truncate">
                    {creditsInfo.remaining === 0
                      ? 'No AI credits remaining this month'
                      : `Only ${creditsInfo.remaining} AI credits remaining`
                    }
                  </span>
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  creditsInfo?.remaining === 0
                    ? "No credits remaining..."
                    : "Ask me anything..."
                }
                disabled={isLoading || creditsInfo?.remaining === 0}
                className={cn(
                  "flex-1",
                  viewMode === 'fullscreen' ? 'text-base h-12' : 'text-sm h-9 sm:h-10'
                )}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading || creditsInfo?.remaining === 0}
                size={viewMode === 'fullscreen' ? 'default' : 'sm'}
                className={cn(
                  "bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600",
                  viewMode === 'fullscreen' ? 'px-4' : 'px-2 sm:px-3'
                )}
              >
                <Send className={cn(
                  viewMode === 'fullscreen' ? 'h-5 w-5' : 'h-3 w-3 sm:h-4 sm:w-4'
                )} />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Detached Mode */}
      {isOpen && viewMode === 'detached' && (
        <AIAssistantDetached
          onClose={() => {
            setViewMode('compact')
          }}
          initialMessages={messages}
        />
      )}
    </>
  )
}

export default AIAssistant
