import React, { useState, useRef, useEffect } from 'react'
import { Send, Loader2, <PERSON><PERSON>les, X, Minimize2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import { useLocation } from 'react-router-dom'
import { aiService } from '@/lib/aiService'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface DetachedAIProps {
  onClose: () => void
  initialMessages?: Message[]
}

const AIAssistantDetached: React.FC<DetachedAIProps> = ({ onClose, initialMessages = [] }) => {
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const location = useLocation()
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [lastUsedModel, setLastUsedModel] = useState<string>('Auto')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Initialize with welcome message if no initial messages
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([{
        id: '1',
        content: `Hi ${profile?.full_name || 'there'}! 👋 Welcome to the detached AI Assistant.\n\nI can help you with:\n• Creating blog posts and content\n• Setting up workflows and automations\n• Navigating the platform\n• General questions about MBI features\n\nWhat would you like help with today?`,
        isUser: false,
        timestamp: new Date()
      }])
    }
  }, [profile?.full_name])

  const determineMessageType = (message: string, pathname: string): 'chat_help' | 'blog_generation' | 'workflow_help' | 'code_assistance' => {
    const lowerMessage = message.toLowerCase()
    
    if (pathname.includes('/blog') || lowerMessage.includes('blog') || lowerMessage.includes('write') || lowerMessage.includes('content')) {
      return 'blog_generation'
    } else if (pathname.includes('/automations') || lowerMessage.includes('workflow') || lowerMessage.includes('automation')) {
      return 'workflow_help'
    } else if (lowerMessage.includes('code') || lowerMessage.includes('api') || lowerMessage.includes('integration')) {
      return 'code_assistance'
    }
    
    return 'chat_help'
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !user || !currentOrganization) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isUser: true,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const context = aiService.getContextualPrompt(location.pathname)
      const messageType = determineMessageType(userMessage.content, location.pathname)
      const selectedModel = aiService.selectBestModel(userMessage.content, messageType)

      const response = await aiService.sendChatMessage({
        message: userMessage.content,
        context,
        userId: user.id,
        organizationId: currentOrganization.id,
        model: selectedModel,
        messageType
      })

      if (response.success && response.response) {
        if (response.modelUsed) {
          const modelNames = {
            'gemini-flash': 'Gemini Flash',
            'deepseek-r1': 'DeepSeek R1 (Free)',
            'qwen-plus': 'Qwen 72B (Free)'
          }
          setLastUsedModel(modelNames[response.modelUsed as keyof typeof modelNames] || 'Auto')
        }

        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: response.response,
          isUser: false,
          timestamp: new Date()
        }

        setMessages(prev => [...prev, aiResponse])

        if (response.creditsUsed && response.creditsLimit) {
          const remaining = response.creditsLimit - response.creditsUsed
          if (remaining <= 5) {
            toast.warning(`AI Credits: ${remaining} remaining out of ${response.creditsLimit}`)
          }
        }
      } else {
        const errorMessage = response.error || 'Failed to get AI response'
        toast.error(errorMessage)

        const errorResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: `❌ ${errorMessage}`,
          isUser: false,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, errorResponse])
      }
    } catch (error) {
      console.error('AI Chat Error:', error)
      toast.error('Network error. Please check your connection and try again.')

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: '❌ Network error. Please check your connection and try again.',
        isUser: false,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorResponse])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="fixed inset-0 z-50 bg-white dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-500 to-blue-500">
        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
          <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-white flex-shrink-0" />
          <div className="min-w-0">
            <h1 className="text-lg sm:text-xl font-bold text-white truncate">AI Assistant</h1>
            <p className="text-xs sm:text-sm text-white/80">Detached Mode</p>
          </div>
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs hidden sm:inline-flex">
            {lastUsedModel}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20"
            title="Minimize to main window"
          >
            <Minimize2 className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.close()}
            className="text-white hover:bg-white/20"
            title="Close window"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 sm:p-8 space-y-4 sm:space-y-6 max-w-4xl mx-auto w-full">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex',
              message.isUser ? 'justify-end' : 'justify-start'
            )}
          >
            <div
              className={cn(
                'max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 text-sm sm:text-base whitespace-pre-wrap',
                message.isUser
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
              )}
            >
              {message.content}
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 sm:p-4 flex items-center gap-2 sm:gap-3">
              <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 animate-spin" />
              <span className="text-sm sm:text-base text-gray-600 dark:text-gray-400">Thinking...</span>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 sm:p-8 border-t border-gray-200 dark:border-gray-700 max-w-4xl mx-auto w-full">
        <div className="flex gap-2 sm:gap-4">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything..."
            disabled={isLoading}
            className="flex-1 text-sm sm:text-base p-3 sm:p-4 h-10 sm:h-12"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            size="default"
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 px-3 sm:px-6"
          >
            <Send className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default AIAssistantDetached
