import React from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Building2, 
  ChevronDown, 
  Crown, 
  Users, 
  Zap,
  Sparkles
} from 'lucide-react'

const OrganizationSwitcher: React.FC = () => {
  const { user } = useAuth()
  const {
    currentOrganization,
    organizations,
    switchOrganization,
    loading
  } = useOrganization()

  if (loading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse">
        <Building2 className="h-4 w-4" />
        <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    )
  }

  // If no organizations and still loading, show loading state
  if ((!currentOrganization || organizations.length === 0) && loading) {
    return (
      <Button
        variant="outline"
        disabled
        className="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
      >
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          Setting up workspace...
        </span>
      </Button>
    )
  }

  // If no organizations after loading is complete, show manual setup (fallback)
  if (!currentOrganization || organizations.length === 0) {
    const handleSetupOrganization = async () => {
      try {
        toast.loading('Setting up your workspace...')

        if (!user?.id) {
          throw new Error('User not found')
        }

        console.log('Manual organization creation for user:', user.id)

        // Create a personal organization for the user
        const { data: newOrg, error: orgError } = await supabase
          .from('organizations')
          .insert({
            name: `${user?.email?.split('@')[0] || 'My'} Workspace`,
            slug: `user-${user?.id}`,
            subscription_plan: 'free',
            workflow_credits_limit: 100,
            ai_credits_limit: 50,
            workflow_credits_used: 0,
            ai_credits_used: 0
          })
          .select()
          .single()

        if (orgError) {
          console.error('Organization creation error:', orgError)
          throw orgError
        }

        console.log('Organization created:', newOrg)

        // Add user as owner
        const { error: memberError } = await supabase
          .from('organization_members')
          .insert({
            organization_id: newOrg.id,
            user_id: user.id,
            role: 'owner',
            joined_at: new Date().toISOString(),
            is_active: true
          })

        if (memberError) {
          console.error('Member creation error:', memberError)
          throw memberError
        }

        console.log('User added as organization owner')
        toast.success('Workspace created successfully!')

        // Refresh organizations instead of full page reload
        setTimeout(() => {
          window.location.reload()
        }, 1000)

      } catch (error) {
        console.error('Error setting up organization:', error)
        toast.error(`Failed to setup organization: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return (
      <Button
        variant="outline"
        onClick={handleSetupOrganization}
        className="flex items-center gap-2 px-3 py-2 bg-orange-100 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 hover:bg-orange-200 dark:hover:bg-orange-900/30"
      >
        <Building2 className="h-4 w-4 text-orange-600" />
        <span className="text-sm text-orange-700 dark:text-orange-400">
          Setup Workspace
        </span>
      </Button>
    )
  }

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'enterprise':
        return <Crown className="h-3 w-3 text-yellow-500" />
      case 'pro':
        return <Sparkles className="h-3 w-3 text-purple-500" />
      case 'basic':
        return <Zap className="h-3 w-3 text-blue-500" />
      default:
        return <Users className="h-3 w-3 text-gray-500" />
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'enterprise':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'pro':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
      case 'basic':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-2 max-w-xs justify-between"
        >
          <div className="flex items-center gap-2 min-w-0">
            <Building2 className="h-4 w-4 flex-shrink-0" />
            <span className="truncate text-sm font-medium">
              {currentOrganization.name}
            </span>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            <Badge 
              variant="secondary" 
              className={`text-xs ${getPlanColor(currentOrganization.subscription_plan)}`}
            >
              {getPlanIcon(currentOrganization.subscription_plan)}
              <span className="ml-1 capitalize">
                {currentOrganization.subscription_plan}
              </span>
            </Badge>
            <ChevronDown className="h-3 w-3" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="start" className="w-80">
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {organizations.map((org) => (
          <DropdownMenuItem
            key={org.id}
            onClick={() => switchOrganization(org.id)}
            className={`flex items-center justify-between p-3 ${
              org.id === currentOrganization.id ? 'bg-accent' : ''
            }`}
          >
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <div className="font-medium truncate">{org.name}</div>
                <div className="text-xs text-gray-500 truncate">{org.slug}</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 flex-shrink-0">
              <Badge 
                variant="secondary" 
                className={`text-xs ${getPlanColor(org.subscription_plan)}`}
              >
                {getPlanIcon(org.subscription_plan)}
                <span className="ml-1 capitalize">
                  {org.subscription_plan}
                </span>
              </Badge>
              
              {org.id === currentOrganization.id && (
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              )}
            </div>
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator />
        
        {/* Current Organization Credits */}
        <div className="p-3 bg-gray-50 dark:bg-gray-800/50">
          <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            Current Credits
          </div>
          
          <div className="space-y-2">
            {/* Workflow Credits */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-blue-500" />
                <span>Workflows</span>
              </div>
              <span className="font-mono">
                {currentOrganization.workflow_credits_used}/
                {currentOrganization.workflow_credits_limit === -1 
                  ? '∞' 
                  : currentOrganization.workflow_credits_limit}
              </span>
            </div>
            
            {/* AI Credits */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-1">
                <Sparkles className="h-3 w-3 text-purple-500" />
                <span>AI Requests</span>
              </div>
              <span className="font-mono">
                {currentOrganization.ai_credits_used}/
                {currentOrganization.ai_credits_limit === -1 
                  ? '∞' 
                  : currentOrganization.ai_credits_limit}
              </span>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default OrganizationSwitcher
