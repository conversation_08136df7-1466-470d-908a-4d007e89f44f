import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { supabase, signUpWithEmail, signInWithEmail } from '@/lib/supabase'
import { toast } from 'sonner'

const AuthDebug: React.FC = () => {
  const { user, profile, loading, refreshProfile } = useAuth()
  const [testEmail, setTestEmail] = useState('<EMAIL>')
  const [testPassword, setTestPassword] = useState('password123')
  const [testName, setTestName] = useState('Test User')
  const [isTestingSignup, setIsTestingSignup] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'failed'>('unknown')
  const [schemaStatus, setSchemaStatus] = useState<'unknown' | 'success' | 'failed'>('unknown')

  const testSignUp = async () => {
    setIsTestingSignup(true)
    try {
      console.log('Testing sign up with:', { testEmail, testName })

      // First, check if user already exists
      const { data: existingUser } = await supabase.auth.getUser()
      if (existingUser?.user?.email === testEmail) {
        toast.info('User already signed in with this email')
        setIsTestingSignup(false)
        return
      }

      const { data, error } = await signUpWithEmail(testEmail, testPassword, testName)

      if (error) {
        console.error('Test sign up error:', error)

        // Provide specific error analysis
        if (error.message.includes('Database error')) {
          toast.error(`Database trigger error: ${error.message}. Try running the SQL fix script.`)
        } else if (error.message.includes('User already registered')) {
          toast.error('User already exists. Try a different email or sign in instead.')
        } else if (error.message.includes('Invalid email')) {
          toast.error('Invalid email format.')
        } else {
          toast.error(`Sign up failed: ${error.message}`)
        }
      } else {
        console.log('Test sign up successful:', data)
        if (data?.user) {
          toast.success(`Sign up successful! User ID: ${data.user.id}`)

          // Check if profile was created
          setTimeout(async () => {
            try {
              const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', data.user.id)
                .single()

              if (profile) {
                toast.success('Profile created successfully!')
                console.log('Profile created:', profile)
              } else {
                toast.warning('User created but profile missing. Database trigger may have failed.')
                console.warn('Profile creation failed:', profileError)
              }
            } catch (profileErr) {
              console.warn('Profile check failed:', profileErr)
            }
          }, 2000)
        } else {
          toast.success('Sign up initiated - check email for confirmation')
        }
      }
    } catch (err) {
      console.error('Test sign up exception:', err)
      toast.error(`Test sign up exception: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setIsTestingSignup(false)
    }
  }

  const testConnection = async () => {
    try {
      console.log('Testing Supabase connection...')

      // Test basic connection
      const { data, error } = await supabase.from('profiles').select('count').limit(1)

      if (error) {
        console.error('Connection test error:', error)
        toast.error(`Connection test failed: ${error.message}`)
        return
      }

      console.log('Basic connection successful:', data)

      // Test auth service
      const { data: authData, error: authError } = await supabase.auth.getSession()
      console.log('Auth service test:', { authData, authError })

      // Test if we can read from auth.users (this will fail if RLS is too restrictive)
      try {
        const { data: usersData, error: usersError } = await supabase.rpc('get_current_user_id')
        console.log('User ID test:', { usersData, usersError })
      } catch (rpcErr) {
        console.log('RPC test failed (expected):', rpcErr)
      }

      setConnectionStatus('success')
      toast.success('Supabase connection and auth service working!')
    } catch (err) {
      console.error('Connection test exception:', err)
      setConnectionStatus('failed')
      toast.error(`Connection test exception: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const checkAuthUser = async () => {
    try {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      console.log('Current auth user:', authUser, 'error:', error)

      if (authUser) {
        toast.success(`Auth user found: ${authUser.email}`)
      } else {
        toast.info('No authenticated user found')
      }
    } catch (err) {
      console.error('Auth check exception:', err)
      toast.error(`Auth check failed: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const checkDatabaseSchema = async () => {
    try {
      console.log('Checking database schema...')

      // Check if profiles table exists and its structure
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1)

      console.log('Profiles table test:', { profilesData, profilesError })

      if (profilesError) {
        setSchemaStatus('failed')
        toast.error(`Profiles table issue: ${profilesError.message}`)
        return
      }

      // Check if organizations table exists
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .limit(1)

      console.log('Organizations table test:', { orgsData, orgsError })

      if (orgsError) {
        console.warn('Organizations table issue:', orgsError)
      }

      setSchemaStatus('success')
      toast.success('Database schema check completed - see console for details')
    } catch (err) {
      console.error('Schema check exception:', err)
      setSchemaStatus('failed')
      toast.error(`Schema check failed: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug Panel</CardTitle>
          <div className="text-sm text-gray-600">
            <p>Use this panel to diagnose sign-up issues. Start by testing the connection and database schema.</p>
            <p className="mt-1">If tests fail, run the <code className="bg-gray-100 px-1 rounded">quick_signup_fix.sql</code> script in your Supabase SQL Editor.</p>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* System Status */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">System Status</h3>
            <div className="flex flex-wrap gap-4 text-sm">
              <div>Connection: <Badge variant={connectionStatus === 'success' ? 'default' : connectionStatus === 'failed' ? 'destructive' : 'secondary'}>{connectionStatus}</Badge></div>
              <div>Database Schema: <Badge variant={schemaStatus === 'success' ? 'default' : schemaStatus === 'failed' ? 'destructive' : 'secondary'}>{schemaStatus}</Badge></div>
            </div>
          </div>

          {/* Current Auth State */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Auth State</h3>
              <div className="space-y-2">
                <div>Loading: <Badge variant={loading ? "destructive" : "secondary"}>{loading ? "Yes" : "No"}</Badge></div>
                <div>User: <Badge variant={user ? "default" : "secondary"}>{user ? "Authenticated" : "Not authenticated"}</Badge></div>
                <div>Profile: <Badge variant={profile ? "default" : "secondary"}>{profile ? "Loaded" : "Not loaded"}</Badge></div>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">User Details</h3>
              {user ? (
                <div className="space-y-1 text-sm">
                  <div>Email: {user.email}</div>
                  <div>ID: {user.id}</div>
                  <div>Confirmed: {user.email_confirmed_at ? "Yes" : "No"}</div>
                  <div>Created: {new Date(user.created_at).toLocaleString()}</div>
                </div>
              ) : (
                <div className="text-gray-500">No user data</div>
              )}
            </div>
          </div>

          {/* Profile Details */}
          {profile && (
            <div>
              <h3 className="font-semibold mb-2">Profile Details</h3>
              <div className="space-y-1 text-sm">
                <div>Name: {profile.full_name}</div>
                <div>Role: {profile.role}</div>
                <div>Created: {new Date(profile.created_at).toLocaleString()}</div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={refreshProfile} variant="outline">
              Refresh Profile
            </Button>
            <Button onClick={testConnection} variant="outline">
              Test Connection
            </Button>
            <Button onClick={checkAuthUser} variant="outline">
              Check Auth User
            </Button>
            <Button onClick={checkDatabaseSchema} variant="outline">
              Check Database Schema
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Sign Up */}
      <Card>
        <CardHeader>
          <CardTitle>Test Sign Up</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <input
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <input
                type="password"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="password123"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Full Name</label>
              <input
                type="text"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="Test User"
              />
            </div>
          </div>
          
          <Button 
            onClick={testSignUp} 
            disabled={isTestingSignup}
            className="w-full"
          >
            {isTestingSignup ? "Testing Sign Up..." : "Test Sign Up"}
          </Button>
        </CardContent>
      </Card>

      {/* Environment Info */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Info</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div>Supabase URL: {import.meta.env.VITE_SUPABASE_URL}</div>
            <div>Anon Key: {import.meta.env.VITE_SUPABASE_ANON_KEY ? `${import.meta.env.VITE_SUPABASE_ANON_KEY.substring(0, 20)}...` : 'Not set'}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AuthDebug
