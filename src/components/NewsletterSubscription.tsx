import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Loader2, Mail, CheckCircle } from 'lucide-react';
import { subscribeToNewsletter } from '@/lib/supabase';

interface NewsletterSubscriptionProps {
  title?: string;
  description?: string;
  placeholder?: string;
  buttonText?: string;
  source?: string;
  className?: string;
  variant?: 'default' | 'card' | 'inline';
  showIcon?: boolean;
}

const NewsletterSubscription: React.FC<NewsletterSubscriptionProps> = ({
  title = "Stay Updated",
  description = "Subscribe to our newsletter for the latest insights and updates.",
  placeholder = "Your email",
  buttonText = "Subscribe",
  source = "website",
  className = "",
  variant = "default",
  showIcon = true
}) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !isValidEmail(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsSubscribing(true);

    try {
      const { data, error } = await subscribeToNewsletter(email, name || undefined, source);

      if (error) {
        toast.error(error.message || 'Failed to subscribe');
      } else if (data?.success) {
        toast.success(data.message);
        setEmail('');
        setName('');
        setIsSubscribed(true);
        
        // Reset success state after 3 seconds
        setTimeout(() => setIsSubscribed(false), 3000);
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Failed to subscribe. Please try again.');
    } finally {
      setIsSubscribing(false);
    }
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex flex-col sm:flex-row gap-2">
        <Input
          type="email"
          placeholder={placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={isSubscribing}
          className="flex-1"
          required
        />
        <Button
          type="submit"
          disabled={isSubscribing || !email}
          className="bg-primary hover:bg-primary/80 text-white px-4 py-2 disabled:opacity-50"
        >
          {isSubscribing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Subscribing...
            </>
          ) : isSubscribed ? (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Subscribed!
            </>
          ) : (
            <>
              {showIcon && <Mail className="w-4 h-4 mr-2" />}
              {buttonText}
            </>
          )}
        </Button>
      </div>
      
      {/* Optional name field for enhanced personalization */}
      <Input
        type="text"
        placeholder="Your name (optional)"
        value={name}
        onChange={(e) => setName(e.target.value)}
        disabled={isSubscribing}
        className="text-sm"
      />
    </form>
  );

  if (variant === 'card') {
    return (
      <Card className={`bg-white/80 dark:bg-white/5 border border-white/40 dark:border-white/10 backdrop-blur-lg ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg font-montserrat">
            {showIcon && <Mail className="w-5 h-5 text-primary" />}
            {title}
          </CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400 font-poppins">
            {description}
          </p>
        </CardHeader>
        <CardContent>
          {renderForm()}
        </CardContent>
      </Card>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={`inline-flex items-center gap-2 ${className}`}>
        <Input
          type="email"
          placeholder={placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={isSubscribing}
          className="w-64"
          required
        />
        <Button
          onClick={handleSubmit}
          disabled={isSubscribing || !email}
          className="bg-primary hover:bg-primary/80 text-white"
        >
          {isSubscribing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : isSubscribed ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <>
              {showIcon && <Mail className="w-4 h-4 mr-2" />}
              {buttonText}
            </>
          )}
        </Button>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`space-y-3 ${className}`}>
      <div>
        <h3 className="font-montserrat font-semibold text-gray-900 dark:text-white text-base mb-2">
          {showIcon && <Mail className="w-5 h-5 text-primary inline mr-2" />}
          {title}
        </h3>
        <p className="font-poppins text-gray-600 dark:text-gray-400 text-sm">
          {description}
        </p>
      </div>
      {renderForm()}
    </div>
  );
};

export default NewsletterSubscription;
