import React from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { useAuth } from '@/contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Zap, 
  ArrowRight, 
  Crown,
  AlertTriangle,
  Sparkles
} from 'lucide-react'
import { toast } from 'sonner'

const WorkflowCreditBanner: React.FC = () => {
  const { currentOrganization } = useOrganization()
  const { profile } = useAuth()
  const navigate = useNavigate()

  if (!currentOrganization || !profile) return null

  const creditsUsed = currentOrganization.workflow_credits_used || 0
  const creditsLimit = currentOrganization.workflow_credits_limit || 100
  const creditsRemaining = Math.max(0, creditsLimit - creditsUsed)
  const usagePercentage = creditsLimit > 0 ? (creditsUsed / creditsLimit) * 100 : 0
  
  const isNearLimit = usagePercentage >= 80
  const isAtLimit = creditsUsed >= creditsLimit
  const isFreeUser = profile.subscription_plan === 'free'

  const handleUpgrade = () => {
    // Check if user is owner/super admin - they can upgrade
    if (profile?.role === 'owner' || profile?.role === 'super_admin') {
      navigate('/pricing?highlight=workflow_credits&from=workflow_banner')
    } else {
      // Regular users see coming soon message
      toast.info('Payment Gateway Coming Soon!', {
        description: 'We\'re working on integrating secure payment processing. Stay tuned!'
      })
    }
  }

  const showUpgradeToast = () => {
    toast.custom((t) => (
      <Card className="w-full max-w-md border-l-4 border-l-primary">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Zap className="h-4 w-4 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                Need More Credits?
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Upgrade to get 10x more workflow automation power with 1,000 monthly credits.
              </p>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  onClick={handleUpgrade}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  Upgrade Now
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => toast.dismiss(t)}
                >
                  Later
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    ), {
      duration: 8000,
      position: 'top-right'
    })
  }

  // Free tier banner
  if (isFreeUser) {
    return (
      <Card className="mb-6 border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Zap className="h-5 w-5 text-white" />
                </div>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    Free Tier Workflows
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    {creditsRemaining} credits left
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Test workflows with {creditsLimit} free monthly credits. 
                  {isAtLimit ? ' You\'ve reached your limit.' : isNearLimit ? ' Running low!' : ''}
                </p>
                
                {/* Progress bar */}
                <div className="mt-2 flex items-center gap-2">
                  <Progress 
                    value={usagePercentage} 
                    className="flex-1 h-2"
                  />
                  <span className="text-xs text-gray-500 font-mono">
                    {creditsUsed}/{creditsLimit}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {(isNearLimit || isAtLimit) && (
                <Button
                  size="sm"
                  onClick={handleUpgrade}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  {(profile?.role === 'owner' || profile?.role === 'super_admin') ? (
                    <>
                      <Crown className="mr-1 h-3 w-3" />
                      Upgrade
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-1 h-3 w-3" />
                      Coming Soon
                    </>
                  )}
                </Button>
              )}
              <Button
                size="sm"
                variant="outline"
                onClick={() => navigate('/pricing')}
              >
                View Plans
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Paid tier - show credits remaining if getting low
  if (isNearLimit) {
    return (
      <Card className="mb-6 border-l-4 border-l-orange-500 bg-orange-50 dark:bg-orange-900/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Credits Running Low
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                You have {creditsRemaining} workflow credits remaining this month.
              </p>
            </div>
            <Badge variant="outline" className="text-orange-600 border-orange-600">
              {creditsUsed}/{creditsLimit}
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  return null
}

export default WorkflowCreditBanner
