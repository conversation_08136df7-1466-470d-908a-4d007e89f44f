
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Pricing } from '@/components/ui/pricing';
import CalendarModal from './CalendarModal';
import QuoteRequestModal from './QuoteRequestModal';

const PricingSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);

  const originalPlans = [
    {
      name: 'Starter Package',
      price: '$12,000',
      period: 'starting price',
      description: 'Perfect for startups and small businesses looking to launch their MVP.',
      features: [
        'MVP Development',
        'Landing Page Design',
        'Initial Strategy Session',
        'Basic Support (60 days)',
        'Responsive Design',
        'SEO Foundation',
        'Performance Optimization',
        'Security Best Practices'
      ],
      highlighted: false,
      cta: 'Get Started'
    },
    {
      name: 'Professional Package',
      price: '$35,000',
      period: 'starting price',
      description: 'Ideal for growing businesses that need comprehensive digital solutions.',
      features: [
        'Everything in Starter',
        'Custom Web Applications',
        'Advanced Database Design',
        'Extended Support (120 days)',
        'Advanced Analytics Integration',
        'Third-party API Integration',
        'Cloud Infrastructure Setup',
        'Advanced Security Implementation',
        'Performance Monitoring'
      ],
      highlighted: true,
      cta: 'Most Popular'
    },
    {
      name: 'Enterprise Package',
      price: '$75,000+',
      period: 'custom pricing',
      description: 'Comprehensive SaaS platforms and enterprise solutions for serious businesses.',
      features: [
        'Everything in Professional',
        'Full SaaS Platform Development',
        'Digital Strategy Consulting ($250/hr)',
        'Dedicated Development Team',
        'Long-term Partnership',
        'Dedicated Project Manager',
        '24/7 Priority Support',
        'Enterprise Security Features',
        'Scalability Architecture',
        'Custom Integrations & APIs',
        'DevOps & CI/CD Pipeline',
        'Ongoing Maintenance Plans'
      ],
      highlighted: false,
      cta: 'Schedule Consultation'
    }
  ];

  // Convert to new pricing component format
  const convertToPricingPlans = () => {
    return originalPlans.map((plan) => {
      // Handle different pricing formats
      let displayPrice = plan.price;
      let yearlyPrice = plan.price;

      // For the enterprise plan with custom pricing, keep original format
      if (plan.price.includes('+') || plan.price.toLowerCase().includes('custom')) {
        displayPrice = plan.price.replace('$', '').replace(',', '');
        yearlyPrice = displayPrice;
      } else {
        // Extract numeric price for calculations
        const priceMatch = plan.price.match(/[\d,]+/);
        const numericPrice = priceMatch ? parseInt(priceMatch[0].replace(',', '')) : 0;

        // Calculate yearly price (20% discount)
        const yearlyDiscount = Math.round(numericPrice * 0.8);

        displayPrice = numericPrice.toString();
        yearlyPrice = yearlyDiscount.toString();
      }

      return {
        name: plan.name.replace(' Package', '').toUpperCase(),
        price: displayPrice,
        yearlyPrice: yearlyPrice,
        period: plan.period,
        features: plan.features,
        description: plan.description,
        buttonText: plan.cta,
        href: '#',
        isPopular: plan.highlighted,
        planId: plan.name.toLowerCase().replace(' package', '').replace(' ', '_')
      };
    });
  };

  const handlePlanClick = (planName: string) => {
    // Handle plan selection - you can customize this
    if (planName.includes('ENTERPRISE')) {
      setIsCalendarOpen(true);
    } else {
      setIsQuoteModalOpen(true);
    }
  };

  return (
    <section id="pricing" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-20 w-48 h-48 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-20 w-64 h-64 bg-gradient-to-r from-cyan-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 left-1/4 w-36 h-36 bg-gradient-to-r from-teal-300/10 to-blue-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Enterprise-Grade <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Solutions</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto px-4 sm:px-0">
            Transform your startup idea into a scalable, secure, and profitable digital platform. Our pricing reflects the enterprise-level quality and strategic value we deliver.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              No hidden fees
            </span>
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              Fixed-price projects
            </span>
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              Money-back guarantee
            </span>
          </div>
        </div>
        
        {/* New Interactive Pricing Component */}
        <Pricing
          plans={convertToPricingPlans()}
          title="" // Remove duplicate title
          description="" // Remove duplicate description
          onPlanClick={handlePlanClick}
        />
        
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 mb-8">
            <h3 className="font-montserrat font-bold text-2xl text-gray-900 dark:text-white mb-4">
              Ready to Build Something Amazing?
            </h3>
            <p className="font-poppins text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Join 50+ successful startups who've transformed their ideas into profitable digital platforms with our expert team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-8 py-3 text-lg"
                onClick={() => setIsCalendarOpen(true)}
              >
                Get Your Free Strategy Session
              </Button>
              <Button
                variant="outline"
                className="border-primary text-primary hover:bg-primary/10 font-poppins px-8 py-3 text-lg"
                onClick={() => setIsQuoteModalOpen(true)}
              >
                Request Custom Quote
              </Button>
            </div>
          </div>

          <p className="font-poppins text-sm text-gray-500 dark:text-gray-400">
            💡 <strong>Limited Availability:</strong> We only take on 3-4 new projects per quarter to ensure exceptional quality.
          </p>
        </div>
      </div>

      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />

      <QuoteRequestModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
      />
    </section>
  );
};

export default PricingSection;
