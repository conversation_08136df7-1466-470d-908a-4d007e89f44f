import React, { useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface CalendarModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CalendarModal: React.FC<CalendarModalProps> = ({ isOpen, onClose }) => {
  useEffect(() => {
    if (isOpen) {
      // Load the GHL calendar script when modal opens
      const script = document.createElement('script');
      script.src = 'https://calendar.aha-innovations.com/js/form_embed.js';
      script.type = 'text/javascript';
      script.async = true;
      document.head.appendChild(script);

      return () => {
        // Clean up script when modal closes
        const existingScript = document.querySelector('script[src="https://calendar.aha-innovations.com/js/form_embed.js"]');
        if (existingScript) {
          document.head.removeChild(existingScript);
        }
      };
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl w-[95vw] h-[90vh] p-0 overflow-hidden border-0 bg-transparent shadow-none">
        <div className="w-full h-full bg-white dark:bg-gray-900 rounded-lg overflow-hidden shadow-2xl">
          <div className="w-full h-full">
            <iframe
              src="https://calendar.aha-innovations.com/widget/booking/muwcL091TjKbXukSZgs5"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                overflow: 'hidden',
                borderRadius: '8px'
              }}
              scrolling="yes"
              id="eLsfI5xBSm4w9nZyxtfy_1748977986939"
              title="Schedule Consultation"
              allow="camera; microphone; geolocation"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CalendarModal;
