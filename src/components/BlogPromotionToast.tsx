import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { <PERSON>, <PERSON>, Sparkles, ArrowRight } from 'lucide-react'
import { useLocation } from 'react-router-dom'

const BlogPromotionToast = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const { user } = useAuth()
  const location = useLocation()

  useEffect(() => {
    // Don't show if user is authenticated or on blog pages or if already dismissed
    if (user || location.pathname.startsWith('/blog') || location.pathname.startsWith('/admin')) {
      return
    }

    // Check if user has dismissed this before (localStorage)
    const dismissed = localStorage.getItem('blog-promotion-dismissed')
    if (dismissed) {
      setIsDismissed(true)
      return
    }

    // Show toast after 3 seconds on homepage
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 3000)

    return () => clearTimeout(timer)
  }, [user, location.pathname])

  const handleDismiss = () => {
    setIsVisible(false)
    setIsDismissed(true)
    localStorage.setItem('blog-promotion-dismissed', 'true')
  }

  const handleCTAClick = () => {
    window.location.href = '/blog'
  }

  if (!isVisible || isDismissed || user) {
    return null
  }

  return (
    <div className="fixed bottom-6 right-6 z-50 max-w-sm animate-in slide-in-from-bottom-2 duration-500">
      <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-white/30 dark:border-gray-700/50 rounded-xl shadow-xl p-4 relative">
        {/* Close button */}
        <button
          onClick={handleDismiss}
          className="absolute top-2 right-2 w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center transition-colors"
          aria-label="Dismiss"
        >
          <X className="w-3 h-3 text-gray-500 dark:text-gray-400" />
        </button>

        {/* Content */}
        <div className="pr-6">
          {/* Icon and badge */}
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
              <Edit className="w-4 h-4 text-white" />
            </div>
            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              <Sparkles className="w-3 h-3 inline mr-1" />
              Free + AI
            </div>
          </div>

          {/* Title */}
          <h3 className="font-montserrat font-bold text-sm text-gray-900 dark:text-white mb-1">
            Start Your Blog Today
          </h3>

          {/* Description */}
          <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
            Join our free blogging platform with AI writing assistance. No setup required!
          </p>

          {/* CTA Button */}
          <Button
            onClick={handleCTAClick}
            size="sm"
            className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white text-xs font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/25"
          >
            Try Free Blogging
            <ArrowRight className="w-3 h-3 ml-1" />
          </Button>
        </div>

        {/* Subtle animation glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-xl animate-pulse pointer-events-none" />
      </div>
    </div>
  )
}

export default BlogPromotionToast
