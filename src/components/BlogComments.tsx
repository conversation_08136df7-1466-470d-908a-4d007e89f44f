import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { MessageCircle, Reply, Trash2, Edit3, Mail, User, Eye, EyeOff } from 'lucide-react'
import { toast } from 'sonner'
import { getPostComments, createComment, deleteComment, signInWithGoogle } from '@/lib/supabase'

interface Comment {
  id: string
  content: string
  created_at: string
  updated_at: string
  user_id: string | null
  parent_id: string | null
  guest_email: string | null
  guest_name: string | null
  is_anonymous: boolean
  is_verified: boolean
  wants_notifications: boolean
  wants_promotions: boolean
  user?: {
    id: string
    full_name: string
    email: string
    avatar_url: string | null
  }
}

interface BlogCommentsProps {
  postId: string
  commentCount?: number
}

const BlogComments: React.FC<BlogCommentsProps> = ({ postId, commentCount = 0 }) => {
  const { user } = useAuth()
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [replyTo, setReplyTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // Guest comment state
  const [guestEmail, setGuestEmail] = useState('')
  const [guestName, setGuestName] = useState('')
  const [isAnonymous, setIsAnonymous] = useState(false)
  const [showGuestForm, setShowGuestForm] = useState(false)
  const [wantsNotifications, setWantsNotifications] = useState(false)
  const [wantsPromotions, setWantsPromotions] = useState(false)

  useEffect(() => {
    fetchComments()
  }, [postId])

  const fetchComments = async () => {
    setLoading(true)
    try {
      const { data, error } = await getPostComments(postId)
      if (error) {
        console.error('Error fetching comments:', error)
        return
      }
      setComments(data || [])
    } catch (error) {
      console.error('Error fetching comments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast.error('Please enter a comment')
      return
    }

    if (!user && !showGuestForm) {
      setShowGuestForm(true)
      return
    }

    if (!user && !guestEmail.trim()) {
      toast.error('Please enter your email address')
      return
    }

    if (!user && !isAnonymous && !guestName.trim()) {
      toast.error('Please enter your name')
      return
    }

    setSubmitting(true)
    try {
      const guestInfo = !user ? {
        email: guestEmail.trim(),
        name: guestName.trim(),
        isAnonymous,
        wantsNotifications,
        wantsPromotions
      } : undefined

      const { data, error } = await createComment(postId, newComment.trim(), undefined, guestInfo)
      if (error) {
        toast.error('Failed to post comment')
        return
      }

      if (data) {
        setComments(prev => [...prev, data])
        setNewComment('')
        setGuestEmail('')
        setGuestName('')
        setIsAnonymous(false)
        setWantsNotifications(false)
        setWantsPromotions(false)
        setShowGuestForm(false)

        if (!user && !isAnonymous) {
          toast.success('Comment posted! Check your email for a signup invitation 📧')
        } else {
          toast.success('Comment posted!')
        }
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentId: string) => {
    if (!user) {
      toast.error('Please sign in to reply')
      return
    }

    if (!replyContent.trim()) {
      toast.error('Please enter a reply')
      return
    }

    setSubmitting(true)
    try {
      const { data, error } = await createComment(postId, replyContent.trim(), parentId)
      if (error) {
        toast.error('Failed to post reply')
        return
      }

      if (data) {
        setComments(prev => [...prev, data])
        setReplyContent('')
        setReplyTo(null)
        toast.success('Reply posted!')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) {
      return
    }

    try {
      const { error } = await deleteComment(commentId)
      if (error) {
        toast.error('Failed to delete comment')
        return
      }

      setComments(prev => prev.filter(c => c.id !== commentId))
      toast.success('Comment deleted')
    } catch (error) {
      toast.error('An unexpected error occurred')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Organize comments into threads
  const topLevelComments = comments.filter(c => !c.parent_id)
  const getReplies = (parentId: string) => comments.filter(c => c.parent_id === parentId)

  return (
    <div id="comments-section" className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Comments ({commentCount})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* New Comment Form */}
          <div className="space-y-4">
            {/* Comment Input */}
            <div className="flex gap-3">
              <Avatar className="h-8 w-8 flex-shrink-0">
                {user ? (
                  <>
                    <AvatarImage src={user.user_metadata?.avatar_url} alt={user.user_metadata?.full_name || user.email} />
                    <AvatarFallback>
                      {(user.user_metadata?.full_name || user.email || 'U').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </>
                ) : (
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                )}
              </Avatar>
              <div className="flex-1">
                <Textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder={user ? "Write a comment..." : "Write a comment... (we'll ask for your details next)"}
                  className="min-h-[80px] resize-none"
                />
              </div>
            </div>

            {/* Guest Information Form */}
            {!user && showGuestForm && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 space-y-4">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Mail className="h-4 w-4" />
                  <span className="font-medium">Join the conversation!</span>
                </div>

                {/* Email (always required) */}
                <div>
                  <Input
                    type="email"
                    placeholder="Your email (required)"
                    value={guestEmail}
                    onChange={(e) => setGuestEmail(e.target.value)}
                    className="w-full"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="anonymous"
                    checked={isAnonymous}
                    onCheckedChange={(checked) => setIsAnonymous(checked as boolean)}
                  />
                  <label htmlFor="anonymous" className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                    Comment anonymously (hide your name)
                  </label>
                  {isAnonymous ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
                </div>

                {!isAnonymous && (
                  <div>
                    <Input
                      type="text"
                      placeholder="Your name"
                      value={guestName}
                      onChange={(e) => setGuestName(e.target.value)}
                      className="w-full"
                    />
                  </div>
                )}

                {/* Notification Preferences */}
                <div className="space-y-2 border-t pt-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="notifications"
                      checked={wantsNotifications}
                      onCheckedChange={(checked) => setWantsNotifications(checked as boolean)}
                    />
                    <label htmlFor="notifications" className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                      Notify me of replies to my comment
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="promotions"
                      checked={wantsPromotions}
                      onCheckedChange={(checked) => setWantsPromotions(checked as boolean)}
                    />
                    <label htmlFor="promotions" className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                      Send me updates about new blog posts and features
                    </label>
                  </div>
                </div>

                <div className="text-xs text-gray-600 dark:text-gray-400">
                  We'll send you a signup invitation!
                  <button
                    type="button"
                    onClick={async () => {
                      try {
                        await signInWithGoogle(window.location.href)
                      } catch (error) {
                        toast.error('Failed to start sign-up process')
                      }
                    }}
                    className="font-medium text-blue-600 dark:text-blue-400 hover:underline ml-1"
                  >
                    Sign up now - it's easy with Google!
                  </button>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center">
              {!user && showGuestForm && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    setShowGuestForm(false)
                    setGuestEmail('')
                    setGuestName('')
                    setIsAnonymous(false)
                  }}
                  className="text-gray-500"
                >
                  Cancel
                </Button>
              )}

              <div className="flex gap-2 ml-auto">
                {!user && !showGuestForm && (
                  <Button
                    variant="outline"
                    className="text-blue-600 border-blue-600 hover:bg-blue-50"
                    onClick={async () => {
                      try {
                        await signInWithGoogle(window.location.href)
                      } catch (error) {
                        toast.error('Failed to start sign-in process')
                      }
                    }}
                  >
                    Sign In with Google
                  </Button>
                )}

                <Button
                  onClick={handleSubmitComment}
                  disabled={submitting || !newComment.trim()}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                >
                  {submitting ? 'Posting...' : 'Post Comment'}
                </Button>
              </div>
            </div>
          </div>

          {/* Comments List */}
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex gap-3">
                    <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No comments yet. Be the first to share your thoughts!</p>
            </div>
          ) : (
            <div className="space-y-6">
              {topLevelComments.map((comment) => (
                <div key={comment.id} className="space-y-4">
                  {/* Main Comment */}
                  <div className="flex gap-3">
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      {comment.user ? (
                        <>
                          <AvatarImage src={comment.user.avatar_url || undefined} alt={comment.user.full_name} />
                          <AvatarFallback>
                            {comment.user.full_name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </>
                      ) : (
                        <AvatarFallback>
                          {comment.is_anonymous ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            comment.guest_name?.charAt(0).toUpperCase() || 'G'
                          )}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="font-medium text-gray-900 dark:text-white">
                          {comment.user?.full_name || comment.guest_name || 'Anonymous'}
                        </span>
                        {!comment.user && !comment.is_anonymous && (
                          <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded">
                            Guest
                          </span>
                        )}
                        {comment.is_anonymous && (
                          <span className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded">
                            Anonymous
                          </span>
                        )}
                        <span className="text-gray-500">
                          {formatDate(comment.created_at)}
                        </span>
                      </div>
                      <div className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {comment.content}
                      </div>
                      <div className="flex items-center gap-2">
                        {user && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setReplyTo(replyTo === comment.id ? null : comment.id)}
                            className="text-gray-500 hover:text-gray-700 h-8 px-2"
                          >
                            <Reply className="h-3 w-3 mr-1" />
                            Reply
                          </Button>
                        )}
                        {user?.id === comment.user_id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteComment(comment.id)}
                            className="text-red-500 hover:text-red-700 h-8 px-2"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        )}
                      </div>

                      {/* Reply Form */}
                      {replyTo === comment.id && (
                        <div className="mt-4 space-y-3">
                          <div className="flex gap-3">
                            <Avatar className="h-6 w-6 flex-shrink-0">
                              <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.user_metadata?.full_name || user?.email} />
                              <AvatarFallback className="text-xs">
                                {(user?.user_metadata?.full_name || user?.email || 'U').charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <Textarea
                                value={replyContent}
                                onChange={(e) => setReplyContent(e.target.value)}
                                placeholder={`Reply to ${comment.user.full_name}...`}
                                className="min-h-[60px] resize-none text-sm"
                              />
                            </div>
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setReplyTo(null)
                                setReplyContent('')
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSubmitReply(comment.id)}
                              disabled={submitting || !replyContent.trim()}
                              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                            >
                              {submitting ? 'Posting...' : 'Reply'}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Replies */}
                  {getReplies(comment.id).map((reply) => (
                    <div key={reply.id} className="ml-11 flex gap-3">
                      <Avatar className="h-6 w-6 flex-shrink-0">
                        <AvatarImage src={reply.user.avatar_url || undefined} alt={reply.user.full_name} />
                        <AvatarFallback className="text-xs">
                          {reply.user.full_name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {reply.user.full_name}
                          </span>
                          <span className="text-gray-500">
                            {formatDate(reply.created_at)}
                          </span>
                        </div>
                        <div className="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-wrap">
                          {reply.content}
                        </div>
                        {user?.id === reply.user_id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteComment(reply.id)}
                            className="text-red-500 hover:text-red-700 h-6 px-1 text-xs"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default BlogComments
