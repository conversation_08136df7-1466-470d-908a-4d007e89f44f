import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import {
  Eye,
  MousePointer,
  Clock,
  TrendingUp,
  Users,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus,
  ExternalLink,
  Lock,
  Crown
} from 'lucide-react'
import { toast } from 'sonner'
import {
  getAnalyticsOverview,
  getAnalyticsChartData,
  getTrafficSources,
  getDeviceStats,
  getTopPosts,
  hasEnhancedAnalyticsAccess,
  type AnalyticsOverview,
  type ChartDataPoint,
  type TrafficSource,
  type DeviceStats,
  type TopPost
} from '@/lib/analytics'

const UserAnalytics = () => {
  const { profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const [overviewStats, setOverviewStats] = useState<AnalyticsOverview>({
    totalViews: 0,
    totalUniqueViews: 0,
    totalClicks: 0,
    avgReadingTime: 0,
    avgBounceRate: 0,
    totalPosts: 0,
    publishedPosts: 0
  })
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])
  const [topPosts, setTopPosts] = useState<TopPost[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')
  
  // Check if user has enhanced analytics access
  const hasEnhancedAccess = hasEnhancedAnalyticsAccess(
    profile?.role,
    currentOrganization?.subscription_plan || profile?.subscription_plan
  )

  useEffect(() => {
    loadAnalytics()
  }, [currentOrganization, timeRange])

  const loadAnalytics = async () => {
    setLoading(true)
    try {
      // Users only see their own organization's data
      const organizationId = currentOrganization?.id

      const [overview, chartData, topPosts] = await Promise.all([
        getAnalyticsOverview(organizationId, timeRange),
        getAnalyticsChartData(organizationId, timeRange),
        getTopPosts(organizationId, timeRange, 'view_count', 5) // Limited to top 5 for users
      ])

      setOverviewStats(overview)
      setChartData(chartData)
      setTopPosts(topPosts)

    } catch (error) {
      console.error('Error loading analytics:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const getPerformanceColor = (value: number, type: 'bounce' | 'ctr' | 'time') => {
    switch (type) {
      case 'bounce':
        if (value > 70) return 'text-red-600'
        if (value > 50) return 'text-yellow-600'
        return 'text-green-600'
      case 'ctr':
        if (value > 5) return 'text-green-600'
        if (value > 2) return 'text-yellow-600'
        return 'text-red-600'
      case 'time':
        if (value > 120) return 'text-green-600'
        if (value > 60) return 'text-yellow-600'
        return 'text-red-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const chartColors = {
    primary: '#3b82f6',
    secondary: '#10b981',
    accent: '#f59e0b',
    muted: '#6b7280'
  }

  return (
    <ProtectedRoute requiredRole={['user', 'admin', 'super_admin', 'owner', 'saas_owner']}>
      <AdminLayout
        title="My Analytics"
        subtitle="Track your content performance and audience engagement"
        actions={
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadAnalytics} variant="outline">
              <TrendingUp className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        }
      >
        {loading ? (
          <div className="space-y-6">
            {/* Loading Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="h-4 bg-muted rounded w-20"></div>
                    <div className="h-4 w-4 bg-muted rounded"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                    <div className="h-3 bg-muted rounded w-24"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500 bg-gradient-to-br from-blue-50 to-background dark:from-blue-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Views</CardTitle>
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {overviewStats.totalViews.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {overviewStats.totalUniqueViews.toLocaleString()} unique visitors
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-amber-500 bg-gradient-to-br from-amber-50 to-background dark:from-amber-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-300">Engagement</CardTitle>
                  <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-full">
                    <MousePointer className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                    {overviewStats.totalClicks.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {overviewStats.totalViews > 0
                      ? `${Math.round((overviewStats.totalClicks / overviewStats.totalViews) * 100)}% CTR`
                      : '0% CTR'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500 bg-gradient-to-br from-purple-50 to-background dark:from-purple-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Reading Time</CardTitle>
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                    <Clock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatDuration(overviewStats.avgReadingTime)}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <ArrowDown className="h-3 w-3" />
                    {overviewStats.avgBounceRate.toFixed(1)}% bounce rate
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500 bg-gradient-to-br from-green-50 to-background dark:from-green-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Published Posts</CardTitle>
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                    <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {overviewStats.publishedPosts}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    in selected period
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    views: {
                      label: "Views",
                      color: "#3b82f6",
                    },
                    uniqueViews: {
                      label: "Unique Views",
                      color: "#10b981",
                    },
                  }}
                  className="h-80"
                >
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                      <linearGradient id="colorUniqueViews" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis
                      dataKey="date"
                      axisLine={false}
                      tickLine={false}
                      className="text-xs"
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      className="text-xs"
                    />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      cursor={{ stroke: '#3b82f6', strokeWidth: 1, strokeDasharray: '3 3' }}
                    />
                    <Area
                      type="monotone"
                      dataKey="views"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorViews)"
                      name="Views"
                    />
                    <Area
                      type="monotone"
                      dataKey="uniqueViews"
                      stroke="#10b981"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorUniqueViews)"
                      name="Unique Views"
                    />
                  </AreaChart>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Top Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Your Top Performing Posts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topPosts.length > 0 ? (
                    topPosts.map((post, index) => (
                      <div key={post.post_id} className="group relative">
                        <div className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-all duration-200 bg-gradient-to-r from-background to-muted/20">
                          <div className="flex items-center gap-3">
                            <div className={`
                              flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold
                              ${index === 0 ? 'bg-yellow-500 text-white' :
                                index === 1 ? 'bg-gray-400 text-white' :
                                index === 2 ? 'bg-amber-600 text-white' :
                                'bg-muted text-muted-foreground'}
                            `}>
                              {index + 1}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="font-semibold truncate group-hover:text-primary transition-colors">
                                {post.title}
                              </h4>
                              <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                                <span className="flex items-center gap-1">
                                  <Eye className="h-3 w-3" />
                                  {post.view_count?.toLocaleString() || 0}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  {post.unique_view_count?.toLocaleString() || 0}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatDuration(post.avg_reading_time || 0)}
                                </span>
                              </div>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" asChild className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <a href={`/blog/${post.slug}`} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No posts found for the selected time period.</p>
                      <p className="text-sm">Start creating content to see analytics!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Upgrade CTA for Enhanced Analytics */}
            {!hasEnhancedAccess && (
              <Card className="border-2 border-dashed border-primary/30 bg-gradient-to-r from-primary/5 to-purple/5">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <Crown className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Unlock Enhanced Analytics</h3>
                  </div>
                  <p className="text-muted-foreground mb-4">
                    Get detailed traffic sources, device analytics, UTM tracking, and more insights with a paid plan.
                  </p>
                  <Button asChild className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90">
                    <a href="/pricing">
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade Now
                    </a>
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default UserAnalytics
