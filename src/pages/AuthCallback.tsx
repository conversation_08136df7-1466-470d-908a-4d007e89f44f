import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'

const AuthCallback: React.FC = () => {
  const navigate = useNavigate()
  const { user, profile, loading } = useAuth()

  useEffect(() => {
    // Handle OAuth callback
    const handleCallback = async () => {
      console.log('AuthCallback: Handling OAuth callback...')
      console.log('AuthCallback: User:', user?.email)
      console.log('AuthCallback: Profile:', profile)
      console.log('AuthCallback: Loading:', loading)

      // Wait for auth to be determined
      if (loading) {
        console.log('AuthCallback: Still loading, waiting...')
        return
      }

      // If no user, redirect to home
      if (!user) {
        console.log('AuthCallback: No user found, redirecting to home')
        navigate('/', { replace: true })
        return
      }

      // If user but no profile, wait a bit more
      if (!profile) {
        console.log('AuthCallback: User found but no profile, waiting for profile...')
        return
      }

      // User and profile loaded, redirect based on role
      console.log('AuthCallback: User and profile loaded, redirecting...')
      console.log('AuthCallback: User email:', user.email)
      console.log('AuthCallback: Profile role:', profile.role)

      if (user.email === '<EMAIL>') {
        console.log('AuthCallback: Redirecting admin to /admin/blog')
        toast.success('Welcome back, Stephen! Redirecting to admin panel...')
        navigate('/admin/blog', { replace: true })
      } else {
        console.log('AuthCallback: Redirecting user to /dashboard')
        toast.success('Welcome! Redirecting to your workspace...')
        navigate('/dashboard', { replace: true })
      }
    }

    handleCallback()
  }, [user, profile, loading, navigate])

  // Show loading screen
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        <h2 className="text-lg font-semibold mb-2">Signing you in...</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Please wait while we set up your account.
        </p>
      </div>
    </div>
  )
}

export default AuthCallback
