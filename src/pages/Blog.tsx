import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import AdminQuickAccess from '@/components/AdminQuickAccess'
import AuthModal from '@/components/auth/AuthModal'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { getBlogPosts, BlogPost, getDisplayAuthor, getReadingTime } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Search, Calendar, User, ArrowRight, Clock, TrendingUp, Star, Edit, Plus, BarChart3 } from 'lucide-react'
import { toast } from 'sonner'
import BlogEngagement from '@/components/BlogEngagement'

const Blog = () => {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([])
  const [showAuthModal, setShowAuthModal] = useState(false)
  const { user, profile } = useAuth()

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        // For public blog view, fetch all published posts from all organizations
        const { data, error } = await getBlogPosts('published', false, undefined, true)
        if (error) {
          toast.error('Failed to load blog posts')
          console.error('Error fetching posts:', error)
        } else {
          setPosts(data || [])
          setFilteredPosts(data || [])
        }
      } catch (error) {
        toast.error('An unexpected error occurred')
        console.error('Error:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [])

  useEffect(() => {
    if (searchTerm) {
      const filtered = posts.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredPosts(filtered)
    } else {
      setFilteredPosts(posts)
    }
  }, [searchTerm, posts])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getExcerpt = (content: any, maxLength: number = 150) => {
    if (typeof content === 'string') {
      return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
    }
    
    // Extract text from Tiptap JSON content
    if (content && content.content) {
      let text = ''
      const extractText = (node: any) => {
        if (node.type === 'text') {
          text += node.text
        } else if (node.content) {
          node.content.forEach(extractText)
        }
      }
      content.content.forEach(extractText)
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    }
    
    return 'No preview available...'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-20 pb-12">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="text-center py-20">
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-12 bg-gradient-to-b from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center py-12 sm:py-16">
            <h1 className="font-montserrat font-bold text-4xl sm:text-5xl md:text-6xl text-gray-900 dark:text-white mb-6">
              Community <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Blog</span>
            </h1>
            <p className="font-poppins text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Discover amazing stories, insights, and ideas from our growing community of writers. Join the conversation and share your voice with the world.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/80 dark:bg-white/10 backdrop-blur-md border-white/30 dark:border-white/20"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Public Blogging Platform CTA */}
      {!user && (
        <section className="py-12 bg-white/50 dark:bg-gray-800/50 backdrop-blur-md border-y border-white/30 dark:border-gray-700/50">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-full inline-block text-sm font-poppins mb-4">
                ✨ Free Platform - No Setup Required
              </div>
              <h2 className="font-montserrat font-bold text-3xl sm:text-4xl text-gray-900 dark:text-white mb-4">
                Ready to Share Your Story?
              </h2>
              <p className="font-poppins text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                Join thousands of writers on our free blogging platform. Start writing, build your audience, and share your ideas with the world.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  onClick={() => setShowAuthModal(true)}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-poppins px-8 py-3 rounded-full text-lg transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/25"
                >
                  <Edit className="mr-2 w-5 h-5" />
                  Start Writing for Free
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowAuthModal(true)}
                  className="border-cyan-500 text-cyan-500 hover:bg-cyan-500 hover:text-white font-poppins px-8 py-3 rounded-full text-lg transition-all duration-300"
                >
                  Sign In
                </Button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Welcome Message for Authenticated Users */}
      {user && profile && (
        <section className="py-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-y border-green-200 dark:border-green-800">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="font-montserrat font-bold text-2xl text-gray-900 dark:text-white mb-2">
                Welcome back, {profile.full_name || 'Writer'}! 👋
              </h2>
              <p className="font-poppins text-gray-600 dark:text-gray-300 mb-4">
                Ready to create something amazing? Your audience is waiting.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                <Link to="/admin/blog/new">
                  <Button className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-poppins px-6 py-2 rounded-full transition-all duration-300">
                    <Plus className="mr-2 w-4 h-4" />
                    Write New Post
                  </Button>
                </Link>
                <Link to="/admin/blog">
                  <Button variant="outline" className="border-cyan-500 text-cyan-500 hover:bg-cyan-500 hover:text-white font-poppins px-6 py-2 rounded-full transition-all duration-300">
                    <BarChart3 className="mr-2 w-4 h-4" />
                    Dashboard
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Blog Posts */}
      <section className="py-12 sm:py-16 relative overflow-hidden">
        {/* Glassmorphism background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-48 h-48 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-r from-blue-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-teal-300/10 to-cyan-300/10 rounded-full blur-lg"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-xl font-montserrat font-semibold text-gray-900 dark:text-white mb-4">
                {searchTerm ? 'No articles found' : 'No articles published yet'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 font-poppins">
                {searchTerm 
                  ? 'Try adjusting your search terms or browse all articles.' 
                  : 'Check back soon for our latest insights and tutorials.'
                }
              </p>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  variant="outline"
                  className="mt-4"
                >
                  Clear Search
                </Button>
              )}
            </div>
          ) : (
            <div className="flex gap-12">
              {/* Main Content */}
              <div className="flex-1 max-w-4xl">
                <div className="space-y-8">
                  {filteredPosts.map((post, index) => {
                    const displayAuthor = getDisplayAuthor(post)
                    const readingTime = getReadingTime(post.content)
                    const isLarge = index === 0 // Make first post larger like Medium

                    return (
                      <article key={post.id} className="group border-b border-gray-200 dark:border-gray-700 pb-8 last:border-b-0">
                        <div className="flex gap-6">
                          {/* Content - Always on the left */}
                          <div className="flex-1">
                            {/* Author and Meta */}
                            <div className="flex items-center gap-3 mb-3">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={displayAuthor.avatar_url || undefined} alt={displayAuthor.name} />
                                <AvatarFallback className="text-xs">
                                  {displayAuthor.name.charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                                {displayAuthor.name}
                              </span>
                              <span className="text-gray-400">·</span>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDate(post.published_at || post.created_at)}
                              </span>
                            </div>

                            {/* Title */}
                            <Link to={`/blog/${post.slug}`}>
                              <h2 className={`font-bold text-gray-900 dark:text-white group-hover:text-primary transition-colors mb-2 ${
                                isLarge ? 'text-3xl md:text-4xl leading-tight' : 'text-xl md:text-2xl'
                              }`}>
                                {post.title}
                              </h2>
                            </Link>

                            {/* Excerpt */}
                            <p className={`text-gray-600 dark:text-gray-300 mb-4 ${
                              isLarge ? 'text-lg leading-relaxed' : 'text-base'
                            }`}>
                              {post.excerpt || getExcerpt(post.content)}
                            </p>

                            {/* Meta Info and Engagement */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-4 w-4" />
                                  <span>{readingTime} min read</span>
                                </div>
                                {post.categories && post.categories.length > 0 && (
                                  <Badge variant="secondary" className="text-xs">
                                    {post.categories[0].category?.name}
                                  </Badge>
                                )}
                              </div>

                              {/* Engagement Actions - Medium Style */}
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                <BlogEngagement
                                  postId={post.id}
                                  postTitle={post.title}
                                  postUrl={`/blog/${post.slug}`}
                                  reactionCount={post.reaction_count || 0}
                                  commentCount={post.comment_count || 0}
                                  saveCount={post.save_count || 0}
                                  shareCount={post.share_count || 0}
                                  showCounts={false}
                                  size="sm"
                                  variant="horizontal"
                                />
                              </div>
                            </div>
                          </div>

                          {/* Thumbnail - Always on the right (Medium style) */}
                          {(post.thumbnail_url || post.featured_image) && (
                            <div className={`flex-shrink-0 overflow-hidden rounded-lg ${
                              isLarge ? 'w-48 h-32 md:w-56 md:h-36' : 'w-32 h-20 md:w-40 md:h-24'
                            }`}>
                              <img
                                src={post.thumbnail_url || post.featured_image}
                                alt={post.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              />
                            </div>
                          )}
                        </div>
                      </article>
                    )
                  })}
                </div>
              </div>

              {/* Sidebar */}
              <aside className="hidden lg:block w-80 flex-shrink-0">
                <div className="sticky top-8 space-y-8">
                  {/* Staff Picks */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Star className="h-5 w-5 text-primary" />
                      <h3 className="font-bold text-lg text-gray-900 dark:text-white">Staff Picks</h3>
                    </div>
                    <div className="space-y-4">
                      {filteredPosts.slice(0, 3).map((post) => {
                        const displayAuthor = getDisplayAuthor(post)
                        return (
                          <div key={post.id} className="group">
                            <Link to={`/blog/${post.slug}`}>
                              <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-primary transition-colors mb-1 line-clamp-2">
                                {post.title}
                              </h4>
                            </Link>
                            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                              <span>{displayAuthor.name}</span>
                              <span>·</span>
                              <span>{getReadingTime(post.content)} min read</span>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>

                  {/* Recommended Topics */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <TrendingUp className="h-5 w-5 text-primary" />
                      <h3 className="font-bold text-lg text-gray-900 dark:text-white">Recommended topics</h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {['Programming', 'Writing', 'Self Improvement', 'Data Science', 'Relationships', 'Technology'].map((topic) => (
                        <Badge key={topic} variant="outline" className="cursor-pointer hover:bg-primary hover:text-white transition-colors">
                          {topic}
                        </Badge>
                      ))}
                    </div>
                    <Button variant="ghost" className="w-full mt-4 text-primary hover:text-primary/80">
                      See more topics
                    </Button>
                  </div>
                </div>
              </aside>
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Admin Quick Access - Only visible to authenticated users */}
      <AdminQuickAccess />

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  )
}

export default Blog
