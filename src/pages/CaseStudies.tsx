import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, Clock, Users, DollarSign, TrendingUp, Plus } from 'lucide-react'
import { Link } from 'react-router-dom'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface CaseStudy {
  id: string
  title: string
  slug: string
  client_name?: string
  client_industry?: string
  project_type?: string
  challenge: string
  solution: string
  results: string
  technologies?: string[]
  timeline_months?: number
  team_size?: number
  project_value?: number
  roi_percentage?: number
  featured_image?: string
  is_featured: boolean
}

const CaseStudies = () => {
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([])
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  useEffect(() => {
    fetchCaseStudies()
  }, [])

  const fetchCaseStudies = async () => {
    try {
      const { data, error } = await supabase
        .from('case_studies')
        .select('*')
        .eq('is_approved', true)
        .order('is_featured', { ascending: false })
        .order('display_order', { ascending: true })

      if (error) {
        console.error('Error fetching case studies:', error)
        return
      }

      setCaseStudies(data || [])
    } catch (error) {
      console.error('Error fetching case studies:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-12 bg-gradient-to-b from-accent/5 to-transparent">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-montserrat font-bold text-4xl sm:text-5xl md:text-6xl text-gray-900 dark:text-white mb-6">
              Case Studies & <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Project Showcases</span>
            </h1>
            <p className="font-poppins text-lg sm:text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Deep dives into how we've helped startups and businesses build scalable platforms, raise funding, and achieve remarkable growth.
            </p>
            
            {user && (
              <Link to="/submit-case-study">
                <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 py-3">
                  <Plus className="w-4 h-4 mr-2" />
                  Submit Your Case Study
                </Button>
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6">
          {loading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : caseStudies.length === 0 ? (
            <div className="text-center py-16">
              <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="font-montserrat font-semibold text-xl text-gray-600 dark:text-gray-400 mb-2">
                No case studies available
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                Check back soon for detailed project showcases and success stories.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {caseStudies.map((caseStudy) => (
                <Card 
                  key={caseStudy.id}
                  className={`bg-white/80 dark:bg-white/5 border backdrop-blur-lg hover:shadow-xl transition-all duration-300 group ${
                    caseStudy.is_featured 
                      ? 'border-accent/50 ring-2 ring-accent/20' 
                      : 'border-white/40 dark:border-white/10'
                  }`}
                >
                  <CardContent className="p-6">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        {caseStudy.is_featured && (
                          <Badge className="mb-2 bg-gradient-to-r from-accent to-primary text-white">
                            Featured Case Study
                          </Badge>
                        )}
                        <h3 className="font-montserrat font-bold text-xl text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors">
                          {caseStudy.title}
                        </h3>
                        {caseStudy.client_name && (
                          <div className="text-sm text-primary font-medium mb-1">
                            {caseStudy.client_name}
                            {caseStudy.client_industry && ` • ${caseStudy.client_industry}`}
                          </div>
                        )}
                      </div>
                      {caseStudy.project_type && (
                        <Badge variant="outline" className="ml-4">
                          {caseStudy.project_type.toUpperCase()}
                        </Badge>
                      )}
                    </div>

                    {/* Project Metrics */}
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      {caseStudy.timeline_months && (
                        <div className="text-center">
                          <Clock className="w-4 h-4 text-blue-500 mx-auto mb-1" />
                          <div className="text-xs text-gray-600 dark:text-gray-400">Timeline</div>
                          <div className="font-semibold text-sm">{caseStudy.timeline_months}mo</div>
                        </div>
                      )}
                      {caseStudy.team_size && (
                        <div className="text-center">
                          <Users className="w-4 h-4 text-green-500 mx-auto mb-1" />
                          <div className="text-xs text-gray-600 dark:text-gray-400">Team</div>
                          <div className="font-semibold text-sm">{caseStudy.team_size} devs</div>
                        </div>
                      )}
                      {caseStudy.project_value && (
                        <div className="text-center">
                          <DollarSign className="w-4 h-4 text-purple-500 mx-auto mb-1" />
                          <div className="text-xs text-gray-600 dark:text-gray-400">Value</div>
                          <div className="font-semibold text-sm">{formatCurrency(caseStudy.project_value)}</div>
                        </div>
                      )}
                      {caseStudy.roi_percentage && (
                        <div className="text-center">
                          <TrendingUp className="w-4 h-4 text-orange-500 mx-auto mb-1" />
                          <div className="text-xs text-gray-600 dark:text-gray-400">ROI</div>
                          <div className="font-semibold text-sm">{caseStudy.roi_percentage}%</div>
                        </div>
                      )}
                    </div>

                    {/* Challenge */}
                    <div className="mb-4">
                      <h4 className="font-montserrat font-semibold text-sm text-gray-900 dark:text-white mb-2">
                        Challenge
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                        {caseStudy.challenge}
                      </p>
                    </div>

                    {/* Solution Preview */}
                    <div className="mb-4">
                      <h4 className="font-montserrat font-semibold text-sm text-gray-900 dark:text-white mb-2">
                        Solution
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                        {caseStudy.solution}
                      </p>
                    </div>

                    {/* Technologies */}
                    {caseStudy.technologies && caseStudy.technologies.length > 0 && (
                      <div className="mb-6">
                        <h4 className="font-montserrat font-semibold text-sm text-gray-900 dark:text-white mb-2">
                          Technologies
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {caseStudy.technologies.slice(0, 4).map((tech, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                          {caseStudy.technologies.length > 4 && (
                            <Badge variant="secondary" className="text-xs">
                              +{caseStudy.technologies.length - 4} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}

                    {/* CTA */}
                    <Link to={`/case-studies/${caseStudy.slug}`}>
                      <Button 
                        variant="ghost" 
                        className="w-full text-primary hover:text-primary/80 hover:bg-primary/10 font-poppins group"
                      >
                        Read Full Case Study
                        <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary/10 to-accent/10">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl text-gray-900 dark:text-white mb-6">
            Ready to Create Your Own Success Story?
          </h2>
          <p className="font-poppins text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's build something amazing together. From concept to launch, we'll help you create a platform that drives real business results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/#contact">
              <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-8 py-3 text-lg">
                Start Your Project
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
            <Link to="/testimonials">
              <Button variant="outline" className="border-primary text-primary hover:bg-primary/10 font-poppins px-8 py-3 text-lg">
                Read Testimonials
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default CaseStudies
