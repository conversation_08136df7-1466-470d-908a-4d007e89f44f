import React, { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { CheckCircle, ArrowRight, Sparkles, Zap } from 'lucide-react'
import { toast } from 'sonner'

const BillingSuccess = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { profile, refreshProfile } = useAuth()
  const { refreshOrganization } = useOrganization()
  const [sessionId] = useState(searchParams.get('session_id'))

  useEffect(() => {
    // Show success message
    toast.success('Payment successful!', {
      description: 'Your subscription has been activated. Welcome to your new plan!'
    })

    // Refresh user profile and organization data to get updated subscription info
    // Add a small delay to allow webhook processing
    const refreshData = async () => {
      try {
        // Wait a moment for webhook to process
        await new Promise(resolve => setTimeout(resolve, 2000))

        await Promise.all([
          refreshProfile?.(),
          refreshOrganization?.()
        ])

        console.log('Successfully refreshed user and organization data after payment')
      } catch (error) {
        console.error('Error refreshing data after payment:', error)
        // If refresh fails, show a message but don't break the experience
        toast.info('Subscription activated! Data will update shortly.')
      }
    }

    refreshData()
  }, [sessionId]) // Only run when sessionId changes (i.e., once on mount)

  const handleContinue = () => {
    navigate('/dashboard')
  }

  const handleViewBilling = () => {
    navigate('/billing')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
      <Navigation />

      <div className="container mx-auto px-4 pt-24 pb-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="mb-8">
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-green-600 dark:text-green-400" />
            </div>
            <h1 className="font-montserrat font-bold text-4xl text-gray-900 dark:text-white mb-4">
              Payment Successful!
            </h1>
            <p className="font-poppins text-xl text-gray-600 dark:text-gray-300">
              Welcome to your new subscription plan. You now have access to all premium features!
            </p>
          </div>

          {/* Success Card */}
          <Card className="mb-8 border-green-200 dark:border-green-800">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2 text-green-800 dark:text-green-200">
                <Sparkles className="h-5 w-5" />
                Subscription Activated
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <p>Your subscription has been successfully activated and you now have access to:</p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-4 text-left">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">Enhanced workflow automation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">Advanced AI features</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Priority support</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Increased usage limits</span>
                  </div>
                </div>
              </div>

              {sessionId && (
                <div className="text-xs text-gray-500 pt-4 border-t">
                  Session ID: {sessionId}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={handleContinue}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
            >
              Continue to Dashboard
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleViewBilling}
            >
              View Billing Details
            </Button>
          </div>

          {/* Next Steps */}
          <div className="mt-12 text-left">
            <h2 className="text-xl font-semibold mb-4 text-center">What's Next?</h2>
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">1</span>
                </div>
                <div>
                  <p className="font-medium">Explore Advanced Features</p>
                  <p className="text-xs">Check out the enhanced workflow automation and AI capabilities in your dashboard.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-purple-600 dark:text-purple-400">2</span>
                </div>
                <div>
                  <p className="font-medium">Set Up Your Workflows</p>
                  <p className="text-xs">Create powerful automation workflows to streamline your content and business processes.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-green-600 dark:text-green-400">3</span>
                </div>
                <div>
                  <p className="font-medium">Get Support</p>
                  <p className="text-xs">Need help? Our priority support team is here to assist you with any questions.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default BillingSuccess
