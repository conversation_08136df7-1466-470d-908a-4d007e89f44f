import React from 'react';
import { LogosSliderDemo } from '@/components/ui/logos-slider-demo';

const TestSlider = () => {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Infinite Slider Test</h1>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Demo Logos Slider</h2>
          <LogosSliderDemo />
        </div>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Simple Test</h2>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <p>If you can see this, the component is loading correctly.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestSlider;
