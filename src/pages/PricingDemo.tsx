import React from 'react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { PricingDemo } from '@/components/ui/pricing-demo'

const PricingDemoPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="font-montserrat font-bold text-4xl md:text-5xl text-gray-900 dark:text-white mb-4">
            New Pricing Component Demo
          </h1>
          <p className="font-poppins text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Interactive pricing component with animated transitions, confetti effects, and responsive design.
          </p>
        </div>

        <PricingDemo />
      </div>

      <Footer />
    </div>
  )
}

export default PricingDemoPage
