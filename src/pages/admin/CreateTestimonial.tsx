import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createBlogPost } from '@/lib/supabase'
import { useOrganization } from '@/contexts/OrganizationContext'
import { ArrowLeft, Save, Star, Building, User } from 'lucide-react'
import { toast } from 'sonner'

const CreateTestimonial = () => {
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    role: '',
    projectType: '',
    rating: 5,
    testimonial: '',
    results: '',
    authorOption: 'use_name', // 'use_name', 'anonymous', 'mbi_team', 'custom_name'
    customName: '', // For custom name option
    publishTo: 'testimonials' // 'testimonials', 'blog', 'both'
  })

  const projectTypes = [
    'MVP Development',
    'Custom Web Solutions', 
    'Landing Pages',
    'SaaS Platforms',
    'Digital Strategy',
    'Web Applications',
    'E-commerce',
    'Other'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user?.id) return

    setLoading(true)
    try {
      // Create a structured blog post for the testimonial
      const contentHtml = `
<h2>About ${formData.company}</h2>

<p><strong>Project Type:</strong> ${formData.projectType}</p>
<p><strong>My Role:</strong> ${formData.role}</p>

<h2>My Experience with MBI</h2>

<p>${formData.testimonial.replace(/\n/g, '</p><p>')}</p>

<h2>Results &amp; Impact</h2>

<p>${formData.results.replace(/\n/g, '</p><p>')}</p>

<h2>Rating</h2>

<p>${'⭐'.repeat(formData.rating)} (${formData.rating}/5 stars)</p>

<hr>

<p><em>This success story was shared through MBI's blog platform. Want to share your own experience? <a href="/blog">Join our blog community</a> and tell your story!</em></p>
      `.trim()

      // Generate a more unique slug to avoid conflicts
      const companySlug = formData.company.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
      const timestamp = Date.now()
      const randomSuffix = Math.random().toString(36).substring(2, 8)
      const slug = `${companySlug}-success-story-${timestamp}-${randomSuffix}`

      // Determine category and tags based on publish destination
      let category = 'blog' // Default to blog
      let tags = ['success-story', formData.projectType.toLowerCase().replace(/\s+/g, '-')]

      if (formData.publishTo === 'testimonials') {
        category = 'testimonials'
        tags = ['testimonial', 'success-story', formData.projectType.toLowerCase().replace(/\s+/g, '-')]
      } else if (formData.publishTo === 'both') {
        category = 'blog' // Set as blog so it appears in blog feed
        tags = ['testimonial', 'success-story', 'featured-testimonial', formData.projectType.toLowerCase().replace(/\s+/g, '-')]
      }

      const blogPostData: any = {
        title: `Success Story: ${formData.title}`,
        content: contentHtml, // Send as HTML string like BlogEditor does
        excerpt: `${formData.company} shares their experience working with MBI on ${formData.projectType.toLowerCase()}.`,
        slug,
        status: 'draft', // Start as draft for review
        author_id: user.id,
        author_option: formData.authorOption,
        category,
        tags,
        organization_id: currentOrganization?.id // Add organization_id
      }

      // Add custom name if the option is selected
      if (formData.authorOption === 'custom_name' && formData.customName) {
        blogPostData.custom_name = formData.customName
      }

      console.log('Creating blog post with data:', blogPostData)
      const { data, error } = await createBlogPost(blogPostData)

      if (error) {
        toast.error('Failed to create testimonial')
        console.error('Error creating testimonial:', error)
        console.error('Blog post data that failed:', blogPostData)
      } else {
        toast.success('Testimonial created successfully! It will be reviewed before publishing.')
        navigate('/admin/blog')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute requiredRole="user">
      <div className="min-h-screen bg-gradient-to-br from-cyan-50 via-blue-50 to-teal-50 dark:from-gray-900 dark:via-cyan-900/20 dark:to-blue-900/20 relative overflow-hidden">
        {/* Glassmorphism background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-10 w-60 h-60 bg-gradient-to-r from-blue-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              onClick={() => navigate('/admin/blog')}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Share Your Success Story with MBI
              </CardTitle>
              <p className="text-gray-600 dark:text-gray-400">
                Help others learn about your experience working with Millennial Business Innovations. 
                Your story will be reviewed before being featured on our testimonials page.
              </p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Story Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="e.g., How MBI Transformed Our Digital Presence"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company/Organization *</Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Your company name"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="role">Your Role *</Label>
                    <Input
                      id="role"
                      value={formData.role}
                      onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                      placeholder="e.g., CEO, Marketing Director, Founder"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="projectType">Project Type *</Label>
                    <Select
                      value={formData.projectType}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, projectType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select project type" />
                      </SelectTrigger>
                      <SelectContent>
                        {projectTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Rating */}
                <div>
                  <Label htmlFor="rating">Overall Rating *</Label>
                  <Select
                    value={formData.rating.toString()}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, rating: parseInt(value) }))}
                  >
                    <SelectTrigger className="w-full md:w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <SelectItem key={rating} value={rating.toString()}>
                          {'⭐'.repeat(rating)} ({rating}/5 stars)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Testimonial */}
                <div>
                  <Label htmlFor="testimonial">Your Experience *</Label>
                  <Textarea
                    id="testimonial"
                    value={formData.testimonial}
                    onChange={(e) => setFormData(prev => ({ ...prev, testimonial: e.target.value }))}
                    placeholder="Tell us about your experience working with MBI. What was the project about? How was the process? What did you think of the team and communication?"
                    rows={6}
                    required
                  />
                </div>

                {/* Results */}
                <div>
                  <Label htmlFor="results">Results & Impact</Label>
                  <Textarea
                    id="results"
                    value={formData.results}
                    onChange={(e) => setFormData(prev => ({ ...prev, results: e.target.value }))}
                    placeholder="What results did you achieve? How did the project impact your business? Any specific metrics or outcomes you'd like to share?"
                    rows={4}
                  />
                </div>

                {/* Author Options */}
                <div>
                  <Label htmlFor="authorOption">How would you like to be credited? *</Label>
                  <Select
                    value={formData.authorOption}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, authorOption: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="use_name">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Use my name ({profile?.full_name || profile?.email})
                        </div>
                      </SelectItem>
                      <SelectItem value="anonymous">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Publish anonymously
                        </div>
                      </SelectItem>
                      <SelectItem value="custom_name">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Use custom name
                        </div>
                      </SelectItem>
                      <SelectItem value="mbi_team">
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          Publish as MBI Client Story
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Custom Name Input - Show only when custom_name is selected */}
                {formData.authorOption === 'custom_name' && (
                  <div>
                    <Label htmlFor="customName">Custom Name *</Label>
                    <Input
                      id="customName"
                      value={formData.customName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customName: e.target.value }))}
                      placeholder="Enter the name you'd like to be credited as"
                      required
                    />
                  </div>
                )}

                {/* Publish Destination */}
                <div>
                  <Label htmlFor="publishTo">Where should this story appear? *</Label>
                  <Select
                    value={formData.publishTo}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, publishTo: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="testimonials">
                        <div className="flex flex-col">
                          <span className="font-medium">Success Stories & Testimonials Page Only</span>
                          <span className="text-sm text-gray-500">Dedicated testimonials section (recommended)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="blog">
                        <div className="flex flex-col">
                          <span className="font-medium">Blog Section Only</span>
                          <span className="text-sm text-gray-500">Regular blog post format</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="both">
                        <div className="flex flex-col">
                          <span className="font-medium">Both Testimonials & Blog</span>
                          <span className="text-sm text-gray-500">Maximum visibility across site</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.publishTo === 'testimonials' && "Your story will appear on the dedicated Success Stories & Testimonials page with structured formatting."}
                    {formData.publishTo === 'blog' && "Your story will appear as a regular blog post in the blog section."}
                    {formData.publishTo === 'both' && "Your story will appear in both the testimonials page and blog section for maximum reach."}
                  </p>
                </div>

                {/* Submit */}
                <div className="flex justify-end gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/admin/blog')}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading || !formData.title || !formData.company || !formData.role || !formData.projectType || !formData.testimonial || (formData.authorOption === 'custom_name' && !formData.customName)}
                    className="bg-gradient-to-r from-primary to-accent text-white"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Submit Story
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  )
}

export default CreateTestimonial
