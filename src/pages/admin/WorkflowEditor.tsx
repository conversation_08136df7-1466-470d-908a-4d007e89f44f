import React, { useState, useCallback, useRef, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import {
  ArrowLeft,
  Plus,
  Trash2,
  Zap,
  Settings,
  Clock,
  Webhook,
  Mail,
  Code,
  TestTube,
  Save,
  Play,
  ArrowRight,
  Filter,
  Send,
  Database,
  Slack,
  Globe,
  Move,
  MoreVertical,
  Menu,
  X,
  FileText,
  MessageCircle,
  Heart,
  AlertTriangle,
  Bell,
  MessageSquare
} from 'lucide-react'
import { toast } from 'sonner'
import { ThemeToggle } from '@/components/ThemeToggle'
import { WorkflowService, type Workflow } from '@/services/workflowService'
import { getEmailIntegrations, type EmailIntegration } from '@/lib/supabase'
import { useOrganization } from '@/contexts/OrganizationContext'

interface WorkflowNode {
  id: string
  type: 'trigger' | 'condition' | 'action'
  position: { x: number; y: number }
  data: any
  connections: string[]
}

interface DragState {
  isDragging: boolean
  nodeId: string | null
  offset: { x: number; y: number }
}

interface ConnectionState {
  isConnecting: boolean
  sourceNodeId: string | null
  sourcePosition: { x: number; y: number } | null
  currentPosition: { x: number; y: number } | null
}

const WorkflowEditor = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const [workflowName, setWorkflowName] = useState('')
  const [workflowDescription, setWorkflowDescription] = useState('')
  const [nodes, setNodes] = useState<WorkflowNode[]>([
    {
      id: 'trigger-1',
      type: 'trigger',
      position: { x: 100, y: 200 },
      data: {
        triggerType: 'new_quote',
        label: 'New Quote Received',
        description: 'Triggers when a new quote is submitted'
      },
      connections: []
    }
  ])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    nodeId: null,
    offset: { x: 0, y: 0 }
  })
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnecting: false,
    sourceNodeId: null,
    sourcePosition: null,
    currentPosition: null
  })
  const [isTestMode, setIsTestMode] = useState(false)
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [testEmail, setTestEmail] = useState('')
  const [emailIntegrations, setEmailIntegrations] = useState<EmailIntegration[]>([])
  const [showNodeConfig, setShowNodeConfig] = useState(false)
  const [configNodeId, setConfigNodeId] = useState<string | null>(null)
  const canvasRef = useRef<HTMLDivElement>(null)

  // Available variables for email templates
  const availableVariables = [
    // Contact/Quote variables
    { label: 'Contact Email', value: '{{ contact.email }}' },
    { label: 'Contact Name', value: '{{ contact.name }}' },
    { label: 'Contact Company', value: '{{ contact.company }}' },
    { label: 'Contact Phone', value: '{{ contact.phone }}' },
    { label: 'Quote Email', value: '{{ quote.email }}' },
    { label: 'Quote Name', value: '{{ quote.name }}' },
    { label: 'Quote Company', value: '{{ quote.company }}' },
    { label: 'Quote Project Type', value: '{{ quote.project_type }}' },
    { label: 'Quote Budget', value: '{{ quote.budget }}' },
    { label: 'Quote Industry', value: '{{ quote.industry }}' },
    { label: 'Quote Timeline', value: '{{ quote.timeline }}' },
    { label: 'Quote ID', value: '{{ quote.id }}' },
    // Newsletter variables
    { label: 'Subscriber Email', value: '{{ email }}' },
    { label: 'Subscriber Name', value: '{{ name }}' },
    { label: 'Subscription Source', value: '{{ source }}' },
    { label: 'Subscribed Date', value: '{{ subscribed_at }}' },
    // Generic variables
    { label: 'Name (fallback)', value: '{{ name || "there" }}' },
    { label: 'Current Date', value: '{{ current_date }}' },
    { label: 'Current Time', value: '{{ current_time }}' },
    { label: 'Company Name', value: 'Millennial Business Innovations' }
  ]

  const nodeTypes = {
    trigger: {
      icon: Zap,
      color: 'bg-blue-500',
      borderColor: 'border-blue-500',
      options: [
        { value: 'new_quote', label: 'New Quote Received', icon: Zap },
        { value: 'newsletter_signup', label: 'Newsletter Signup', icon: Mail },
        { value: 'blog_created', label: 'Blog Post Created', icon: FileText },
        { value: 'blog_comment', label: 'Blog Comment Added', icon: MessageCircle },
        { value: 'blog_reaction', label: 'Blog Post Reaction', icon: Heart },
        { value: 'low_credits', label: 'Low Credits Warning', icon: AlertTriangle },
        { value: 'status_change', label: 'Quote Status Changed', icon: Settings },
        { value: 'time_delay', label: 'Time Delay', icon: Clock }
      ]
    },
    condition: {
      icon: Filter,
      color: 'bg-yellow-500',
      borderColor: 'border-yellow-500',
      options: [
        { value: 'budget_check', label: 'Budget Amount', icon: Filter },
        { value: 'industry_check', label: 'Industry Type', icon: Filter },
        { value: 'project_type', label: 'Project Type', icon: Filter }
      ]
    },
    action: {
      icon: Send,
      color: 'bg-green-500',
      borderColor: 'border-green-500',
      options: [
        { value: 'send_email', label: 'Send Email', icon: Mail },
        { value: 'send_notification', label: 'Send Notification', icon: Bell },
        { value: 'auto_response', label: 'Auto Response', icon: MessageSquare },
        { value: 'delay', label: 'Delay/Wait', icon: Clock },
        { value: 'webhook', label: 'Send Webhook', icon: Webhook },
        { value: 'slack', label: 'Slack Notification', icon: Slack },
        { value: 'ghl_sync', label: 'GoHighLevel Sync', icon: Database },
        { value: 'custom_code', label: 'Custom Code', icon: Code }
      ]
    }
  }

  // Load existing workflow if editing
  useEffect(() => {
    const loadWorkflow = async () => {
      if (id && id !== 'new') {
        try {
          const workflow = await WorkflowService.getWorkflow(id)
          if (workflow) {
            setWorkflowName(workflow.name)
            setWorkflowDescription(workflow.description || '')
            setNodes(workflow.nodes)
          } else {
            toast.error('Workflow not found')
            navigate('/admin/automations')
          }
        } catch (error) {
          console.error('Error loading workflow:', error)
          toast.error('Failed to load workflow')
          navigate('/admin/automations')
        }
      }
    }

    loadWorkflow()
  }, [id, navigate])

  // Load email integrations
  useEffect(() => {
    const loadEmailIntegrations = async () => {
      try {
        const { data, error } = await getEmailIntegrations()
        if (error) {
          console.error('Error loading email integrations:', error)
          // Set empty array on error to prevent component crashes
          setEmailIntegrations([])
        } else {
          setEmailIntegrations(data || [])
        }
      } catch (error) {
        console.error('Error loading email integrations:', error)
        // Set empty array on error to prevent component crashes
        setEmailIntegrations([])
      }
    }

    loadEmailIntegrations()
  }, [])

  const addNode = (type: 'trigger' | 'condition' | 'action') => {
    const newNode: WorkflowNode = {
      id: `${type}-${Date.now()}`,
      type,
      position: {
        x: Math.max(50, 200 + nodes.length * 80),
        y: Math.max(50, 200 + (nodes.length % 3) * 120)
      },
      data: {
        label: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
        description: `Configure this ${type}`,
        ...(type === 'trigger' && { triggerType: 'new_quote' }),
        ...(type === 'condition' && { field: 'budget', operator: 'contains', value: '' }),
        ...(type === 'action' && { actionType: 'webhook', config: {} })
      },
      connections: []
    }
    setNodes([...nodes, newNode])
  }

  const deleteNode = (nodeId: string) => {
    setNodes(nodes.filter(node => {
      if (node.id === nodeId) return false
      // Remove connections to deleted node
      node.connections = node.connections.filter(conn => conn !== nodeId)
      return true
    }))
    if (selectedNode === nodeId) setSelectedNode(null)
  }

  const updateNodeData = (nodeId: string, data: any) => {
    setNodes(nodes.map(node =>
      node.id === nodeId ? { ...node, data: { ...node.data || {}, ...data } } : node
    ))
  }

  const updateNodePosition = (nodeId: string, position: { x: number; y: number }) => {
    setNodes(nodes.map(node =>
      node.id === nodeId ? { ...node, position } : node
    ))
  }

  const connectNodes = (fromId: string, toId: string) => {
    // Prevent self-connection and duplicate connections
    if (fromId === toId) return

    setNodes(nodes.map(node =>
      node.id === fromId
        ? { ...node, connections: [...node.connections.filter(c => c !== toId), toId] }
        : node
    ))
  }

  const disconnectNodes = (fromId: string, toId: string) => {
    setNodes(nodes.map(node =>
      node.id === fromId
        ? { ...node, connections: node.connections.filter(c => c !== toId) }
        : node
    ))
  }

  // Mouse event handlers for dragging nodes
  const handleMouseDown = (e: React.MouseEvent, nodeId: string) => {
    e.preventDefault()
    e.stopPropagation()

    const node = nodes.find(n => n.id === nodeId)
    if (!node) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const offset = {
      x: e.clientX - rect.left - node.position.x,
      y: e.clientY - rect.top - node.position.y
    }

    setDragState({
      isDragging: true,
      nodeId,
      offset
    })
    setSelectedNode(nodeId)
  }

  // Mouse event handlers for connecting nodes
  const handleConnectionStart = (e: React.MouseEvent, nodeId: string, isOutput: boolean) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isOutput) return // Only allow connections from output points

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const node = nodes.find(n => n.id === nodeId)
    if (!node) return

    // Get the actual position of the connection dot from the DOM element
    const connectionDot = e.currentTarget as HTMLElement
    const dotRect = connectionDot.getBoundingClientRect()

    // Calculate exact position of the output connection dot relative to canvas
    const sourcePosition = {
      x: dotRect.left + dotRect.width / 2 - rect.left,  // Center of the actual dot
      y: dotRect.top + dotRect.height / 2 - rect.top    // Center of the actual dot
    }

    setConnectionState({
      isConnecting: true,
      sourceNodeId: nodeId,
      sourcePosition,
      currentPosition: {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      }
    })
  }

  const handleConnectionEnd = (targetNodeId: string) => {
    if (connectionState.isConnecting && connectionState.sourceNodeId) {
      connectNodes(connectionState.sourceNodeId, targetNodeId)
    }
    setConnectionState({
      isConnecting: false,
      sourceNodeId: null,
      sourcePosition: null,
      currentPosition: null
    })
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()

    // Handle node dragging
    if (dragState.isDragging && dragState.nodeId) {
      const nodeWidth = window.innerWidth < 640 ? 160 : 192 // w-40 vs w-48
      const newPosition = {
        x: Math.max(0, Math.min(rect.width - nodeWidth, e.clientX - rect.left - dragState.offset.x)),
        y: Math.max(0, Math.min(rect.height - 100, e.clientY - rect.top - dragState.offset.y))
      }
      updateNodePosition(dragState.nodeId, newPosition)
    }

    // Handle connection dragging
    if (connectionState.isConnecting) {
      setConnectionState(prev => ({
        ...prev,
        currentPosition: {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        }
      }))
    }
  }, [dragState, connectionState.isConnecting])

  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      nodeId: null,
      offset: { x: 0, y: 0 }
    })

    // Cancel connection if not dropped on a valid target
    if (connectionState.isConnecting) {
      setConnectionState({
        isConnecting: false,
        sourceNodeId: null,
        sourcePosition: null,
        currentPosition: null
      })
    }
  }, [connectionState.isConnecting])

  useEffect(() => {
    if (dragState.isDragging || connectionState.isConnecting) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [dragState.isDragging, connectionState.isConnecting, handleMouseMove, handleMouseUp])

  const handleSave = async () => {
    if (!workflowName.trim()) {
      toast.error('Please enter a workflow name')
      return
    }

    try {
      const workflowData = {
        name: workflowName,
        description: workflowDescription,
        nodes: nodes,
        active: true
      }

      if (id === 'new') {
        await WorkflowService.createWorkflow(workflowData, currentOrganization?.id)
        toast.success('Workflow created successfully!')
      } else {
        await WorkflowService.updateWorkflow(id!, workflowData)
        toast.success('Workflow updated successfully!')
      }

      navigate('/admin/automations')
    } catch (error) {
      console.error('Error saving workflow:', error)
      toast.error('Failed to save workflow')
    }
  }

  const insertVariable = (field: string, variable: string, nodeId: string) => {
    setNodes(prevNodes => {
      return prevNodes.map(node => {
        if (node.id === nodeId) {
          if (field.startsWith('config.')) {
            const configField = field.replace('config.', '')
            return {
              ...node,
              data: {
                ...node.data,
                config: {
                  ...node.data.config,
                  [configField]: (node.data.config?.[configField] || '') + variable
                }
              }
            }
          } else {
            return {
              ...node,
              data: {
                ...node.data,
                [field]: (node.data[field] || '') + variable
              }
            }
          }
        }
        return node
      })
    })
  }

  const openNodeConfig = (nodeId: string) => {
    setConfigNodeId(nodeId)
    setShowNodeConfig(true)
  }

  const closeNodeConfig = () => {
    setShowNodeConfig(false)
    setConfigNodeId(null)
  }

  const handleTest = async () => {
    if (!workflowName.trim()) {
      toast.error('Please save the workflow first')
      return
    }

    const testWorkflow: Workflow = {
      id: id === 'new' ? 'test-workflow' : id!,
      name: workflowName,
      description: workflowDescription,
      nodes: nodes,
      active: true,
      created_by: 'test-user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Check if workflow has email actions
    if (WorkflowService.hasEmailActions(testWorkflow)) {
      setShowTestDialog(true)
      return
    }

    // Run test without email
    await runWorkflowTest(testWorkflow)
  }

  const runWorkflowTest = async (testWorkflow: Workflow, testEmailAddress?: string) => {
    setIsTestMode(true)
    try {
      await WorkflowService.testWorkflow(testWorkflow, testEmailAddress)
      toast.success('Workflow test completed successfully!')
    } catch (error) {
      console.error('Workflow test failed:', error)
      toast.error('Workflow test failed')
    } finally {
      setIsTestMode(false)
    }
  }

  const handleTestWithEmail = async () => {
    if (!testEmail.trim()) {
      toast.error('Please enter a test email address')
      return
    }

    const testWorkflow: Workflow = {
      id: id === 'new' ? 'test-workflow' : id!,
      name: workflowName,
      description: workflowDescription,
      nodes: nodes,
      active: true,
      created_by: 'test-user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    setShowTestDialog(false)
    await runWorkflowTest(testWorkflow, testEmail)
    setTestEmail('')
  }

  // Check permissions - now allow 'user' role for free tier access
  if (!profile || !['owner', 'super_admin', 'admin', 'user', 'saas_owner'].includes(profile.role)) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Card className="max-w-md">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You don't have permission to edit workflows.
              </p>
              <Button onClick={() => navigate('/admin/blog')}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-3 sm:px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/automations')}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Back to Workflows</span>
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-montserrat font-bold text-gray-900 dark:text-white truncate">
                {id === 'new' ? 'Create New Workflow' : 'Edit Workflow'}
              </h1>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 hidden sm:block">
                Build your automation workflow visually
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="sm:hidden"
            >
              <Menu className="h-4 w-4" />
            </Button>
            <ThemeToggle />
            <Button
              variant="outline"
              onClick={handleTest}
              disabled={isTestMode}
              className="hidden sm:flex"
              size="sm"
            >
              {isTestMode ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              ) : (
                <Play className="h-4 w-4 sm:mr-2" />
              )}
              <span className="hidden md:inline">Test Workflow</span>
            </Button>
            <Button onClick={handleSave} size="sm">
              <Save className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Save Workflow</span>
            </Button>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden relative">
          {/* Mobile Sidebar Overlay */}
          {sidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 sm:hidden"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Left Sidebar */}
          <div className={`${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } sm:translate-x-0 fixed sm:relative z-50 sm:z-auto w-80 sm:w-96 lg:w-[480px] xl:w-[520px] bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full transition-transform duration-300 ease-in-out`}>
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Workflow Details</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(false)}
                  className="sm:hidden"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Name</Label>
                  <Input
                    value={workflowName}
                    onChange={(e) => setWorkflowName(e.target.value)}
                    placeholder="Enter workflow name"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Description</Label>
                  <Textarea
                    value={workflowDescription}
                    onChange={(e) => setWorkflowDescription(e.target.value)}
                    placeholder="Describe what this workflow does"
                    rows={3}
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-md font-semibold mb-4">Add Components</h3>
              <div className="space-y-3">
                {Object.entries(nodeTypes).map(([type, config]) => {
                  const Icon = config.icon
                  return (
                    <Button
                      key={type}
                      variant="outline"
                      className="w-full justify-start h-auto p-3"
                      onClick={() => addNode(type as any)}
                    >
                      <div className={`w-8 h-8 rounded-lg ${config.color} flex items-center justify-center mr-3 flex-shrink-0`}>
                        <Icon className="h-4 w-4 text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">
                          Add {type.charAt(0).toUpperCase() + type.slice(1)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {type === 'trigger' && 'Start your workflow'}
                          {type === 'condition' && 'Add logic conditions'}
                          {type === 'action' && 'Perform actions'}
                        </div>
                      </div>
                    </Button>
                  )
                })}
              </div>
            </div>

            {/* Node Properties */}
            {selectedNode && (
              <div className="flex-1 p-6 overflow-y-auto">
                <h3 className="text-md font-semibold mb-4">Properties</h3>
                {(() => {
                  const selectedNodeData = nodes.find(n => n.id === selectedNode)
                  if (!selectedNodeData) return null

                  return (
                    <NodePropertiesPanel
                      node={selectedNodeData}
                      onUpdate={(data) => updateNodeData(selectedNode, data)}
                      nodeTypes={nodeTypes}
                      emailIntegrations={emailIntegrations}
                      availableVariables={availableVariables}
                      onInsertVariable={insertVariable}
                    />
                  )
                })()}
              </div>
            )}
          </div>

          {/* Main Canvas */}
          <div className="flex-1 relative overflow-hidden w-full sm:w-auto">
            <div
              ref={canvasRef}
              className="w-full h-full relative bg-gray-50 dark:bg-gray-900 overflow-auto"
              style={{
                backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
                backgroundSize: '20px 20px'
              }}
            >
              {/* Render connections */}
              <svg className="absolute inset-0 pointer-events-none z-10" style={{ width: '100%', height: '100%' }}>
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#6b7280"
                    />
                  </marker>
                  <marker
                    id="arrowhead-temp"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#3b82f6"
                    />
                  </marker>
                </defs>

                {/* Existing connections */}
                {nodes.map(node =>
                  node.connections.map(targetId => {
                    const targetNode = nodes.find(n => n.id === targetId)
                    if (!targetNode) return null

                    // Try to get actual DOM element positions for precise positioning
                    const sourceElement = document.querySelector(`[data-node-id="${node.id}"] [data-connection-type="output"]`)
                    const targetElement = document.querySelector(`[data-node-id="${targetId}"] [data-connection-type="input"]`)

                    let startX, startY, endX, endY

                    if (sourceElement && targetElement && canvasRef.current) {
                      // Use actual DOM positions for perfect accuracy
                      const canvasRect = canvasRef.current.getBoundingClientRect()
                      const sourceRect = sourceElement.getBoundingClientRect()
                      const targetRect = targetElement.getBoundingClientRect()

                      startX = sourceRect.left + sourceRect.width / 2 - canvasRect.left
                      startY = sourceRect.top + sourceRect.height / 2 - canvasRect.top
                      endX = targetRect.left + targetRect.width / 2 - canvasRect.left
                      endY = targetRect.top + targetRect.height / 2 - canvasRect.top

                      console.log(`DOM-based connection: ${node.id} -> ${targetId}`, {
                        startX, startY, endX, endY,
                        sourceRect, targetRect, canvasRect
                      })
                    } else {
                      // Fallback to calculated positions
                      startX = node.position.x + 192           // Right edge of node (192px width)
                      startY = node.position.y + 70            // Estimated middle of card
                      endX = targetNode.position.x             // Left edge of target node
                      endY = targetNode.position.y + 70        // Estimated middle of card

                      console.log(`Fallback connection: ${node.id} -> ${targetId}`, {
                        startX, startY, endX, endY,
                        reason: 'DOM elements not found'
                      })
                    }

                    const midX = (startX + endX) / 2

                    return (
                      <path
                        key={`${node.id}-${targetId}`}
                        d={`M ${startX} ${startY} Q ${midX} ${startY} ${endX} ${endY}`}
                        stroke="#6b7280"
                        strokeWidth="2"
                        fill="none"
                        markerEnd="url(#arrowhead)"
                        className="hover:stroke-red-500 cursor-pointer"
                        onClick={() => disconnectNodes(node.id, targetId)}
                      />
                    )
                  })
                )}

                {/* Temporary connection line while dragging */}
                {connectionState.isConnecting && connectionState.sourcePosition && connectionState.currentPosition && (
                  <path
                    d={`M ${connectionState.sourcePosition.x} ${connectionState.sourcePosition.y} Q ${(connectionState.sourcePosition.x + connectionState.currentPosition.x) / 2} ${connectionState.sourcePosition.y} ${connectionState.currentPosition.x} ${connectionState.currentPosition.y}`}
                    stroke="#3b82f6"
                    strokeWidth="2"
                    fill="none"
                    strokeDasharray="5,5"
                    markerEnd="url(#arrowhead-temp)"
                  />
                )}
              </svg>

              {/* Render nodes */}
              {nodes.map((node) => (
                <WorkflowNodeComponent
                  key={node.id}
                  node={node}
                  nodes={nodes}
                  isSelected={selectedNode === node.id}
                  isDragging={dragState.isDragging && dragState.nodeId === node.id}
                  nodeTypes={nodeTypes}
                  onMouseDown={(e) => handleMouseDown(e, node.id)}
                  onSelect={() => setSelectedNode(node.id)}
                  onDelete={() => deleteNode(node.id)}
                  onOpenConfig={() => openNodeConfig(node.id)}
                  onConnectionStart={(e, isOutput) => handleConnectionStart(e, node.id, isOutput)}
                  onConnectionEnd={() => handleConnectionEnd(node.id)}
                  isConnectionTarget={connectionState.isConnecting && connectionState.sourceNodeId !== node.id && node.type !== 'trigger'}
                />
              ))}

              {nodes.length === 1 && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="text-center text-gray-500">
                    <ArrowRight className="h-16 w-16 mx-auto mb-6 opacity-30" />
                    <p className="text-xl font-medium mb-2">Build Your Workflow</p>
                    <p className="text-sm mb-2">Add components from the sidebar to create your automation</p>
                    <p className="text-xs text-gray-400">💡 Tip: Drag from blue dots to connect nodes</p>
                  </div>
                </div>
              )}

              {connectionState.isConnecting && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-lg shadow-lg pointer-events-none z-20">
                  <p className="text-sm font-medium">Drop on a green connection point to connect</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Test Email Dialog */}
        <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Test Workflow</DialogTitle>
              <DialogDescription>
                This workflow contains email actions. Please enter a test email address to receive the test emails.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="test-email">Test Email Address</Label>
                <Input
                  id="test-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowTestDialog(false)
                    setTestEmail('')
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleTestWithEmail}
                  disabled={isTestMode}
                >
                  {isTestMode ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Run Test
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Full-Screen Node Configuration Modal */}
        <Dialog open={showNodeConfig} onOpenChange={setShowNodeConfig}>
          <DialogContent className="max-w-none w-screen h-screen m-0 p-0 rounded-none">
            {configNodeId && (() => {
              const configNode = nodes.find(n => n.id === configNodeId)
              if (!configNode) return null

              const nodeType = nodeTypes[configNode.type]
              const Icon = nodeType.icon

              return (
                <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-900">
                  {/* Header */}
                  <div className="flex items-center justify-between p-6 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={closeNodeConfig}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        Back to canvas
                      </Button>
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 rounded-lg ${nodeType.color} flex items-center justify-center`}>
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {configNode.data.label || `${configNode.type.charAt(0).toUpperCase() + configNode.type.slice(1)} Configuration`}
                          </h1>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Configure your {configNode.type} settings
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        onClick={closeNodeConfig}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={closeNodeConfig}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 flex">
                    {/* Main Configuration Area */}
                    <div className="flex-1 p-8 overflow-y-auto">
                      <div className="max-w-2xl">
                        <NodePropertiesPanel
                          node={configNode}
                          onUpdate={(data) => updateNodeData(configNodeId, data)}
                          nodeTypes={nodeTypes}
                          emailIntegrations={emailIntegrations}
                          availableVariables={availableVariables}
                          onInsertVariable={insertVariable}
                        />
                      </div>
                    </div>

                    {/* Right Sidebar - Documentation/Help */}
                    <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                        {configNode.type === 'trigger' && 'Trigger Documentation'}
                        {configNode.type === 'condition' && 'Condition Documentation'}
                        {configNode.type === 'action' && 'Action Documentation'}
                      </h3>

                      {configNode.type === 'trigger' && (
                        <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
                          <p>Triggers start your workflow when specific events occur.</p>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Available Triggers:</h4>
                            <ul className="space-y-1">
                              <li>• <strong>New Quote:</strong> Fires when a quote is submitted</li>
                              <li>• <strong>Newsletter Signup:</strong> Fires when someone subscribes to newsletter</li>
                              <li>• <strong>Status Change:</strong> Fires when quote status changes</li>
                              <li>• <strong>Time Delay:</strong> Fires after a specified time</li>
                            </ul>
                          </div>
                        </div>
                      )}

                      {configNode.type === 'condition' && (
                        <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
                          <p>Conditions control the flow of your workflow based on data.</p>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Operators:</h4>
                            <ul className="space-y-1">
                              <li>• <strong>Contains:</strong> Field contains the value</li>
                              <li>• <strong>Equals:</strong> Field exactly matches the value</li>
                              <li>• <strong>Greater Than:</strong> Numeric comparison</li>
                              <li>• <strong>Less Than:</strong> Numeric comparison</li>
                            </ul>
                          </div>
                        </div>
                      )}

                      {configNode.type === 'action' && (
                        <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
                          <p>Actions perform tasks when the workflow reaches this step.</p>

                          {(configNode.data.actionType === 'email' || configNode.data.actionType === 'send_email') && (
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Email Variables:</h4>
                              <div className="space-y-2">
                                {availableVariables.map((variable) => (
                                  <div key={variable.value} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                    <span className="font-mono text-xs">{variable.value}</span>
                                    <span className="text-xs">{variable.label}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {configNode.data.actionType === 'webhook' && (
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Webhook Tips:</h4>
                              <ul className="space-y-1">
                                <li>• Use HTTPS URLs for security</li>
                                <li>• Test your endpoint before saving</li>
                                <li>• Check webhook logs for debugging</li>
                              </ul>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })()}
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedRoute>
  )
}

// Individual workflow node component
const WorkflowNodeComponent: React.FC<{
  node: WorkflowNode
  nodes: WorkflowNode[]
  isSelected: boolean
  isDragging: boolean
  nodeTypes: any
  onMouseDown: (e: React.MouseEvent) => void
  onSelect: () => void
  onDelete: () => void
  onOpenConfig: () => void
  onConnectionStart: (e: React.MouseEvent, isOutput: boolean) => void
  onConnectionEnd: () => void
  isConnectionTarget: boolean
}> = ({
  node,
  nodes,
  isSelected,
  isDragging,
  nodeTypes,
  onMouseDown,
  onSelect,
  onDelete,
  onOpenConfig,
  onConnectionStart,
  onConnectionEnd,
  isConnectionTarget
}) => {
  const nodeType = nodeTypes[node.type]
  const Icon = nodeType.icon

  return (
    <div
      className={`absolute cursor-move transition-all duration-200 z-20 ${
        isSelected ? 'scale-105' : 'scale-100'
      } ${isDragging ? 'z-30' : ''}`}
      data-node-id={node.id}
      style={{
        left: node.position.x,
        top: node.position.y,
        transform: isDragging ? 'rotate(2deg)' : 'rotate(0deg)'
      }}
      onMouseDown={(e) => {
        // Only start node dragging if not clicking on connection points
        const target = e.target as HTMLElement
        if (!target.closest('.connection-point')) {
          onMouseDown(e)
        }
      }}
      onClick={onSelect}
    >
      <Card className={`w-40 sm:w-48 ${isSelected ? `ring-2 ring-blue-500 shadow-xl` : 'shadow-lg'} ${isConnectionTarget ? 'ring-2 ring-green-500 bg-green-50 dark:bg-green-900/20' : ''} hover:shadow-xl transition-all bg-white dark:bg-gray-800 group`}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className={`w-10 h-10 rounded-lg ${nodeType.color} flex items-center justify-center flex-shrink-0`}>
              <Icon className="h-5 w-5 text-white" />
            </div>
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-blue-100 hover:text-blue-600"
                onClick={(e) => {
                  e.stopPropagation()
                  onOpenConfig()
                }}
              >
                <Settings className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={`text-xs ${nodeType.borderColor}`}>
                {node.type}
              </Badge>
            </div>

            <h4 className="font-semibold text-sm text-gray-900 dark:text-white">
              {node.data.label}
            </h4>

            <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
              {node.data.description}
            </p>

            {node.type === 'trigger' && (
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {nodeTypes.trigger.options.find((o: any) => o.value === node.data.triggerType)?.label}
              </p>
            )}

            {node.type === 'condition' && node.data.field && (
              <p className="text-xs text-yellow-600 dark:text-yellow-400">
                {node.data.field} {node.data.operator} {node.data.value}
              </p>
            )}

            {node.type === 'action' && node.data.actionType && (
              <p className="text-xs text-green-600 dark:text-green-400">
                {nodeTypes.action.options.find((o: any) => o.value === node.data.actionType)?.label}
              </p>
            )}
          </div>

          {/* Output connection point - all nodes can have outputs */}
          <div
            className="connection-point absolute -right-3 top-1/2 transform -translate-y-1/2 cursor-crosshair z-30"
            data-connection-type="output"
            onMouseDown={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onConnectionStart(e, true)
            }}
          >
            <div className="w-6 h-6 bg-white dark:bg-gray-700 border-2 border-blue-500 rounded-full flex items-center justify-center hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            </div>
          </div>

          {/* Input connection point - all nodes except triggers can have inputs */}
          {node.type !== 'trigger' && (() => {
            // Check if this node has incoming connections by looking at all other nodes' connections arrays
            const hasIncomingConnection = nodes.some(n => n.connections.includes(node.id))

            return (
              <div
                className="connection-point absolute -left-3 top-1/2 transform -translate-y-1/2 cursor-crosshair z-30"
                data-connection-type="input"
                onMouseDown={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}
                onMouseUp={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  if (isConnectionTarget) {
                    onConnectionEnd()
                  }
                }}
                onMouseEnter={() => {
                  if (isConnectionTarget) {
                    // Visual feedback when hovering over valid target
                  }
                }}
              >
                <div className={`w-6 h-6 bg-white dark:bg-gray-700 border-2 ${
                  isConnectionTarget
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : hasIncomingConnection
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-300 dark:border-gray-600'
                } rounded-full flex items-center justify-center hover:border-green-500 transition-colors`}>
                  <div className={`w-2 h-2 ${
                    isConnectionTarget
                      ? 'bg-green-500'
                      : hasIncomingConnection
                        ? 'bg-blue-500'
                        : 'bg-gray-400'
                  } rounded-full`}></div>
                </div>
              </div>
            )
          })()}
        </CardContent>
      </Card>
    </div>
  )
}

// Node properties panel component
const NodePropertiesPanel: React.FC<{
  node: WorkflowNode
  onUpdate: (data: any) => void
  nodeTypes: any
  emailIntegrations: EmailIntegration[]
  availableVariables: Array<{ label: string; value: string }>
  onInsertVariable: (field: string, variable: string, nodeId: string) => void
}> = ({ node, onUpdate, nodeTypes, emailIntegrations, availableVariables, onInsertVariable }) => {
  // Safety check for node data
  if (!node || !node.data) {
    return <div className="text-red-500 text-sm">Error: Invalid node data</div>
  }

  if (node.type === 'trigger') {
    return (
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Label</Label>
          <Input
            value={node.data.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            placeholder="Enter trigger name"
            className="mt-1"
          />
        </div>
        <div>
          <Label className="text-sm font-medium">Trigger Type</Label>
          <Select
            value={node.data.triggerType}
            onValueChange={(value) => onUpdate({ triggerType: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {nodeTypes.trigger.options.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="text-sm font-medium">Description</Label>
          <Textarea
            value={node.data.description}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Describe when this trigger fires"
            rows={2}
            className="mt-1"
          />
        </div>
      </div>
    )
  }

  if (node.type === 'condition') {
    return (
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Label</Label>
          <Input
            value={node.data.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            placeholder="Enter condition name"
            className="mt-1"
          />
        </div>
        <div>
          <Label className="text-sm font-medium">Field</Label>
          <Select
            value={node.data.field}
            onValueChange={(value) => onUpdate({ field: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="budget">Budget</SelectItem>
              <SelectItem value="industry">Industry</SelectItem>
              <SelectItem value="project_type">Project Type</SelectItem>
              <SelectItem value="timeline">Timeline</SelectItem>
              <SelectItem value="company">Company</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="text-sm font-medium">Operator</Label>
          <Select
            value={node.data.operator}
            onValueChange={(value) => onUpdate({ operator: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="contains">Contains</SelectItem>
              <SelectItem value="equals">Equals</SelectItem>
              <SelectItem value="greater_than">Greater Than</SelectItem>
              <SelectItem value="less_than">Less Than</SelectItem>
              <SelectItem value="not_equals">Not Equals</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="text-sm font-medium">Value</Label>
          <Input
            value={node.data.value}
            onChange={(e) => onUpdate({ value: e.target.value })}
            placeholder="Enter comparison value"
            className="mt-1"
          />
        </div>
        <div>
          <Label className="text-sm font-medium">Description</Label>
          <Textarea
            value={node.data.description}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Describe this condition"
            rows={2}
            className="mt-1"
          />
        </div>
      </div>
    )
  }

  if (node.type === 'action') {
    return (
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Label</Label>
          <Input
            value={node.data.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            placeholder="Enter action name"
            className="mt-1"
          />
        </div>
        <div>
          <Label className="text-sm font-medium">Action Type</Label>
          <Select
            value={node.data.actionType}
            onValueChange={(value) => onUpdate({ actionType: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {nodeTypes.action.options.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {node.data.actionType === 'webhook' && (
          <div>
            <Label className="text-sm font-medium">Webhook URL</Label>
            <Input
              value={node.data.config?.url || ''}
              onChange={(e) => onUpdate({
                config: { ...node.data.config, url: e.target.value }
              })}
              placeholder="https://hooks.slack.com/..."
              className="mt-1 font-mono text-sm"
            />
          </div>
        )}

        {(node.data.actionType === 'email' || node.data.actionType === 'send_email') && (
          <>
            <div>
              <Label className="text-sm font-medium">Email Integration</Label>
              <Select
                value={node.data.config?.integrationId || 'none'}
                onValueChange={(value) => onUpdate({
                  config: { ...node.data.config, integrationId: value === 'none' ? '' : value }
                })}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select email service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No integration selected</SelectItem>
                  {emailIntegrations.filter(integration => integration.is_active).map(integration => (
                    <SelectItem key={integration.id} value={integration.id}>
                      {integration.name} ({integration.provider})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">To Email</Label>
                <Select
                  value=""
                  onValueChange={(value) => {
                    onInsertVariable('config.to', value, node.id)
                  }}
                >
                  <SelectTrigger className="w-auto h-6 text-xs">
                    <SelectValue placeholder="+ Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableVariables.map((variable) => (
                      <SelectItem key={variable.value} value={variable.value}>
                        {variable.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Input
                value={node.data.config?.to || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, to: e.target.value }
                })}
                placeholder="<EMAIL> or {{ contact.email }}"
                className="mt-1"
              />
            </div>
            <div>
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Subject</Label>
                <Select
                  value=""
                  onValueChange={(value) => {
                    onInsertVariable('config.subject', value, node.id)
                  }}
                >
                  <SelectTrigger className="w-auto h-6 text-xs">
                    <SelectValue placeholder="+ Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableVariables.map((variable) => (
                      <SelectItem key={variable.value} value={variable.value}>
                        {variable.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Input
                value={node.data.config?.subject || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, subject: e.target.value }
                })}
                placeholder="Email subject or {{ quote.project_type }} Quote"
                className="mt-1"
              />
            </div>
            <div>
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Email Body</Label>
                <Select
                  value=""
                  onValueChange={(value) => {
                    onInsertVariable('config.body', value, node.id)
                  }}
                >
                  <SelectTrigger className="w-auto h-6 text-xs">
                    <SelectValue placeholder="+ Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableVariables.map((variable) => (
                      <SelectItem key={variable.value} value={variable.value}>
                        {variable.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Textarea
                value={node.data.config?.body || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, body: e.target.value }
                })}
                placeholder="Email content with {{ variables }}"
                className="mt-1 min-h-[120px]"
              />
            </div>
          </>
        )}

        {node.data.actionType === 'delay' && (
          <div>
            <Label className="text-sm font-medium">Delay Duration</Label>
            <div className="grid grid-cols-2 gap-2 mt-1">
              <Input
                type="number"
                value={node.data.config?.delayValue || ''}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0
                  const unit = node.data.config?.delayUnit || 'minutes'
                  const delayMs = convertToMilliseconds(value, unit)
                  onUpdate({
                    config: {
                      ...node.data.config,
                      delayValue: value,
                      delayUnit: unit,
                      delayMs
                    }
                  })
                }}
                placeholder="5"
                min="1"
              />
              <Select
                value={node.data.config?.delayUnit || 'minutes'}
                onValueChange={(unit) => {
                  const value = parseInt(node.data.config?.delayValue) || 0
                  const delayMs = convertToMilliseconds(value, unit)
                  onUpdate({
                    config: {
                      ...node.data.config,
                      delayValue: value,
                      delayUnit: unit,
                      delayMs
                    }
                  })
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seconds">Seconds</SelectItem>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                  <SelectItem value="days">Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Workflow will pause for this duration before continuing to the next step
            </p>
          </div>
        )}

        <div>
          <Label className="text-sm font-medium">Description</Label>
          <Textarea
            value={node.data.description}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Describe what this action does"
            rows={2}
            className="mt-1"
          />
        </div>
      </div>
    )
  }

  return null
}

// Helper function to convert delay to milliseconds
function convertToMilliseconds(value: number, unit: string): number {
  switch (unit) {
    case 'seconds': return value * 1000
    case 'minutes': return value * 60 * 1000
    case 'hours': return value * 60 * 60 * 1000
    case 'days': return value * 24 * 60 * 60 * 1000
    default: return value * 60 * 1000 // default to minutes
  }
}

export default WorkflowEditor