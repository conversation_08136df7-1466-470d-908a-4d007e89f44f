import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Eye,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Search,
  Filter,
  MessageSquare,
  Globe
} from 'lucide-react'
import { toast } from 'sonner'
import { getBlogPosts, updateBlogPost } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { formatDistanceToNow } from 'date-fns'

interface BlogPost {
  id: string
  title: string
  excerpt?: string
  status: string
  submission_status?: string
  author?: { full_name?: string; email: string }
  published_at?: string
  created_at: string
  organization_id?: string
  is_mbi_featured?: boolean
}

const ModerationDashboard: React.FC = () => {
  const { profile } = useAuth()
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'published_mbi' | 'flagged'>('published_mbi')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null)
  const [moderationAction, setModerationAction] = useState<'approve' | 'flag' | 'remove' | null>(null)
  const [moderationNotes, setModerationNotes] = useState('')
  const [showModerationDialog, setShowModerationDialog] = useState(false)

  useEffect(() => {
    fetchPosts()
  }, [filter])

  const fetchPosts = async () => {
    setLoading(true)
    try {
      // Fetch posts that need moderation (published to MBI)
      const { data, error } = await getBlogPosts('published', false, undefined, false)
      
      if (error) {
        console.error('Error fetching posts:', error)
        toast.error('Failed to load posts')
        return
      }

      // Filter posts based on current filter
      let filteredPosts = data || []
      if (filter === 'published_mbi') {
        filteredPosts = filteredPosts.filter(post => 
          post.submission_status === 'published_mbi' || post.is_mbi_featured
        )
      }

      setPosts(filteredPosts)
    } catch (error) {
      console.error('Error:', error)
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleModeration = async () => {
    if (!selectedPost || !moderationAction) return

    try {
      let updateData: any = {}
      
      switch (moderationAction) {
        case 'approve':
          updateData = { 
            is_mbi_featured: true,
            submission_status: 'published_mbi'
          }
          break
        case 'flag':
          updateData = { 
            status: 'flagged',
            moderation_notes: moderationNotes
          }
          break
        case 'remove':
          updateData = { 
            status: 'archived',
            submission_status: 'rejected',
            moderation_notes: moderationNotes
          }
          break
      }

      const { error } = await updateBlogPost(selectedPost.id, updateData)
      
      if (error) {
        toast.error('Failed to moderate post')
        return
      }

      toast.success(`Post ${moderationAction}d successfully`)
      setShowModerationDialog(false)
      setSelectedPost(null)
      setModerationAction(null)
      setModerationNotes('')
      fetchPosts()
    } catch (error) {
      console.error('Error moderating post:', error)
      toast.error('An unexpected error occurred')
    }
  }

  const openModerationDialog = (post: BlogPost, action: 'approve' | 'flag' | 'remove') => {
    setSelectedPost(post)
    setModerationAction(action)
    setShowModerationDialog(true)
  }

  const filteredPosts = posts.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.author?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.author?.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!profile || !['admin', 'super_admin', 'owner', 'saas_owner'].includes(profile.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Restricted</h2>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access the moderation dashboard.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Content Moderation</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Review and moderate published content
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {filteredPosts.length} posts to review
        </Badge>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex gap-2">
              <Button
                variant={filter === 'published_mbi' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('published_mbi')}
              >
                <Globe className="h-4 w-4 mr-2" />
                MBI Published
              </Button>
              <Button
                variant={filter === 'flagged' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('flagged')}
              >
                <Flag className="h-4 w-4 mr-2" />
                Flagged
              </Button>
              <Button
                variant={filter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('all')}
              >
                <Filter className="h-4 w-4 mr-2" />
                All
              </Button>
            </div>
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search posts, authors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Posts List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading posts...</p>
          </div>
        ) : filteredPosts.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No posts to moderate</h3>
              <p className="text-gray-600 dark:text-gray-400">
                All posts are up to date with moderation.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredPosts.map((post) => (
            <Card key={post.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold">{post.title}</h3>
                      {post.is_mbi_featured && (
                        <Badge variant="secondary" className="text-xs">
                          Featured
                        </Badge>
                      )}
                      {post.submission_status === 'published_mbi' && (
                        <Badge variant="outline" className="text-xs">
                          MBI Published
                        </Badge>
                      )}
                    </div>
                    
                    {post.excerpt && (
                      <p className="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {post.excerpt}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>By {post.author?.full_name || post.author?.email}</span>
                      <span>•</span>
                      <span>
                        {post.published_at 
                          ? formatDistanceToNow(new Date(post.published_at), { addSuffix: true })
                          : formatDistanceToNow(new Date(post.created_at), { addSuffix: true })
                        }
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`/blog/${post.id}`, '_blank')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openModerationDialog(post, 'approve')}
                      className="text-green-600 hover:text-green-700"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openModerationDialog(post, 'flag')}
                      className="text-yellow-600 hover:text-yellow-700"
                    >
                      <Flag className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openModerationDialog(post, 'remove')}
                      className="text-red-600 hover:text-red-700"
                    >
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Moderation Dialog */}
      <Dialog open={showModerationDialog} onOpenChange={setShowModerationDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {moderationAction === 'approve' && 'Approve Post'}
              {moderationAction === 'flag' && 'Flag Post'}
              {moderationAction === 'remove' && 'Remove Post'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {selectedPost && (
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold">{selectedPost.title}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  By {selectedPost.author?.full_name || selectedPost.author?.email}
                </p>
              </div>
            )}
            
            {(moderationAction === 'flag' || moderationAction === 'remove') && (
              <div>
                <label className="block text-sm font-medium mb-2">
                  Reason {moderationAction === 'remove' ? '(Required)' : '(Optional)'}
                </label>
                <Textarea
                  value={moderationNotes}
                  onChange={(e) => setModerationNotes(e.target.value)}
                  placeholder={`Explain why you're ${moderationAction}ing this post...`}
                  rows={3}
                />
              </div>
            )}
            
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowModerationDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleModeration}
                disabled={moderationAction === 'remove' && !moderationNotes.trim()}
                className={
                  moderationAction === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                  moderationAction === 'flag' ? 'bg-yellow-600 hover:bg-yellow-700' :
                  'bg-red-600 hover:bg-red-700'
                }
              >
                {moderationAction === 'approve' && 'Approve'}
                {moderationAction === 'flag' && 'Flag'}
                {moderationAction === 'remove' && 'Remove'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ModerationDashboard
