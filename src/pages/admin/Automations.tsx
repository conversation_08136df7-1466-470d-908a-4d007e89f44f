import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { Button } from '@/components/ui/button'
import WorkflowCreditBanner from '@/components/WorkflowCreditBanner'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  ArrowLeft,
  Plus,
  Settings,
  Zap,
  Clock,
  Globe,
  Mail,
  Webhook,
  Play,
  Pause,
  Edit,
  Trash2,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Copy,
  Star,
  Download,
  Upload,
  Eye,
  Calendar,
  User
} from 'lucide-react'
import { ThemeToggle } from '@/components/ThemeToggle'
import { WorkflowService, type Workflow, type WorkflowExecution } from '@/services/workflowService'
import { useUpsell } from '@/components/UpsellToast'
import UpgradePrompt from '@/components/UpgradePrompt'
import { useOrganization } from '@/contexts/OrganizationContext'

import { toast } from 'sonner'

// Use the types from the service
type AutomationRule = Workflow
type ExecutionLog = WorkflowExecution

const Automations = () => {
  const { profile } = useAuth()
  const navigate = useNavigate()
  const { checkWorkflowLimit } = useUpsell()
  const {
    currentOrganization,
    workflowCreditsRemaining,
    isNearWorkflowLimit,
    canUseWorkflows
  } = useOrganization()
  const [activeTab, setActiveTab] = useState('rules')
  const [rules, setRules] = useState<AutomationRule[]>([])
  const [logs, setLogs] = useState<ExecutionLog[]>([])
  const [templates, setTemplates] = useState<AutomationRule[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalWorkflows: 0,
    activeWorkflows: 0,
    totalExecutions: 0,
    successRate: 0
  })

  // Load real automation data from Supabase
  useEffect(() => {
    const loadAutomations = async () => {
      // SaaS Owner doesn't need an organization context - they can see everything
      if (!currentOrganization?.id && profile?.role !== 'saas_owner') return

      try {
        // For super admins and saas_owner, don't filter by organization (they can see all)
        const orgId = (profile?.role === 'super_admin' || profile?.role === 'saas_owner') ? undefined : currentOrganization.id

        const [workflowsData, executionsData, statsData, templatesData] = await Promise.all([
          WorkflowService.getWorkflows(orgId),
          WorkflowService.getWorkflowExecutions(undefined, orgId),
          WorkflowService.getWorkflowStats(orgId),
          WorkflowService.getWorkflowTemplates()
        ])

        setRules(workflowsData)
        setLogs(executionsData)
        setStats(statsData)
        setTemplates(templatesData)
        setLoading(false)
      } catch (error) {
        console.error('Error loading automations:', error)
        toast.error('Failed to load workflows')
        setLoading(false)
      }
    }

    loadAutomations()
  }, [currentOrganization?.id, profile?.role])

  const handleToggleRule = async (ruleId: string, active: boolean) => {
    try {
      await WorkflowService.toggleWorkflowStatus(ruleId, active)
      setRules(prev => prev.map(rule =>
        rule.id === ruleId ? { ...rule, active } : rule
      ))
      toast.success(`Workflow ${active ? 'enabled' : 'disabled'}`)
    } catch (error) {
      console.error('Error toggling workflow status:', error)
      toast.error('Failed to update workflow status')
    }
  }

  const handleCreateTemplate = async (workflowId: string) => {
    try {
      await WorkflowService.createTemplate(workflowId)
      toast.success('Template created successfully!')

      // Reload templates
      const templatesData = await WorkflowService.getWorkflowTemplates()
      setTemplates(templatesData)
    } catch (error) {
      console.error('Error creating template:', error)
      toast.error('Failed to create template')
    }
  }

  const handleUseTemplate = async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId)
      if (!template) return

      // Create new workflow from template
      const workflowData = {
        name: template.name.replace(' Template', ''),
        description: template.description?.replace('Template based on: ', '') || '',
        nodes: template.nodes,
        active: false
      }

      // Pass the current organization ID to ensure the workflow is associated with the user's organization
      const newWorkflow = await WorkflowService.createWorkflow(workflowData, currentOrganization?.id)

      // Reload workflows to show the new one in the list
      const orgId = (profile?.role === 'super_admin' || profile?.role === 'saas_owner') ? undefined : currentOrganization?.id
      const workflowsData = await WorkflowService.getWorkflows(orgId)
      setRules(workflowsData)

      toast.success('Workflow created from template!')
      navigate(`/admin/automations/editor/${newWorkflow.id}`)
    } catch (error) {
      console.error('Error using template:', error)
      toast.error('Failed to create workflow from template')
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return

    try {
      await WorkflowService.deleteTemplate(templateId)
      toast.success('Template deleted successfully!')

      // Remove from local state
      setTemplates(prev => prev.filter(t => t.id !== templateId))
    } catch (error) {
      console.error('Error deleting template:', error)
      toast.error('Failed to delete template')
    }
  }

  const getTriggerInfo = (nodes: any[]) => {
    const triggerNode = nodes.find(node => node.type === 'trigger')
    if (!triggerNode) return { icon: <Activity className="h-4 w-4" />, label: 'No Trigger' }

    switch (triggerNode.data?.triggerType) {
      case 'new_quote': return { icon: <Zap className="h-4 w-4" />, label: 'New Quote' }
      case 'newsletter_signup': return { icon: <Mail className="h-4 w-4" />, label: 'Newsletter Signup' }
      case 'status_change': return { icon: <Settings className="h-4 w-4" />, label: 'Status Change' }
      case 'time_delay': return { icon: <Clock className="h-4 w-4" />, label: 'Time Delay' }
      default: return { icon: <Activity className="h-4 w-4" />, label: 'Custom Trigger' }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  // Check if user has permissions - now allow 'user' role for free tier access
  if (!profile || !['owner', 'super_admin', 'admin', 'user', 'saas_owner'].includes(profile.role)) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Card className="max-w-md">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You don't have permission to view automations.
              </p>
              <Link to="/admin/blog">
                <Button>Go to Dashboard</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-cyan-50 via-blue-50 to-teal-50 dark:from-gray-900 dark:via-cyan-900/20 dark:to-blue-900/20 relative overflow-hidden">
        {/* Glassmorphism background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-10 w-60 h-60 bg-gradient-to-r from-blue-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        </div>

        {/* Header */}
        <header className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-white/30 dark:border-gray-700/50 relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center py-4 sm:py-6 gap-4">
              <div className="flex items-center gap-4">
                <Link to="/admin/blog">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
                <div>
                  <h1 className="text-xl sm:text-2xl font-montserrat font-bold text-gray-900 dark:text-white">
                    Workflow Builder
                  </h1>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 font-poppins">
                    Create visual automation workflows for your business
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <ThemeToggle />
                <Button
                  className="bg-gradient-to-r from-primary to-accent text-white"
                  onClick={() => {
                    // Check if user can create workflows
                    if (!canUseWorkflows) {
                      checkWorkflowLimit(
                        currentOrganization?.workflow_credits_used || 0,
                        currentOrganization?.workflow_credits_limit || 0
                      )
                      return
                    }
                    navigate('/admin/automations/editor/new')
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Workflow
                </Button>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Rules</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats.activeWorkflows}
                    </p>
                  </div>
                  <Zap className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Executions</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats.totalExecutions}
                    </p>
                  </div>
                  <Activity className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats.successRate}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Workflows</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.totalWorkflows}
                  </p>
                  </div>
                  <Globe className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Credit Usage Banner */}
          <WorkflowCreditBanner />

          {/* Upgrade Prompt */}
          {currentOrganization && (
            <UpgradePrompt
              type="workflow"
              currentUsage={currentOrganization.workflow_credits_used}
              limit={currentOrganization.workflow_credits_limit}
              className="mb-8"
            />
          )}

          {/* Main Content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger value="rules" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Workflows
              </TabsTrigger>
              <TabsTrigger value="templates" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Templates
              </TabsTrigger>
              <TabsTrigger value="logs" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Execution Logs
              </TabsTrigger>
            </TabsList>

            <TabsContent value="rules">
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
                <CardHeader>
                  <CardTitle>Workflows</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                      <p className="mt-2 text-gray-600 dark:text-gray-400">Loading workflows...</p>
                    </div>
                  ) : rules.length === 0 ? (
                    <div className="text-center py-12">
                      <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Workflows Yet</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Create your first visual workflow to automate your business processes.
                      </p>
                      <Button onClick={() => navigate('/admin/automations/editor/new')}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Workflow
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {rules.map((rule) => (
                        <div
                          key={rule.id}
                          className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        >
                          <div className="flex items-center gap-4 flex-1">
                            <div className="flex items-center gap-2">
                              {getTriggerInfo(rule.nodes).icon}
                              <Badge variant="outline">
                                {getTriggerInfo(rule.nodes).label}
                              </Badge>
                            </div>
                            
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {rule.name}
                              </h3>
                              {rule.description && (
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {rule.description}
                                </p>
                              )}
                              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                <span>{rule.nodes.length} components</span>
                                <span>Created: {formatDate(rule.created_at)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Switch
                              checked={rule.active}
                              onCheckedChange={(checked) => handleToggleRule(rule.id, checked)}
                            />
                            {profile?.role === 'owner' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCreateTemplate(rule.id)}
                                title="Create Template"
                              >
                                <Star className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/admin/automations/editor/${rule.id}`)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates">
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Workflow Templates</CardTitle>
                  {profile?.role === 'owner' && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Create templates from existing workflows using the star icon
                    </div>
                  )}
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                      <p className="mt-2 text-gray-600 dark:text-gray-400">Loading templates...</p>
                    </div>
                  ) : templates.length === 0 ? (
                    <div className="text-center py-12">
                      <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Templates Yet</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {profile?.role === 'owner'
                          ? 'Create templates from your existing workflows to help users get started quickly.'
                          : 'Workflow templates will appear here to help you get started quickly.'
                        }
                      </p>
                      {profile?.role === 'owner' && rules.length > 0 && (
                        <p className="text-sm text-blue-600 dark:text-blue-400">
                          Go to the Workflows tab and click the star icon on any workflow to create a template.
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {templates.map((template) => (
                        <div
                          key={template.id}
                          className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        >
                          <div className="flex items-center gap-4 flex-1">
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400">
                                Template
                              </Badge>
                            </div>

                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {template.name}
                              </h3>
                              {template.description && (
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {template.description}
                                </p>
                              )}
                              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                <span>{template.nodes.length} components</span>
                                <span>Created: {formatDate(template.created_at)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUseTemplate(template.id)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Use Template
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/admin/automations/editor/${template.id}?preview=true`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {profile?.role === 'owner' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteTemplate(template.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="logs">
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/30 dark:border-gray-700/50">
                <CardHeader>
                  <CardTitle>Execution Logs</CardTitle>
                </CardHeader>
                <CardContent>
                  {logs.length === 0 ? (
                    <div className="text-center py-12">
                      <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Executions Yet</h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Workflow execution logs will appear here once your workflows start running.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {logs.map((log) => (
                        <div
                          key={log.id}
                          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              {getStatusIcon(log.status)}
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <p className="font-medium text-gray-900 dark:text-white">
                                    {log.workflow_name}
                                  </p>
                                  <Badge
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    {log.trigger_data?.quote ? 'Real Data' : 'Test Run'}
                                  </Badge>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                                  <div className="flex items-center gap-1">
                                    <Activity className="h-3 w-3" />
                                    ID: {log.id.slice(0, 8)}...
                                  </div>
                                  {log.execution_time_ms && (
                                    <div className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {log.execution_time_ms}ms
                                    </div>
                                  )}
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    {formatDate(log.executed_at)}
                                  </div>
                                  {log.trigger_data?.quote?.email && (
                                    <div className="flex items-center gap-1">
                                      <User className="h-3 w-3" />
                                      {log.trigger_data.quote.email}
                                    </div>
                                  )}
                                </div>

                                {log.trigger_data && (
                                  <details className="mt-2">
                                    <summary className="text-xs text-blue-600 dark:text-blue-400 cursor-pointer hover:underline">
                                      View Trigger Data
                                    </summary>
                                    <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-x-auto">
                                      {JSON.stringify(log.trigger_data, null, 2)}
                                    </pre>
                                  </details>
                                )}

                                {log.error_message && log.status === 'failed' && (
                                  <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                                    <p className="text-xs text-red-600 dark:text-red-400 font-medium">
                                      Error: {log.error_message}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>

                            <Badge
                              className={
                                log.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                log.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                              }
                            >
                              {log.status.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>


      </div>
    </ProtectedRoute>
  )
}

export default Automations
