import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Zap,
  Sparkles,
  TrendingUp,
  Calendar,
  CreditCard,
  ArrowUpRight,
  Info
} from 'lucide-react'
import { updateOrganizationSubscription } from '@/lib/supabase'
import { toast } from 'sonner'

const CreditsUsage: React.FC = () => {
  const { profile } = useAuth()
  const {
    currentOrganization,
    workflowCreditsRemaining,
    aiCreditsRemaining,
    isNearWorkflowLimit,
    isNearAiLimit,
    refreshOrganization
  } = useOrganization()

  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [upgrading, setUpgrading] = useState(false)

  if (!currentOrganization) {
    return (
      <ProtectedRoute>
        <AdminLayout title="Credits Usage">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">No Organization Found</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Please set up your workspace first.
              </p>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  const workflowUsagePercent = currentOrganization.workflow_credits_limit > 0 
    ? (currentOrganization.workflow_credits_used / currentOrganization.workflow_credits_limit) * 100
    : 0

  const aiUsagePercent = currentOrganization.ai_credits_limit > 0 
    ? (currentOrganization.ai_credits_used / currentOrganization.ai_credits_limit) * 100
    : 0

  const getUsageColor = (percent: number) => {
    if (percent >= 90) return 'text-red-600 dark:text-red-400'
    if (percent >= 80) return 'text-orange-600 dark:text-orange-400'
    if (percent >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-green-600 dark:text-green-400'
  }

  const getProgressColor = (percent: number) => {
    if (percent >= 90) return 'bg-red-500'
    if (percent >= 80) return 'bg-orange-500'
    if (percent >= 60) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const handleUpgrade = async (plan: string) => {
    if (!currentOrganization) return

    // Check if user is owner/super admin - they can upgrade
    if (profile?.role === 'owner' || profile?.role === 'super_admin') {
      setUpgrading(true)
      try {
        const { error } = await updateOrganizationSubscription(currentOrganization.id, plan)
        if (error) {
          toast.error('Failed to upgrade subscription')
          console.error('Upgrade error:', error)
          return
        }

        toast.success(`Successfully upgraded to ${plan} plan!`)
        await refreshOrganization()
        setShowUpgradeDialog(false)
        setSelectedPlan('')
      } catch (error) {
        toast.error('An unexpected error occurred')
        console.error('Error:', error)
      } finally {
        setUpgrading(false)
      }
    } else {
      // Regular users see coming soon message
      toast.info('Payment Gateway Coming Soon!', {
        description: 'We\'re working on integrating secure payment processing. Stay tuned!'
      })
      setShowUpgradeDialog(false)
      setSelectedPlan('')
    }
  }

  const openUpgradeDialog = (plan: string) => {
    setSelectedPlan(plan)
    setShowUpgradeDialog(true)
  }

  return (
    <ProtectedRoute>
      <AdminLayout
        title="Credits Usage"
        subtitle="Monitor your workflow and AI credit consumption"
      >
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Current Plan Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Current Plan
                  </CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {currentOrganization.name}
                  </p>
                </div>
                <Badge 
                  variant="secondary" 
                  className="capitalize text-sm"
                >
                  {currentOrganization.subscription_plan}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Workflow Credits */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">Workflow Credits</span>
                    </div>
                    {isNearWorkflowLimit && (
                      <Badge variant="destructive" className="text-xs">
                        Low Credits
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span className={`font-mono ${getUsageColor(workflowUsagePercent)}`}>
                        {currentOrganization.workflow_credits_used} / {
                          currentOrganization.workflow_credits_limit === -1 
                            ? '∞' 
                            : currentOrganization.workflow_credits_limit
                        }
                      </span>
                    </div>
                    
                    {currentOrganization.workflow_credits_limit > 0 && (
                      <div className="space-y-1">
                        <Progress 
                          value={workflowUsagePercent} 
                          className="h-2"
                        />
                        <div className="text-xs text-gray-500 text-right">
                          {workflowCreditsRemaining} remaining
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* AI Credits */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-purple-500" />
                      <span className="font-medium">AI Credits</span>
                    </div>
                    {isNearAiLimit && (
                      <Badge variant="destructive" className="text-xs">
                        Low Credits
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span className={`font-mono ${getUsageColor(aiUsagePercent)}`}>
                        {currentOrganization.ai_credits_used} / {
                          currentOrganization.ai_credits_limit === -1 
                            ? '∞' 
                            : currentOrganization.ai_credits_limit
                        }
                      </span>
                    </div>
                    
                    {currentOrganization.ai_credits_limit > 0 && (
                      <div className="space-y-1">
                        <Progress 
                          value={aiUsagePercent} 
                          className="h-2"
                        />
                        <div className="text-xs text-gray-500 text-right">
                          {aiCreditsRemaining} remaining
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Info className="h-4 w-4" />
                  Workflow Credits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>• 1 credit per workflow execution</p>
                  <p>• Includes email sending, webhooks, and automations</p>
                  <p>• Credits reset monthly on your billing cycle</p>
                </div>
                
                {currentOrganization.subscription_plan === 'free' && (
                  <div className="pt-3 border-t">
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => window.location.href = '/pricing'}
                    >
                      <ArrowUpRight className="h-4 w-4 mr-2" />
                      Upgrade for More Credits
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Info className="h-4 w-4" />
                  AI Credits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>• 1 credit per AI request</p>
                  <p>• Includes blog generation, content assistance</p>
                  <p>• Credits reset monthly on your billing cycle</p>
                </div>
                
                {currentOrganization.subscription_plan === 'free' && (
                  <div className="pt-3 border-t">
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => window.location.href = '/pricing'}
                    >
                      <ArrowUpRight className="h-4 w-4 mr-2" />
                      Upgrade for More Credits
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Plan Comparison */}
          {currentOrganization.subscription_plan === 'free' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Need More Credits?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <h4 className="font-semibold">Free</h4>
                    <p className="text-2xl font-bold text-gray-600">$0</p>
                    <div className="text-sm text-gray-500 mt-2">
                      <p>100 workflow credits</p>
                      <p>50 AI credits</p>
                    </div>
                  </div>
                  
                  <div className="text-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20">
                    <h4 className="font-semibold">Basic</h4>
                    <p className="text-2xl font-bold text-blue-600">$5</p>
                    <div className="text-sm text-gray-500 mt-2">
                      <p>1,000 workflow credits</p>
                      <p>500 AI credits</p>
                    </div>
                  </div>
                  
                  <div className="text-center p-4 border rounded-lg bg-purple-50 dark:bg-purple-900/20">
                    <h4 className="font-semibold">Pro</h4>
                    <p className="text-2xl font-bold text-purple-600">$8</p>
                    <div className="text-sm text-gray-500 mt-2">
                      <p>5,000 workflow credits</p>
                      <p>2,000 AI credits</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 text-center space-x-4">
                  <Button onClick={() => window.location.href = '/pricing'}>
                    <ArrowUpRight className="h-4 w-4 mr-2" />
                    Upgrade to Basic
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/pricing'}
                  >
                    <ArrowUpRight className="h-4 w-4 mr-2" />
                    Upgrade to Pro
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Upgrade Confirmation Dialog */}
        <AlertDialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
          <AlertDialogContent className="max-w-md mx-4">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-blue-500" />
                Upgrade Subscription
              </AlertDialogTitle>
              <AlertDialogDescription>
                {selectedPlan === 'basic' && (
                  <>
                    Upgrade to <strong>Basic Plan ($5/month)</strong>?
                    <div className="mt-2 text-sm">
                      <p>• 1,000 workflow credits</p>
                      <p>• 500 AI credits</p>
                      <p>• Credits reset monthly</p>
                    </div>
                  </>
                )}
                {selectedPlan === 'pro' && (
                  <>
                    Upgrade to <strong>Pro Plan ($8/month)</strong>?
                    <div className="mt-2 text-sm">
                      <p>• 5,000 workflow credits</p>
                      <p>• 2,000 AI credits</p>
                      <p>• Credits reset monthly</p>
                      <p>• Priority support</p>
                    </div>
                  </>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={upgrading}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => handleUpgrade(selectedPlan)}
                disabled={upgrading}
              >
                {upgrading ? 'Upgrading...' : 'Confirm Upgrade'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      </AdminLayout>
    </ProtectedRoute>
  )
}

export default CreditsUsage
