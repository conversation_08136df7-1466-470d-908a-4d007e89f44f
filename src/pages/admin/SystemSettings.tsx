import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import BlogMigrationTool from '@/components/BlogMigrationTool'
import {
  Settings,
  Database,
  AlertTriangle,
  Info
} from 'lucide-react'

const SystemSettings = () => {
  const { profile } = useAuth()
  const { currentOrganization } = useOrganization()

  return (
    <ProtectedRoute requiredRole="saas_owner">
      <AdminLayout
        title="System Settings"
        subtitle="Advanced system administration and troubleshooting tools"
      >
        <div className="max-w-6xl mx-auto space-y-8">
          
          {/* System Information */}
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                System Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Current User</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Role: <span className="font-mono">{profile?.role}</span>
                    {profile?.role === 'viewer' && (
                      <span className="ml-2 text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded">
                        Needs Upgrade
                      </span>
                    )}
                  </p>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Email: <span className="font-mono">{profile?.email}</span>
                  </p>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    ID: <span className="font-mono">{profile?.id}</span>
                  </p>
                  {profile?.role === 'viewer' && (
                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-2">
                      ⚠️ Run the SQL script to enable workflows and email integrations
                    </p>
                  )}
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">Current Organization</h3>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Name: <span className="font-mono">{currentOrganization?.name || 'None'}</span>
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    ID: <span className="font-mono">{currentOrganization?.id || 'None'}</span>
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Plan: <span className="font-mono">{currentOrganization?.subscription_plan || 'None'}</span>
                  </p>
                </div>
              </div>
              
              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <h3 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Query Scope</h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Blog Query Organization ID: <span className="font-mono">
                    {(profile?.role === 'saas_owner' || profile?.role === 'super_admin') 
                      ? 'undefined (all organizations)' 
                      : currentOrganization?.id || 'None'}
                  </span>
                </p>
                <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                  SaaS owners see all content across all organizations
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Troubleshooting Tools */}
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Troubleshooting Tools
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              
              {/* Warning Notice */}
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-red-900 dark:text-red-100 mb-1">
                      Caution: Administrative Tools
                    </h3>
                    <p className="text-sm text-red-700 dark:text-red-300">
                      These tools perform system-wide operations that affect all users and organizations. 
                      Use with extreme caution and only when necessary. Always backup data before running migrations.
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Role Fix */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                  <Settings className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                    Quick Role Fix
                  </h3>
                </div>

                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                    Enable Workflows & Email Integrations for Free Users
                  </h4>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-3">
                    If users can't see Workflows and Email Integrations in the navigation, run the SQL script:
                  </p>
                  <code className="block text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                    UPDATE profiles SET role = 'user' WHERE role = 'viewer' AND email != '<EMAIL>';
                  </code>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-2">
                    This upgrades 'viewer' users to 'user' role, giving them access to workflows and email integrations with limited credits.
                  </p>
                </div>
              </div>

              {/* Blog Migration Tool */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                  <Database className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                    Blog Migration Tool
                  </h3>
                </div>

                <BlogMigrationTool />
              </div>

            </CardContent>
          </Card>

        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default SystemSettings
