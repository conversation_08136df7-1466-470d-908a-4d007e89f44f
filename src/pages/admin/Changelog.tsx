import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import FeatureRequests from '@/components/FeatureRequests'
import {
  FileText,
  Calendar,
  Plus,
  Wrench,
  Bug,
  Zap,
  GitCommit
} from 'lucide-react'

const Changelog = () => {
  const { profile } = useAuth()
  const [changelogContent, setChangelogContent] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchChangelog()
  }, [])

  const fetchChangelog = async () => {
    try {
      // Fetch the changelog content from the public folder or API
      const response = await fetch('/CHANGELOG.md')
      if (response.ok) {
        const content = await response.text()
        setChangelogContent(content)
      } else {
        // Fallback content if file is not accessible
        setChangelogContent(`# Changelog

All notable changes to the Millennial Business Innovations website will be documented in this file.

## [1.9.0] - 2024-12-19

### Added
- **Workflow Templates System**
  - Owner-exclusive template creation from existing workflows using star icon
  - Dedicated Templates tab in automation dashboard for template management
  - One-click workflow creation from templates for rapid deployment
  - Template library with preview and usage tracking
  - Template deletion with confirmation for owners

- **Enhanced Execution Logs**
  - Real vs test data distinction with visual badges
  - Detailed execution information including duration in milliseconds
  - User email context extraction from trigger data
  - Expandable JSON trigger data viewer for debugging
  - Color-coded status indicators (success/failed/pending)
  - Comprehensive error messages for failed executions
  - Timestamp display with proper formatting

- **Database Schema Enhancements**
  - Added \`is_template\` column to workflows table for template identification
  - Created email integrations table for workflow email actions
  - Created email templates table for reusable email content
  - Proper indexing and RLS policies for new template features
  - Template exclusion from regular workflow statistics

### Changed
- **Workflow Management**
  - Templates are now excluded from active workflow counts
  - Enhanced workflow service with template-specific methods
  - Improved workflow statistics to focus on active automations only

### Technical Improvements
- **Template Architecture**: Robust template creation and management system
- **Execution Tracking**: Enhanced logging with real data capture and analysis
- **User Experience**: Intuitive template creation workflow for owners
- **Data Security**: Proper RLS policies for template and email integration access
- **Performance**: Optimized queries excluding templates from regular workflow operations

## [1.8.0] - 2024-12-19

### Added
- **Visual Workflow Builder**
  - Complete full-screen workflow editor with drag-and-drop interface
  - Visual node-based automation builder with triggers, conditions, and actions
  - SVG connection lines showing data flow between workflow nodes
  - Input dot highlighting system (blue when connected, gray when empty)
  - Real-time visual feedback during connection creation (green hover states)

- **Enhanced Node System**
  - Trigger nodes for webhook, form submission, and time-based events
  - Condition nodes with field comparison and logical operators
  - Action nodes supporting webhook calls and email notifications
  - Properties panel for detailed node configuration
  - Node validation and connection rules enforcement

- **Workflow Management**
  - Workflow execution logging with performance tracking
  - Database schema for storing workflow definitions and execution history
  - Admin interface integration with existing automation dashboard
  - Connection validation preventing invalid workflow configurations

### Changed
- **Automation Interface**
  - Enhanced automation dashboard with visual workflow builder access
  - Improved user experience with full-screen editor interface
  - Better visual representation of automation logic flow

### Technical Improvements
- **Visual Design**: Professional workflow interface with glassmorphism styling
- **Performance**: Optimized SVG rendering for smooth connection animations
- **User Experience**: Intuitive drag-and-drop with visual feedback systems
- **Data Flow**: Clear visual indicators showing workflow execution paths

## [1.7.0] - 2024-12-19

### Added
- **Enterprise Automation System**
  - Complete visual automation builder with 4-step wizard interface
  - Trigger configuration for new quotes, status changes, and time-based events
  - Advanced condition builder for lead filtering and targeting
  - Action configuration with webhook and template support
  - Real-time execution monitoring and comprehensive logging

- **Webhook Infrastructure & GHL Integration**
  - Supabase Edge Function for robust webhook processing
  - GHL-friendly payload formatting for seamless CRM integration
  - Liquid template system for dynamic content generation
  - Bi-directional webhook support (send and receive webhooks)
  - Enterprise-level error handling with automatic retry logic

- **Automation Database Schema**
  - Complete automation rules and templates storage
  - Execution logging with performance tracking
  - Template library with pre-built integrations (Slack, GHL, custom)
  - Queue system for asynchronous webhook processing
  - Comprehensive audit trail for all automation activities

- **Admin Dashboard Enhancement**
  - Glassmorphism automation dashboard at \`/admin/automations\`
  - Real-time stats overview with execution metrics
  - Template library browser with syntax highlighting
  - Live execution logs with status monitoring
  - Integrated navigation from blog dashboard

### Changed
- **Lead Management Workflow**
  - Enhanced quote processing with automated webhook triggers
  - Structured contact data formatting for external CRM systems
  - Custom field mapping and intelligent tag generation
  - Lead scoring and qualification automation capabilities

### Technical Improvements
- **Webhook Processing**: Production-tested Edge Function at \`/functions/v1/workflow-function\`
- **Template Engine**: Liquid templating for dynamic webhook payloads
- **Security**: Role-based access control for automation management
- **Performance**: Optimized database queries with proper indexing
- **Monitoring**: Complete execution tracking and error reporting

## [1.6.0] - 2024-12-19

### Added
- **Quote Request System with Onboarding Flow**
  - Created comprehensive 4-step quote request modal with progressive data collection
  - Added project discovery, requirements gathering, timeline & budget, and contact information steps
  - Implemented optional blog account creation during quote process with Google OAuth support
  - Added progress indicator and step validation for improved user experience

- **Admin Quote Management Dashboard**
  - Built complete admin interface at \`/admin/quotes\` for viewing and managing quote requests
  - Added status tracking system (new → reviewed → quoted → converted → declined)
  - Implemented filtering by status and comprehensive lead details display
  - Added click-to-call and click-to-email functionality for immediate follow-up
  - Integrated account creation indicators for lead nurturing opportunities

- **Database Integration & Security**
  - Created \`quote_requests\` table with comprehensive schema for project data storage
  - Implemented Row Level Security (RLS) policies for secure data access
  - Added automatic timestamp tracking and status management
  - Integrated with existing Supabase authentication system

### Changed
- **Pricing Section Enhancement**
  - Replaced simple "Request Custom Quote" email link with comprehensive onboarding flow
  - Added quote requests access button to blog dashboard for admin users
  - Improved lead qualification process with detailed project requirements collection

### Technical Improvements
- **Lead Management**: Enhanced lead capture with structured data collection and admin tools
- **User Experience**: Non-intrusive account creation that doesn't block quote submission
- **Data Security**: Comprehensive RLS policies ensuring proper data access controls
- **Integration**: Seamless integration with existing blog authentication and user management

## [1.5.0] - 2024-12-19

### Added
- **Blog Editor with Media Upload**
  - Created comprehensive BlogEditor component with rich form interface
  - Added auto-slug generation from title for SEO-friendly URLs
  - Implemented draft/publish workflow with proper status management
  - Added media upload functionality supporting images, videos, and GIFs (up to 10MB)

- **Database Integration**
  - Added \`getBlogPostById\` function for editing existing posts by ID
  - Implemented \`createOrUpdateProfile\` function for user profile management
  - Added proper JSONB content format conversion for database compatibility

### Changed
- **Content Management**
  - Enhanced content field to support both string and JSON formats
  - Improved content extraction and conversion for editing existing posts
  - Added markdown support for rich text formatting

### Technical Improvements
- **Data Validation**: Enhanced post data structure with proper field validation
- **Error Handling**: Added comprehensive error logging and user feedback
- **File Management**: Integrated Supabase storage for media uploads
- **Route Management**: Added protected routes for blog editor access

## [1.4.0] - 2024-12-19

### Added
- **Legal Pages**
  - Created comprehensive Privacy Policy page with GDPR/CCPA compliance
  - Added Terms and Conditions page with service agreements
  - Integrated legal page links in footer navigation

### Changed
- **Services Section UX Improvement**
  - Replaced multiple "Learn More" buttons with single "Book a Free Consultation" CTA
  - Streamlined user journey to reduce decision fatigue
  - Added compelling call-to-action section at bottom of services

### Fixed
- **Next.js Icon Display**
  - Fixed Next.js icon showing as white circle instead of proper logo
  - Updated to use Simple Icons CDN for reliable icon rendering

## [1.3.0] - 2024-12-19

### Changed
- **Default Theme to Light Mode**
  - Changed default theme from "system" to "light" for consistent user experience
  - Users can still toggle between light/dark/system themes using the theme toggle

### Fixed
- **Calendar Integration in Pricing Section**
  - Fixed "Schedule a Consultation" button to properly open calendar modal
  - Added consistent calendar popup behavior across all booking buttons

### Improved
- **Professional Tech Stack Icons**
  - Replaced emoji icons with high-quality SVG icons from DevIcons CDN
  - Added official brand colors for each technology
  - Enhanced visual consistency and professional appearance`)
      }
    } catch (error) {
      console.error('Error fetching changelog:', error)
      setChangelogContent('# Changelog\n\nUnable to load changelog content.')
    } finally {
      setLoading(false)
    }
  }

  const parseChangelog = (content: string) => {
    const lines = content.split('\n')
    const sections: any[] = []
    let currentSection: any = null
    let currentSubsection: any = null

    lines.forEach((line, index) => {
      // Version headers (## [1.5.0] - 2024-12-19)
      if (line.match(/^## \[[\d.]+\] - \d{4}-\d{2}-\d{2}/)) {
        if (currentSection) {
          sections.push(currentSection)
        }
        const versionMatch = line.match(/\[([^\]]+)\]/)
        const dateMatch = line.match(/(\d{4}-\d{2}-\d{2})/)
        currentSection = {
          version: versionMatch ? versionMatch[1] : 'Unknown',
          date: dateMatch ? dateMatch[1] : 'Unknown',
          subsections: []
        }
        currentSubsection = null
      }
      // Subsection headers (### Added, ### Changed, etc.)
      else if (line.match(/^### /)) {
        const title = line.replace('### ', '')
        currentSubsection = {
          title,
          items: []
        }
        if (currentSection) {
          currentSection.subsections.push(currentSubsection)
        }
      }
      // Items (- **Feature**: Description)
      else if (line.match(/^- \*\*/) && currentSubsection) {
        const itemMatch = line.match(/^- \*\*([^*]+)\*\*(.*)/)
        if (itemMatch) {
          currentSubsection.items.push({
            title: itemMatch[1],
            description: itemMatch[2].replace(/^:\s*/, '')
          })
        }
      }
      // Regular items (- Description)
      else if (line.match(/^- /) && currentSubsection) {
        currentSubsection.items.push({
          title: '',
          description: line.replace(/^- /, '')
        })
      }
    })

    if (currentSection) {
      sections.push(currentSection)
    }

    return sections
  }

  const getSubsectionIcon = (title: string) => {
    switch (title.toLowerCase()) {
      case 'added':
        return <Plus className="h-4 w-4 text-green-600" />
      case 'changed':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'fixed':
        return <Bug className="h-4 w-4 text-red-600" />
      case 'improved':
        return <Zap className="h-4 w-4 text-yellow-600" />
      default:
        return <GitCommit className="h-4 w-4 text-gray-600" />
    }
  }

  const getSubsectionColor = (title: string) => {
    switch (title.toLowerCase()) {
      case 'added':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'changed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'fixed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'improved':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const sections = parseChangelog(changelogContent)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <FileText className="h-8 w-8 animate-pulse mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400 font-poppins">
            Loading changelog...
          </p>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute requiredRole="user">
      <AdminLayout
        title="Changelog & Feedback"
        subtitle="Track updates and share your feature ideas"
      >
        <div className="max-w-6xl mx-auto">
        <Tabs defaultValue="changelog" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="changelog" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Changelog
              </TabsTrigger>
              <TabsTrigger value="feedback" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Feature Requests
              </TabsTrigger>
            </TabsList>

            <TabsContent value="changelog">
              <div className="space-y-8">
                {sections.map((section, sectionIndex) => (
              <Card key={sectionIndex} className="overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-primary/10 to-accent/10 border-b">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-3">
                      <div className="bg-primary text-white rounded-full p-2">
                        <GitCommit className="h-5 w-5" />
                      </div>
                      <div>
                        <span className="text-xl font-montserrat">Version {section.version}</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600 dark:text-gray-400 font-poppins">
                            {new Date(section.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                      </div>
                    </CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="p-6">
                  <div className="space-y-6">
                    {section.subsections.map((subsection: any, subsectionIndex: number) => (
                      <div key={subsectionIndex}>
                        <div className="flex items-center gap-2 mb-3">
                          <Badge className={getSubsectionColor(subsection.title)}>
                            {getSubsectionIcon(subsection.title)}
                            <span className="ml-1">{subsection.title}</span>
                          </Badge>
                        </div>
                        
                        <div className="space-y-3 ml-6">
                          {subsection.items.map((item: any, itemIndex: number) => (
                            <div key={itemIndex} className="flex gap-3">
                              <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                              <div>
                                {item.title && (
                                  <span className="font-semibold text-gray-900 dark:text-white">
                                    {item.title}
                                  </span>
                                )}
                                {item.title && item.description && ': '}
                                <span className="text-gray-700 dark:text-gray-300">
                                  {item.description}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="feedback">
              <FeatureRequests />
            </TabsContent>
        </Tabs>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default Changelog
