import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  getAllUsers,
  deleteUser,
  updateUserRole,
  resetUserPassword,
  Profile,
  getBlogPostStatsByUser,
  updateUserSubscription,
  updateOrganizationSubscription,
  addCreditsToOrganization,
  resetOrganizationCredits,
  getUserOrganizations
} from '@/lib/supabase'
import {
  Users,
  Trash2,
  Key,
  Shield,
  Crown,
  UserCheck,
  Edit,
  AlertTriangle,
  FileText,
  Eye,
  CreditCard,
  Plus,
  RefreshCw,
  DollarSign
} from 'lucide-react'
import { toast } from 'sonner'

const UserManagement = () => {
  const { user, profile } = useAuth()
  const [users, setUsers] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [blogStats, setBlogStats] = useState<any[]>([])
  const [statsLoading, setStatsLoading] = useState(true)
  const [userOrganizations, setUserOrganizations] = useState<{[key: string]: any[]}>({})
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false)
  const [showCreditsDialog, setShowCreditsDialog] = useState(false)
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null)
  const [selectedOrganization, setSelectedOrganization] = useState<any>(null)

  useEffect(() => {
    fetchUsers()
    fetchBlogStats()
  }, [])

  // Fetch user organizations for subscription management
  const fetchUserOrganizations = async (userId: string) => {
    if (userOrganizations[userId]) return userOrganizations[userId]

    try {
      const { data, error } = await getUserOrganizations(userId)
      if (error) {
        console.error('Error fetching user organizations:', error)
        return []
      }

      const orgs = data?.map(item => item.organization) || []
      setUserOrganizations(prev => ({ ...prev, [userId]: orgs }))
      return orgs
    } catch (error) {
      console.error('Error:', error)
      return []
    }
  }

  const fetchUsers = async () => {
    try {
      const { data, error } = await getAllUsers()
      if (error) {
        toast.error('Failed to load users')
        console.error('Error fetching users:', error)
      } else {
        setUsers(data || [])
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchBlogStats = async () => {
    try {
      const { data, error } = await getBlogPostStatsByUser()
      if (error) {
        console.error('Error fetching blog stats:', error)
      } else {
        setBlogStats(data || [])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  // Get blog stats for a specific user
  const getUserBlogStats = (userId: string) => {
    return blogStats.find(stat => stat.author?.id === userId) || {
      total: 0,
      published: 0,
      drafts: 0,
      archived: 0
    }
  }

  const handleDeleteUser = async (userId: string, userName: string) => {
    if (userId === user?.id) {
      toast.error('You cannot delete your own account')
      return
    }

    try {
      const { error } = await deleteUser(userId)
      if (error) {
        toast.error('Failed to delete user')
        console.error('Delete error:', error)
        return
      }

      toast.success(`User ${userName} deleted successfully`)
      fetchUsers() // Refresh the list
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleRoleChange = async (userId: string, newRole: Profile['role'], userName: string) => {
    if (userId === user?.id && newRole !== profile?.role) {
      toast.error('You cannot change your own role')
      return
    }

    // SaaS Owner role restrictions
    if (newRole === 'saas_owner' && profile?.role !== 'saas_owner') {
      toast.error('Only the SaaS owner can assign SaaS owner role')
      return
    }

    // Prevent non-SaaS owners from creating super admins
    if (profile?.role !== 'saas_owner' && newRole === 'super_admin') {
      toast.error('Only the SaaS owner can assign super admin role')
      return
    }

    // Prevent non-owners from creating owners (workspace owners)
    if (profile?.role !== 'owner' && profile?.role !== 'saas_owner' && newRole === 'owner') {
      toast.error('Only workspace owners and SaaS owner can assign owner role')
      return
    }

    // Prevent non-super admins from creating admins
    if (profile?.role !== 'super_admin' && profile?.role !== 'owner' && profile?.role !== 'saas_owner' && newRole === 'admin') {
      toast.error('Only super admins, owners, and SaaS owner can assign admin role')
      return
    }

    try {
      const { error } = await updateUserRole(userId, newRole)
      if (error) {
        toast.error('Failed to update user role')
        console.error('Role update error:', error)
        return
      }

      toast.success(`${userName}'s role updated to ${newRole.replace('_', ' ')}`)
      fetchUsers() // Refresh the list
      fetchBlogStats() // Refresh blog stats
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleResetPassword = async (email: string, userName: string) => {
    try {
      const { error } = await resetUserPassword(email)
      if (error) {
        toast.error('Failed to send reset email')
        console.error('Reset error:', error)
        return
      }

      toast.success(`Password reset email sent to ${userName}`)
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleSubscriptionUpdate = async (userId: string, subscriptionPlan: string) => {
    try {
      // Update user subscription
      const { error: userError } = await updateUserSubscription(userId, subscriptionPlan)
      if (userError) {
        toast.error('Failed to update user subscription')
        console.error('User subscription update error:', userError)
        return
      }

      // Update all user's organizations
      const orgs = await fetchUserOrganizations(userId)
      for (const org of orgs) {
        const { error: orgError } = await updateOrganizationSubscription(org.id, subscriptionPlan)
        if (orgError) {
          console.error('Organization subscription update error:', orgError)
        }
      }

      toast.success('Subscription updated successfully')
      fetchUsers()
      setShowSubscriptionDialog(false)
      setSelectedUser(null)
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleCreditsUpdate = async (orgId: string, workflowCredits: number, aiCredits: number, action: 'add' | 'reset') => {
    try {
      if (action === 'reset') {
        const { error } = await resetOrganizationCredits(orgId)
        if (error) {
          toast.error('Failed to reset credits')
          console.error('Credits reset error:', error)
          return
        }
        toast.success('Credits reset successfully')
      } else {
        const { error } = await addCreditsToOrganization(orgId, workflowCredits, aiCredits)
        if (error) {
          toast.error('Failed to add credits')
          console.error('Credits add error:', error)
          return
        }
        toast.success('Credits added successfully')
      }

      setShowCreditsDialog(false)
      setSelectedOrganization(null)
      // Refresh user organizations
      if (selectedUser) {
        setUserOrganizations(prev => ({ ...prev, [selectedUser.id]: [] }))
        await fetchUserOrganizations(selectedUser.id)
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'saas_owner':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0'
      case 'super_admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'owner':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
      case 'admin':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'editor':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'viewer':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
      case 'user':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'saas_owner':
        return <Crown className="h-4 w-4 text-purple-600" />
      case 'super_admin':
        return <Crown className="h-4 w-4 text-blue-600" />
      case 'admin':
        return <Shield className="h-4 w-4" />
      case 'editor':
        return <UserCheck className="h-4 w-4 text-green-600" />
      case 'viewer':
        return <UserCheck className="h-4 w-4" />
      default:
        return <UserCheck className="h-4 w-4" />
    }
  }

  const canDeleteUser = (targetUser: Profile) => {
    // SaaS Owner can delete anyone except themselves
    if (profile?.role === 'saas_owner') {
      return targetUser.id !== user?.id
    }
    // Super admin can delete anyone except themselves, SaaS owner, and other super admins
    if (profile?.role === 'super_admin') {
      return targetUser.id !== user?.id &&
             targetUser.role !== 'saas_owner' &&
             targetUser.role !== 'super_admin'
    }
    // Workspace owners can delete users in their workspace (except SaaS owner and super admins)
    if (profile?.role === 'owner') {
      return targetUser.id !== user?.id &&
             targetUser.role !== 'saas_owner' &&
             targetUser.role !== 'super_admin' &&
             targetUser.role !== 'owner'
    }
    // Regular admins cannot delete users
    return false
  }

  const canChangeRole = (targetUser: Profile) => {
    // SaaS Owner can change anyone's role except their own
    if (profile?.role === 'saas_owner') {
      return targetUser.id !== user?.id
    }
    // Super admin can change anyone's role except their own, SaaS owner, and other super admins
    if (profile?.role === 'super_admin') {
      return targetUser.id !== user?.id &&
             targetUser.role !== 'saas_owner' &&
             targetUser.role !== 'super_admin'
    }
    // Workspace owner can change roles within their workspace (except SaaS owner and super admins)
    if (profile?.role === 'owner') {
      return targetUser.id !== user?.id &&
             targetUser.role !== 'saas_owner' &&
             targetUser.role !== 'super_admin'
    }
    // Regular admins cannot change roles
    return false
  }

  const getAvailableRoles = (targetUser: Profile): Profile['role'][] => {
    if (profile?.role === 'saas_owner') {
      // SaaS Owner can assign any role (removed viewer - everyone should be user or higher)
      return ['saas_owner', 'super_admin', 'owner', 'admin', 'editor', 'user']
    }
    if (profile?.role === 'super_admin') {
      // Super admin can assign roles below super admin
      return ['owner', 'admin', 'editor', 'user']
    }
    if (profile?.role === 'owner') {
      // Workspace owner can assign roles below owner
      return ['admin', 'editor', 'user']
    }
    // Regular users cannot assign roles
    return []
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Users className="h-8 w-8 animate-pulse mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400 font-poppins">
            Loading users...
          </p>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'super_admin', 'owner', 'saas_owner']}>
      <AdminLayout
        title="User Management"
        subtitle="Manage user accounts and permissions"
      >
        <div className="max-w-7xl mx-auto">
        {/* Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 sm:gap-6 mb-8">
            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{users.length}</p>
                  </div>
                  <Users className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            {profile?.role === 'saas_owner' && (
              <Card className="border-2 border-purple-200 dark:border-purple-800">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-purple-600 dark:text-purple-400">SaaS Owner</p>
                      <p className="text-xl sm:text-2xl font-bold text-purple-900 dark:text-purple-100">
                        {users.filter(u => u.role === 'saas_owner').length}
                      </p>
                    </div>
                    <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Workspace Owners</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                      {users.filter(u => u.role === 'owner').length}
                    </p>
                  </div>
                  <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Super Admins</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                      {users.filter(u => u.role === 'super_admin').length}
                    </p>
                  </div>
                  <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Admins</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                      {users.filter(u => u.role === 'admin').length}
                    </p>
                  </div>
                  <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Regular Users</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                      {users.filter(u => ['user', 'editor', 'viewer'].includes(u.role)).length}
                    </p>
                  </div>
                  <UserCheck className="h-6 w-6 sm:h-8 sm:w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>

        {/* Users List */}
        <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                All Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((targetUser) => {
                  const userStats = getUserBlogStats(targetUser.id)

                  return (
                    <div
                      key={targetUser.id}
                      className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg gap-4"
                    >
                      <div className="flex items-center gap-4 flex-1 min-w-0">
                        <Avatar className="flex-shrink-0">
                          <AvatarImage src={targetUser.avatar_url} alt={targetUser.full_name} />
                          <AvatarFallback>
                            {targetUser.full_name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-1">
                            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                              {targetUser.full_name || 'Unnamed User'}
                            </h3>
                            {targetUser.id === user?.id && (
                              <Badge variant="outline" className="text-xs w-fit">You</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1 truncate">{targetUser.email}</p>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-gray-500 dark:text-gray-500">
                            <span>Joined {new Date(targetUser.created_at).toLocaleDateString()}</span>
                            <span className="hidden sm:inline">·</span>
                            <div className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              <span>{userStats.total} posts</span>
                              {userStats.published > 0 && (
                                <span className="text-green-600 dark:text-green-400">
                                  ({userStats.published} published)
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 lg:gap-4">
                      {/* Role Badge */}
                      <div className="flex justify-start sm:justify-center">
                        <Badge className={getRoleColor(targetUser.role)}>
                          {getRoleIcon(targetUser.role)}
                          <span className="ml-1 capitalize">{targetUser.role.replace('_', ' ')}</span>
                        </Badge>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap items-center gap-2">
                        {userStats.total > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs flex-shrink-0"
                            onClick={() => window.open(`/admin/blog?author=${targetUser.id}`, '_blank')}
                          >
                            <Eye className="h-4 w-4 sm:mr-1" />
                            <span className="hidden sm:inline">View Posts</span>
                          </Button>
                        )}

                        {canChangeRole(targetUser) && (
                          <Select
                            value={targetUser.role}
                            onValueChange={(newRole: Profile['role']) =>
                              handleRoleChange(targetUser.id, newRole, targetUser.full_name || targetUser.email)
                            }
                          >
                            <SelectTrigger className="w-28 sm:w-36 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {getAvailableRoles(targetUser).map((role) => (
                                <SelectItem key={role} value={role}>
                                  <div className="flex items-center gap-2">
                                    {getRoleIcon(role)}
                                    <span className="capitalize">{role.replace('_', ' ')}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}

                        {/* Platform Owner Controls */}
                        {(profile?.role === 'owner' || profile?.role === 'platform_owner') && (
                          <>
                            <Button
                              onClick={() => {
                                setSelectedUser(targetUser)
                                setShowSubscriptionDialog(true)
                              }}
                              variant="outline"
                              size="sm"
                              className="flex-shrink-0"
                              title="Manage Subscription"
                            >
                              <CreditCard className="h-4 w-4" />
                            </Button>

                            <Button
                              onClick={async () => {
                                setSelectedUser(targetUser)
                                const orgs = await fetchUserOrganizations(targetUser.id)
                                if (orgs.length > 0) {
                                  setSelectedOrganization(orgs[0]) // Default to first org
                                  setShowCreditsDialog(true)
                                } else {
                                  toast.error('No organizations found for this user')
                                }
                              }}
                              variant="outline"
                              size="sm"
                              className="flex-shrink-0"
                              title="Manage Credits"
                            >
                              <DollarSign className="h-4 w-4" />
                            </Button>
                          </>
                        )}

                        <Button
                          onClick={() => handleResetPassword(targetUser.email, targetUser.full_name || targetUser.email)}
                          variant="outline"
                          size="sm"
                          className="flex-shrink-0"
                          title="Reset Password"
                        >
                          <Key className="h-4 w-4" />
                        </Button>

                        {canDeleteUser(targetUser) && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700 flex-shrink-0"
                                title="Delete User"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="max-w-md mx-4">
                              <AlertDialogHeader>
                                <AlertDialogTitle className="flex items-center gap-2">
                                  <AlertTriangle className="h-5 w-5 text-red-500" />
                                  Delete User Account
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete <strong>{targetUser.full_name || targetUser.email}</strong>?
                                  This action cannot be undone and will permanently remove their account and all associated data.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter className="flex-col sm:flex-row gap-2">
                                <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteUser(targetUser.id, targetUser.full_name || targetUser.email)}
                                  className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
                                >
                                  Delete User
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </div>
                  </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subscription Management Dialog */}
        <AlertDialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
          <AlertDialogContent className="max-w-md mx-4">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-blue-500" />
                Manage Subscription
              </AlertDialogTitle>
              <AlertDialogDescription>
                Update subscription plan for <strong>{selectedUser?.full_name || selectedUser?.email}</strong>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4">
              <Select
                defaultValue={selectedUser?.subscription_plan || 'free'}
                onValueChange={(plan) => {
                  if (selectedUser) {
                    handleSubscriptionUpdate(selectedUser.id, plan)
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subscription plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">Free Plan</SelectItem>
                  <SelectItem value="basic">Basic Plan ($10/month)</SelectItem>
                  <SelectItem value="pro">Pro Plan ($15/month)</SelectItem>
                  <SelectItem value="enterprise">Enterprise Plan</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Credits Management Dialog */}
        <AlertDialog open={showCreditsDialog} onOpenChange={setShowCreditsDialog}>
          <AlertDialogContent className="max-w-md mx-4">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                Manage Credits
              </AlertDialogTitle>
              <AlertDialogDescription>
                Manage credits for <strong>{selectedUser?.full_name || selectedUser?.email}</strong>
                {selectedOrganization && (
                  <div className="mt-2 text-sm">
                    <p>Organization: {selectedOrganization.name}</p>
                    <p>Workflow Credits: {selectedOrganization.workflow_credits_used}/{selectedOrganization.workflow_credits_limit}</p>
                    <p>AI Credits: {selectedOrganization.ai_credits_used}/{selectedOrganization.ai_credits_limit}</p>
                  </div>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Workflow Credits</label>
                  <Input
                    type="number"
                    placeholder="0"
                    id="workflowCredits"
                    min="0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">AI Credits</label>
                  <Input
                    type="number"
                    placeholder="0"
                    id="aiCredits"
                    min="0"
                  />
                </div>
              </div>
            </div>
            <AlertDialogFooter className="flex-col sm:flex-row gap-2">
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <Button
                onClick={() => {
                  if (selectedOrganization) {
                    handleCreditsUpdate(selectedOrganization.id, 0, 0, 'reset')
                  }
                }}
                variant="outline"
                className="w-full sm:w-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset Credits
              </Button>
              <Button
                onClick={() => {
                  if (selectedOrganization) {
                    const workflowCredits = parseInt((document.getElementById('workflowCredits') as HTMLInputElement)?.value || '0')
                    const aiCredits = parseInt((document.getElementById('aiCredits') as HTMLInputElement)?.value || '0')
                    handleCreditsUpdate(selectedOrganization.id, workflowCredits, aiCredits, 'add')
                  }
                }}
                className="w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Credits
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      </AdminLayout>
    </ProtectedRoute>
  )
}

export default UserManagement
