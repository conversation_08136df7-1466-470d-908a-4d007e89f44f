import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  Play, 
  Zap, 
  Mail, 
  CheckCircle, 
  XCircle, 
  Clock,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { WorkflowService, type Workflow, type WorkflowExecution } from '@/services/workflowService';

const WorkflowTesting = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testName, setTestName] = useState('Test User');
  const [customTriggerData, setCustomTriggerData] = useState('{}');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [workflowsData, executionsData] = await Promise.all([
        WorkflowService.getWorkflows(),
        WorkflowService.getWorkflowExecutions()
      ]);
      
      setWorkflows(workflowsData);
      setExecutions(executionsData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load workflow data');
    } finally {
      setLoading(false);
    }
  };

  const testQuoteTrigger = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/trigger-workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          trigger_type: 'new_quote',
          trigger_data: {
            quote: {
              id: 'test-quote-' + Date.now(),
              name: testName,
              email: testEmail,
              company: 'Test Company',
              project_type: 'Web Application',
              budget: '$50,000 - $100,000',
              industry: 'Technology',
              timeline: '3-6 months',
              created_at: new Date().toISOString()
            },
            contact: {
              id: 'test-contact-' + Date.now(),
              name: testName,
              email: testEmail,
              company: 'Test Company',
              phone: '+****************'
            },
            event: 'quote.created',
            timestamp: new Date().toISOString()
          }
        })
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Triggered ${result.triggered_count} quote workflows`);
        loadData(); // Refresh executions
      } else {
        toast.error(result.error || 'Failed to trigger workflows');
      }
    } catch (error) {
      console.error('Error testing quote trigger:', error);
      toast.error('Failed to test quote trigger');
    }
  };

  const testNewsletterTrigger = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/trigger-workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          trigger_type: 'newsletter_signup',
          trigger_data: {
            email: testEmail,
            name: testName,
            source: 'test_trigger',
            subscriber_id: 'test-subscriber-' + Date.now(),
            subscribed_at: new Date().toISOString(),
            metadata: { test: true },
            event: 'newsletter.subscribed',
            timestamp: new Date().toISOString()
          }
        })
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Triggered ${result.triggered_count} newsletter workflows`);
        loadData(); // Refresh executions
      } else {
        toast.error(result.error || 'Failed to trigger workflows');
      }
    } catch (error) {
      console.error('Error testing newsletter trigger:', error);
      toast.error('Failed to test newsletter trigger');
    }
  };

  const testCustomTrigger = async (triggerType: string) => {
    try {
      let triggerData;
      try {
        triggerData = JSON.parse(customTriggerData);
      } catch (parseError) {
        toast.error('Invalid JSON in trigger data');
        return;
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/trigger-workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          trigger_type: triggerType,
          trigger_data: {
            ...triggerData,
            test: true,
            timestamp: new Date().toISOString()
          }
        })
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Triggered ${result.triggered_count} workflows for ${triggerType}`);
        loadData(); // Refresh executions
      } else {
        toast.error(result.error || 'Failed to trigger workflows');
      }
    } catch (error) {
      console.error('Error testing custom trigger:', error);
      toast.error('Failed to test custom trigger');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTriggerIcon = (triggerType: string) => {
    switch (triggerType) {
      case 'new_quote': return <Zap className="w-4 h-4 text-blue-500" />;
      case 'newsletter_signup': return <Mail className="w-4 h-4 text-green-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTriggerType = (workflow: Workflow) => {
    const triggerNode = workflow.nodes.find(node => node.type === 'trigger');
    return triggerNode?.data?.triggerType || 'unknown';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Workflow Testing</h1>
          <p className="text-gray-600 dark:text-gray-400">Test and debug workflow triggers</p>
        </div>
        <Button onClick={loadData} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Test Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Triggers</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Test email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
              />
              <Input
                placeholder="Test name"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Button onClick={testQuoteTrigger} className="w-full">
                <Zap className="w-4 h-4 mr-2" />
                Test Quote Trigger
              </Button>
              <Button onClick={testNewsletterTrigger} className="w-full">
                <Mail className="w-4 h-4 mr-2" />
                Test Newsletter Trigger
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Custom Trigger Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder='{"key": "value"}'
              value={customTriggerData}
              onChange={(e) => setCustomTriggerData(e.target.value)}
              rows={4}
            />
            <div className="space-y-2">
              <Button onClick={() => testCustomTrigger('new_quote')} variant="outline" className="w-full">
                Test as Quote
              </Button>
              <Button onClick={() => testCustomTrigger('newsletter_signup')} variant="outline" className="w-full">
                Test as Newsletter
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Workflows */}
      <Card>
        <CardHeader>
          <CardTitle>Active Workflows ({workflows.filter(w => w.active).length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {workflows.filter(w => w.active).map((workflow) => (
              <div key={workflow.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getTriggerIcon(getTriggerType(workflow))}
                  <div>
                    <h3 className="font-medium">{workflow.name}</h3>
                    <p className="text-sm text-gray-500">
                      Trigger: {getTriggerType(workflow)} • {workflow.nodes.length} nodes
                    </p>
                  </div>
                </div>
                <Badge variant={workflow.active ? "default" : "secondary"}>
                  {workflow.active ? "Active" : "Inactive"}
                </Badge>
              </div>
            ))}
            {workflows.filter(w => w.active).length === 0 && (
              <p className="text-center text-gray-500 py-4">No active workflows found</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Executions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Executions ({executions.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {executions.slice(0, 10).map((execution) => (
              <div key={execution.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(execution.status)}
                  <div>
                    <h3 className="font-medium">{execution.workflow_name}</h3>
                    <p className="text-sm text-gray-500">
                      {new Date(execution.executed_at).toLocaleString()}
                      {execution.execution_time_ms && ` • ${execution.execution_time_ms}ms`}
                    </p>
                  </div>
                </div>
                <Badge variant={
                  execution.status === 'success' ? "default" :
                  execution.status === 'failed' ? "destructive" : "secondary"
                }>
                  {execution.status}
                </Badge>
              </div>
            ))}
            {executions.length === 0 && (
              <p className="text-center text-gray-500 py-4">No executions found</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowTesting;
