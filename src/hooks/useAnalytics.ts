import { useEffect, useRef, useState } from 'react'
import { trackBlogPostView, trackBlogPostClick, trackBlogPostReadingTime } from '@/lib/supabase'

interface UseAnalyticsOptions {
  postId: string
  enabled?: boolean
}

export const useAnalytics = ({ postId, enabled = true }: UseAnalyticsOptions) => {
  const [sessionId] = useState(() => crypto.randomUUID())
  const startTime = useRef<number>(Date.now())
  const maxScroll = useRef<number>(0)
  const hasTrackedView = useRef<boolean>(false)
  const readingTimeTracked = useRef<boolean>(false)

  // Track page view
  useEffect(() => {
    if (!enabled || !postId || hasTrackedView.current) return

    const trackView = async () => {
      try {
        await trackBlogPostView(postId, sessionId)
        hasTrackedView.current = true
      } catch (error) {
        console.error('Failed to track blog post view:', error)
      }
    }

    // Track view after a short delay to ensure it's a real visit
    const timer = setTimeout(trackView, 1000)
    return () => clearTimeout(timer)
  }, [postId, sessionId, enabled])

  // Track scroll percentage
  useEffect(() => {
    if (!enabled || !postId) return

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercentage = Math.round((scrollTop / scrollHeight) * 100)
      
      if (scrollPercentage > maxScroll.current) {
        maxScroll.current = Math.min(scrollPercentage, 100)
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [enabled, postId])

  // Track reading time on page unload
  useEffect(() => {
    if (!enabled || !postId) return

    const handleBeforeUnload = async () => {
      if (readingTimeTracked.current) return

      const timeSpent = Math.round((Date.now() - startTime.current) / 1000)
      
      // Only track if user spent at least 10 seconds on the page
      if (timeSpent >= 10) {
        try {
          // Use sendBeacon for reliable tracking on page unload
          const data = {
            post_id: postId,
            session_id: sessionId,
            time_spent: timeSpent,
            scroll_percentage: maxScroll.current
          }

          if (navigator.sendBeacon) {
            navigator.sendBeacon(
              '/api/track-reading-time',
              JSON.stringify(data)
            )
          } else {
            // Fallback for browsers without sendBeacon
            await trackBlogPostReadingTime(postId, timeSpent, maxScroll.current, sessionId)
          }
          readingTimeTracked.current = true
        } catch (error) {
          console.error('Failed to track reading time:', error)
        }
      }
    }

    // Track on visibility change (tab switch, minimize, etc.)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        handleBeforeUnload()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      // Track reading time when component unmounts
      handleBeforeUnload()
    }
  }, [postId, sessionId, enabled])

  // Function to manually track clicks
  const trackClick = async (clickType: string, clickTarget: string) => {
    if (!enabled || !postId) return

    try {
      await trackBlogPostClick(postId, clickType, clickTarget, sessionId)
    } catch (error) {
      console.error('Failed to track click:', error)
    }
  }

  return {
    trackClick,
    sessionId
  }
}

// Hook for tracking specific elements
export const useClickTracking = (postId: string, enabled = true) => {
  const { trackClick } = useAnalytics({ postId, enabled })

  const trackLinkClick = (url: string) => {
    trackClick('external_link', url)
  }

  const trackCTAClick = (ctaText: string) => {
    trackClick('cta_button', ctaText)
  }

  const trackShareClick = (platform: string) => {
    trackClick('share_button', platform)
  }

  const trackAuthorClick = () => {
    trackClick('author_link', 'author_profile')
  }

  return {
    trackLinkClick,
    trackCTAClick,
    trackShareClick,
    trackAuthorClick
  }
}
