-- Test Blog RLS Policies
-- Run this in Supabase Dashboard > SQL Editor to verify the policies are working

-- 1. Check if <PERSON>'s profile exists and has the correct role
SELECT 
    'Stephen Profile Check' as test_name,
    id,
    email,
    role,
    full_name
FROM profiles 
WHERE email = '<EMAIL>';

-- 2. Check current RLS policies on blog_posts
SELECT 
    'Current RLS Policies' as test_name,
    policyname,
    cmd,
    permissive,
    roles
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 3. Check if there are any blog posts at all (bypassing R<PERSON> temporarily)
SET row_security = off;
SELECT 
    'Total Posts (No RLS)' as test_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE status = 'published') as published_count,
    COUNT(*) FILTER (WHERE status = 'draft') as draft_count,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts
FROM blog_posts;
SET row_security = on;

-- 4. Test what <PERSON> can see with <PERSON><PERSON> enabled (simulate his session)
-- First, let's see what the current user context shows
SELECT 
    'Current Auth Context' as test_name,
    auth.uid() as current_user_id,
    auth.role() as current_role;

-- 5. Test the policies by checking what posts would be visible
-- This simulates what Stephen should see
SELECT 
    'Posts Stephen Should See' as test_name,
    COUNT(*) as visible_count
FROM blog_posts
WHERE (
    -- Policy 1: Public MBI posts
    (status = 'published' AND submission_status = 'published_mbi') OR
    -- Policy 2: SaaS Owner can see everything (if Stephen is saas_owner)
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
        AND role IN ('saas_owner', 'super_admin')
    ) OR
    -- Policy 3: Organization posts (if Stephen is in an org)
    (submission_status = 'private' AND organization_id IN (
        SELECT organization_id FROM organization_members 
        WHERE user_id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
        AND is_active = true
    )) OR
    -- Policy 4: Own posts
    author_id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
);

-- 6. Show sample posts with their details
SELECT 
    'Sample Posts' as test_name,
    id,
    title,
    status,
    submission_status,
    author_id,
    organization_id,
    created_at
FROM blog_posts
ORDER BY created_at DESC
LIMIT 5;

-- 7. Test if a regular user can create posts (check INSERT policy)
-- This will show if the policy allows authenticated users to create posts
SELECT 
    'Insert Policy Test' as test_name,
    'Authenticated users should be able to insert posts where auth.uid() = author_id' as policy_description;
