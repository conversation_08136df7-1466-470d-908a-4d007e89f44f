-- Debug and Fix Profiles Data
-- This script will identify and fix the problematic data

-- 0. Add subscription_plan column if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'free';

-- 1. First, let's see ALL the data in profiles table
SELECT 'All profiles data:' as info;
SELECT id, email, role, subscription_plan FROM profiles ORDER BY id;

-- 2. Check for any NULL or invalid role values
SELECT 'Profiles with invalid roles:' as info;
SELECT id, email, role, subscription_plan 
FROM profiles 
WHERE role IS NULL OR role NOT IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner');

-- 3. Check for any NULL or invalid subscription_plan values
SELECT 'Profiles with invalid subscription_plan:' as info;
SELECT id, email, role, subscription_plan 
FROM profiles 
WHERE subscription_plan IS NULL OR subscription_plan NOT IN ('free', 'basic', 'pro', 'enterprise');

-- 4. Drop ALL existing constraints on profiles table
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'profiles'::regclass 
        AND contype = 'c'  -- Check constraints only
    LOOP
        EXECUTE 'ALTER TABLE profiles DROP CONSTRAINT ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- 5. Fix ALL invalid role data
UPDATE profiles 
SET role = 'user' 
WHERE role IS NULL OR role = '' OR role NOT IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner');

-- 6. Fix ALL invalid subscription_plan data
UPDATE profiles 
SET subscription_plan = 'free' 
WHERE subscription_plan IS NULL OR subscription_plan = '' OR subscription_plan NOT IN ('free', 'basic', 'pro', 'enterprise');

-- 7. Show what we have after fixing
SELECT 'After fixing data:' as info;
SELECT id, email, role, subscription_plan FROM profiles ORDER BY id;

-- 8. Verify no invalid data remains
SELECT 'Any remaining invalid roles:' as info;
SELECT COUNT(*) as invalid_role_count
FROM profiles 
WHERE role IS NULL OR role NOT IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner');

SELECT 'Any remaining invalid subscription_plans:' as info;
SELECT COUNT(*) as invalid_subscription_count
FROM profiles 
WHERE subscription_plan IS NULL OR subscription_plan NOT IN ('free', 'basic', 'pro', 'enterprise');

-- 9. Only add constraints if data is clean
DO $$
DECLARE
    invalid_count INTEGER;
BEGIN
    -- Check for invalid roles
    SELECT COUNT(*) INTO invalid_count
    FROM profiles 
    WHERE role IS NULL OR role NOT IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner');
    
    IF invalid_count = 0 THEN
        ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
        CHECK (role IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner'));
        RAISE NOTICE 'Added role constraint successfully';
    ELSE
        RAISE NOTICE 'Cannot add role constraint - % invalid rows remain', invalid_count;
    END IF;
    
    -- Check for invalid subscription_plans
    SELECT COUNT(*) INTO invalid_count
    FROM profiles 
    WHERE subscription_plan IS NULL OR subscription_plan NOT IN ('free', 'basic', 'pro', 'enterprise');
    
    IF invalid_count = 0 THEN
        ALTER TABLE profiles ADD CONSTRAINT profiles_subscription_plan_check 
        CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise'));
        RAISE NOTICE 'Added subscription_plan constraint successfully';
    ELSE
        RAISE NOTICE 'Cannot add subscription_plan constraint - % invalid rows remain', invalid_count;
    END IF;
END $$;

-- 10. Show final constraints
SELECT 'Final constraints:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

SELECT 'Debug and fix complete!' as status;
