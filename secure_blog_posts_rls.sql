-- Secure Blog Posts with Row Level Security (RLS)
-- Run this in Supabase Dashboard > SQL Editor
-- This ensures users can only access their own posts (unless admin+)

-- Enable RLS on blog_posts table if not already enabled
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can view their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Ad<PERSON> can view all posts" ON blog_posts;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all posts" ON blog_posts;

-- Policy 1: Anyone can view published posts (for public blog reading)
CREATE POLICY "Anyone can view published posts" ON blog_posts
  FOR SELECT USING (status = 'published');

-- Policy 2: Users can view their own posts (all statuses)
CREATE POLICY "Users can view their own posts" ON blog_posts
  FOR SELECT USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 3: Users can insert their own posts
CREATE POLICY "Users can insert their own posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('editor', 'admin', 'super_admin', 'owner')
    )
  );

-- Policy 4: Users can update their own posts (or admins can update any)
CREATE POLICY "Users can update their own posts" ON blog_posts
  FOR UPDATE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Policy 5: Users can delete their own posts (or admins can delete any)
CREATE POLICY "Users can delete their own posts" ON blog_posts
  FOR DELETE USING (
    auth.uid() = author_id OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner')
    )
  );

-- Verify the policies are in place
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'blog_posts';

-- Test the security (these should work for authenticated users)
-- SELECT * FROM blog_posts WHERE status = 'published'; -- Should work for everyone
-- SELECT * FROM blog_posts WHERE author_id = auth.uid(); -- Should work for post owners
-- INSERT INTO blog_posts (title, content, author_id) VALUES ('Test', 'Content', auth.uid()); -- Should work for editors+
