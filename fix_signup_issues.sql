-- Fix Sign-up Issues
-- This script addresses common sign-up problems in the application

-- 0. Ensure required tables exist
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user',
  subscription_plan TEXT DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  avatar_url TEXT,
  subscription_plan TEXT DEFAULT 'free',
  subscription_status TEXT DEFAULT 'active',
  workflow_credits_used INTEGER DEFAULT 0,
  workflow_credits_limit INTEGER DEFAULT 100,
  ai_credits_used INTEGER DEFAULT 0,
  ai_credits_limit INTEGER DEFAULT 50,
  credits_reset_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  UNIQUE(organization_id, user_id)
);

-- 1. Fix role constraints (allow 'user' role which is the default)
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('user', 'editor', 'admin', 'super_admin', 'owner', 'saas_owner', 'platform_owner'));

-- 2. Ensure profiles table has all necessary columns
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'free'
CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise'));

-- 2.1. Update organization credit limits for new pricing structure
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;

-- 3. Create or replace the user signup trigger function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_org_id UUID;
  workspace_name TEXT;
  workspace_slug TEXT;
BEGIN
  -- Insert user profile with error handling
  BEGIN
    INSERT INTO profiles (id, email, full_name, role, subscription_plan)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
      CASE
        WHEN NEW.email = '<EMAIL>' THEN 'saas_owner'
        ELSE 'user'
      END,
      CASE
        WHEN NEW.email = '<EMAIL>' THEN 'enterprise'
        ELSE 'free'
      END
    )
    ON CONFLICT (id) DO UPDATE SET
      email = NEW.email,
      full_name = COALESCE(NEW.raw_user_meta_data->>'full_name', profiles.full_name, NEW.email);
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error creating profile for user %: %', NEW.email, SQLERRM;
      RETURN NEW; -- Continue even if profile creation fails
  END;

  -- Create personal organization for the user (skip for Stephen as he has MBI)
  IF NEW.email != '<EMAIL>' THEN
    BEGIN
      -- Prepare workspace name and slug
      workspace_name := COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email) || ' Workspace';
      workspace_slug := 'user-' || NEW.id;

      INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
      VALUES (
        workspace_name,
        workspace_slug,
        'free',
        100,  -- Free tier: 100 workflow credits (competitive with Zapier)
        50    -- Free tier: 50 AI credits
      )
      ON CONFLICT (slug) DO NOTHING
      RETURNING id INTO user_org_id;

      -- Get organization ID if it already existed
      IF user_org_id IS NULL THEN
        SELECT id INTO user_org_id FROM organizations WHERE slug = workspace_slug;
      END IF;

      -- Add user as owner of their organization
      IF user_org_id IS NOT NULL THEN
        INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
        VALUES (user_org_id, NEW.id, 'owner', NOW(), true)
        ON CONFLICT (organization_id, user_id) DO UPDATE SET
          role = 'owner',
          is_active = true;
      END IF;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error creating organization for user %: %', NEW.email, SQLERRM;
        -- Continue even if organization creation fails
    END;
  ELSE
    -- For Stephen, ensure he's added to MBI organization
    BEGIN
      INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
      SELECT o.id, NEW.id, 'owner', NOW(), true
      FROM organizations o
      WHERE o.slug = 'mbi'
      ON CONFLICT (organization_id, user_id) DO UPDATE SET
        role = 'owner',
        is_active = true;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE LOG 'Error adding Stephen to MBI organization: %', SQLERRM;
        -- Continue even if MBI organization assignment fails
    END;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 5. Ensure MBI organization exists with updated limits
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
VALUES ('Millennial Business Innovations', 'mbi', 'enterprise', 25000, -1)
ON CONFLICT (slug) DO UPDATE SET
  subscription_plan = 'enterprise',
  workflow_credits_limit = 25000,  -- Updated enterprise limit
  ai_credits_limit = -1;           -- Unlimited AI credits

-- 6. Fix any existing users without profiles or organizations
DO $$
DECLARE
  user_record RECORD;
  user_org_id UUID;
BEGIN
  FOR user_record IN SELECT * FROM auth.users LOOP
    -- Ensure profile exists
    INSERT INTO profiles (id, email, full_name, role, subscription_plan)
    VALUES (
      user_record.id,
      user_record.email,
      COALESCE(user_record.raw_user_meta_data->>'full_name', user_record.email),
      CASE 
        WHEN user_record.email = '<EMAIL>' THEN 'saas_owner'
        ELSE 'user'
      END,
      CASE 
        WHEN user_record.email = '<EMAIL>' THEN 'enterprise'
        ELSE 'free'
      END
    )
    ON CONFLICT (id) DO NOTHING;

    -- Ensure organization membership exists
    IF user_record.email = '<EMAIL>' THEN
      -- Add Stephen to MBI organization
      INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
      SELECT o.id, user_record.id, 'owner', NOW(), true
      FROM organizations o
      WHERE o.slug = 'mbi'
      ON CONFLICT (organization_id, user_id) DO UPDATE SET
        role = 'owner',
        is_active = true;
    ELSE
      -- Create personal organization if it doesn't exist
      INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
      VALUES (
        COALESCE(user_record.raw_user_meta_data->>'full_name', user_record.email) || ' Workspace',
        'user-' || user_record.id,
        'free',
        100,  -- Free tier: 100 workflow credits
        50    -- Free tier: 50 AI credits
      )
      ON CONFLICT (slug) DO NOTHING
      RETURNING id INTO user_org_id;

      -- Get organization ID if it already existed
      IF user_org_id IS NULL THEN
        SELECT id INTO user_org_id FROM organizations WHERE slug = 'user-' || user_record.id;
      END IF;

      -- Add user as owner
      INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
      VALUES (user_org_id, user_record.id, 'owner', NOW(), true)
      ON CONFLICT (organization_id, user_id) DO UPDATE SET
        role = 'owner',
        is_active = true;
    END IF;
  END LOOP;
END $$;

-- 7. Enable RLS on key tables if not already enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;

-- 8. Create basic RLS policies for profiles (allow users to read their own profile)
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- 9. Allow profile creation during signup
DROP POLICY IF EXISTS "Enable profile creation during signup" ON profiles;
CREATE POLICY "Enable profile creation during signup" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 10. Add helpful comment about new pricing structure
-- New competitive pricing:
-- Free: $0 - Unlimited blogs, 100 workflow credits, 50 AI credits
-- Basic: $5/month - Unlimited blogs, 1,000 workflow credits, 200 AI credits
-- Pro: $8/month - Unlimited blogs, 3,000 workflow credits, 500 AI credits
-- Enterprise: Custom - Unlimited everything

SELECT 'Sign-up issues fix completed with new competitive pricing!' as status;
