-- Fix blog policies to allow ALL organization members (not just admins) to access their workspace
-- Run this AFTER the main hybrid blog migration and the data fix

-- 1. Drop the restrictive policies
DROP POLICY IF EXISTS "Users can create private posts" ON blog_posts;
DROP POLICY IF EXISTS "Organization members can view private posts" ON blog_posts;

-- 2. Create more inclusive policies for organization members

-- Policy: ALL organization members can view their organization's private posts
CREATE POLICY "All org members can view private posts" ON blog_posts
  FOR SELECT USING (
    submission_status = 'private' AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
      -- No role restriction - all active members can view
    )
  );

-- Policy: ALL organization members can create posts in their organization
CREATE POLICY "All org members can create private posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      -- No role restriction - all active members can create
    ) AND
    submission_status = 'private'
  );

-- 3. Update the update policy to be more inclusive
DROP POLICY IF EXISTS "Users can update posts in their scope" ON blog_posts;

CREATE POLICY "Users can update posts in their scope" ON blog_posts
  FOR UPDATE USING (
    -- Own posts (any user can update their own posts)
    (auth.uid() = author_id) OR
    -- Org admins/owners can update any org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can update everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- 4. Update the delete policy to be more inclusive
DROP POLICY IF EXISTS "Users can delete posts in their scope" ON blog_posts;

CREATE POLICY "Users can delete posts in their scope" ON blog_posts
  FOR DELETE USING (
    -- Own posts (any user can delete their own posts)
    (auth.uid() = author_id) OR
    -- Org admins/owners can delete any org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- Super admins can delete everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- 5. Update blog submissions policy to allow all org members to submit
DROP POLICY IF EXISTS "Organization admins can create submissions" ON blog_submissions;

CREATE POLICY "All org members can create submissions" ON blog_submissions
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      -- No role restriction - all active members can submit
    ) AND
    submitted_by = auth.uid()
  );

-- 6. Show current organization membership for verification
SELECT 
    'Organization Membership Status' as info,
    o.name as organization_name,
    COUNT(om.user_id) as total_members,
    COUNT(*) FILTER (WHERE om.role = 'owner') as owners,
    COUNT(*) FILTER (WHERE om.role = 'admin') as admins,
    COUNT(*) FILTER (WHERE om.role = 'editor') as editors,
    COUNT(*) FILTER (WHERE om.role = 'member') as regular_members
FROM organizations o
LEFT JOIN organization_members om ON o.id = om.organization_id AND om.is_active = true
GROUP BY o.id, o.name
ORDER BY total_members DESC;
