#!/bin/bash

# Script to safely remove .env from git history
# Run this in your project root directory

echo "🔒 Fixing Git Credentials Security Issue"
echo "========================================"

# Step 1: Remove .env from current tracking
echo "1. Removing .env from git tracking..."
git rm --cached .env
git add .gitignore
git commit -m "security: Remove .env from tracking and update .gitignore"

# Step 2: Clean git history (removes .env from all commits)
echo "2. Cleaning git history..."
echo "⚠️  This will rewrite git history - make sure you have backups!"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Remove .env from entire git history
    git filter-branch --force --index-filter \
        'git rm --cached --ignore-unmatch .env' \
        --prune-empty --tag-name-filter cat -- --all
    
    # Clean up
    git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    echo "✅ Git history cleaned!"
else
    echo "❌ Skipped git history cleaning"
fi

# Step 3: Force push to remote (if you have one)
echo "3. Updating remote repository..."
read -p "Do you want to force push to remote? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git push origin --force --all
    git push origin --force --tags
    echo "✅ Remote repository updated!"
else
    echo "⚠️  Remember to force push later: git push origin --force --all"
fi

echo ""
echo "🎉 Security fix complete!"
echo ""
echo "📋 Next steps:"
echo "1. Verify your .env file still exists locally"
echo "2. Consider rotating your Supabase credentials as a precaution"
echo "3. Run: npm audit fix"
echo ""
echo "Your credentials are now safe! 🔐"
