-- Analytics Database Setup for MBI Blog
-- Run this SQL in your Supabase Dashboard > SQL Editor
-- This creates comprehensive analytics tracking for blog posts

-- 1. Add analytics columns to blog_posts table if they don't exist
DO $$ 
BEGIN
    -- Add analytics columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN view_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'unique_view_count') THEN
        ALTER TABLE blog_posts ADD COLUMN unique_view_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'click_count') THEN
        ALTER TABLE blog_posts ADD COLUMN click_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'avg_reading_time') THEN
        ALTER TABLE blog_posts ADD COLUMN avg_reading_time INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'bounce_rate') THEN
        ALTER TABLE blog_posts ADD COLUMN bounce_rate DECIMAL(5,2) DEFAULT 0.00;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'reaction_count') THEN
        ALTER TABLE blog_posts ADD COLUMN reaction_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'comment_count') THEN
        ALTER TABLE blog_posts ADD COLUMN comment_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'blog_posts' AND column_name = 'share_count') THEN
        ALTER TABLE blog_posts ADD COLUMN share_count INTEGER DEFAULT 0;
    END IF;
END $$;

-- 2. Blog Post Views Tracking
CREATE TABLE IF NOT EXISTS blog_post_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  device_type TEXT CHECK (device_type IN ('desktop', 'mobile', 'tablet')),
  browser TEXT,
  os TEXT,
  country TEXT,
  city TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Blog Post Clicks Tracking
CREATE TABLE IF NOT EXISTS blog_post_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  click_type TEXT DEFAULT 'link',
  target_url TEXT,
  element_id TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Blog Post Reading Time Tracking
CREATE TABLE IF NOT EXISTS blog_post_reading_time (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  time_spent INTEGER NOT NULL, -- in seconds
  scroll_percentage INTEGER DEFAULT 0,
  is_bounce BOOLEAN DEFAULT FALSE,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, session_id)
);

-- 5. Blog Post Reactions (likes, hearts, etc.)
CREATE TABLE IF NOT EXISTS blog_post_reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  anonymous_id TEXT, -- For anonymous users
  ip_address TEXT, -- For spam prevention
  reaction_type VARCHAR(20) NOT NULL DEFAULT 'like',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id, reaction_type), -- For authenticated users
  UNIQUE(post_id, anonymous_id, reaction_type) -- For anonymous users
);

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_post_views_post_id ON blog_post_views(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_created_at ON blog_post_views(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_session_id ON blog_post_views(session_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_views_organization_id ON blog_post_views(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_post_id ON blog_post_clicks(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_created_at ON blog_post_clicks(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_clicks_organization_id ON blog_post_clicks(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_post_id ON blog_post_reading_time(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_created_at ON blog_post_reading_time(created_at);
CREATE INDEX IF NOT EXISTS idx_blog_post_reading_time_organization_id ON blog_post_reading_time(organization_id);

CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_post_id ON blog_post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_post_reactions_organization_id ON blog_post_reactions(organization_id);

-- 7. Functions to update blog_posts analytics columns
CREATE OR REPLACE FUNCTION update_blog_post_views()
RETURNS TRIGGER AS $$
BEGIN
  -- Update view count and unique view count
  UPDATE blog_posts 
  SET 
    view_count = (SELECT COUNT(*) FROM blog_post_views WHERE post_id = NEW.post_id),
    unique_view_count = (SELECT COUNT(DISTINCT session_id) FROM blog_post_views WHERE post_id = NEW.post_id)
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_clicks()
RETURNS TRIGGER AS $$
BEGIN
  -- Update click count
  UPDATE blog_posts 
  SET click_count = (SELECT COUNT(*) FROM blog_post_clicks WHERE post_id = NEW.post_id)
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_reading_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update average reading time and bounce rate
  UPDATE blog_posts 
  SET 
    avg_reading_time = (
      SELECT COALESCE(AVG(time_spent), 0)::INTEGER 
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    ),
    bounce_rate = (
      SELECT COALESCE(
        (COUNT(*) FILTER (WHERE is_bounce = true)::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 
        0
      )
      FROM blog_post_reading_time 
      WHERE post_id = NEW.post_id
    )
  WHERE id = NEW.post_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_blog_post_reactions()
RETURNS TRIGGER AS $$
BEGIN
  -- Update reaction count
  UPDATE blog_posts 
  SET reaction_count = (SELECT COUNT(*) FROM blog_post_reactions WHERE post_id = COALESCE(NEW.post_id, OLD.post_id))
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 8. Create triggers to automatically update analytics
DROP TRIGGER IF EXISTS trigger_update_blog_views ON blog_post_views;
CREATE TRIGGER trigger_update_blog_views
  AFTER INSERT ON blog_post_views
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_views();

DROP TRIGGER IF EXISTS trigger_update_blog_clicks ON blog_post_clicks;
CREATE TRIGGER trigger_update_blog_clicks
  AFTER INSERT ON blog_post_clicks
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_clicks();

DROP TRIGGER IF EXISTS trigger_update_blog_reading_stats ON blog_post_reading_time;
CREATE TRIGGER trigger_update_blog_reading_stats
  AFTER INSERT OR UPDATE ON blog_post_reading_time
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_reading_stats();

DROP TRIGGER IF EXISTS trigger_update_blog_reactions ON blog_post_reactions;
CREATE TRIGGER trigger_update_blog_reactions
  AFTER INSERT OR DELETE ON blog_post_reactions
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_reactions();

-- 9. Enable RLS (Row Level Security)
ALTER TABLE blog_post_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reading_time ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_reactions ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies for analytics tracking
-- Allow public read access for analytics (admins can see all data)
CREATE POLICY "Allow admin read access" ON blog_post_views FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner', 'saas_owner')
    )
  );

-- Allow public insert for tracking (anonymous users can track views)
CREATE POLICY "Allow public view tracking" ON blog_post_views FOR INSERT 
  WITH CHECK (true);

-- Similar policies for clicks
CREATE POLICY "Allow admin read clicks" ON blog_post_clicks FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner', 'saas_owner')
    )
  );

CREATE POLICY "Allow public click tracking" ON blog_post_clicks FOR INSERT 
  WITH CHECK (true);

-- Similar policies for reading time
CREATE POLICY "Allow admin read reading time" ON blog_post_reading_time FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner', 'saas_owner')
    )
  );

CREATE POLICY "Allow public reading time tracking" ON blog_post_reading_time FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public reading time updates" ON blog_post_reading_time FOR UPDATE 
  USING (true) WITH CHECK (true);

-- Similar policies for reactions
CREATE POLICY "Allow admin read reactions" ON blog_post_reactions FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin', 'owner', 'saas_owner')
    )
  );

CREATE POLICY "Allow public reactions" ON blog_post_reactions FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow users to delete their own reactions" ON blog_post_reactions FOR DELETE 
  USING (user_id = auth.uid() OR anonymous_id IS NOT NULL);

-- 11. Create a view for easy analytics querying
CREATE OR REPLACE VIEW blog_analytics_summary AS
SELECT 
  bp.id,
  bp.title,
  bp.slug,
  bp.organization_id,
  bp.view_count,
  bp.unique_view_count,
  bp.click_count,
  bp.avg_reading_time,
  bp.bounce_rate,
  bp.reaction_count,
  bp.comment_count,
  bp.share_count,
  bp.published_at,
  bp.created_at,
  -- Additional calculated metrics
  CASE 
    WHEN bp.view_count > 0 
    THEN ROUND((bp.click_count::DECIMAL / bp.view_count) * 100, 2)
    ELSE 0 
  END as click_through_rate,
  
  -- Recent performance (last 7 days)
  (
    SELECT COUNT(*) 
    FROM blog_post_views bpv 
    WHERE bpv.post_id = bp.id 
    AND bpv.created_at >= NOW() - INTERVAL '7 days'
  ) as views_last_7_days
FROM blog_posts bp
WHERE bp.status = 'published'
ORDER BY bp.view_count DESC;

-- Success message
SELECT 'Analytics tables and triggers created successfully!' as status;
