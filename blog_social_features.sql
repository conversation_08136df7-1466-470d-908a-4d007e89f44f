-- Blog Social Features Database Schema
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Blog Post Reactions (likes, hearts, etc.)
CREATE TABLE IF NOT EXISTS blog_reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  reaction_type VARCHAR(20) NOT NULL DEFAULT 'like', -- 'like', 'heart', 'clap', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id, reaction_type)
);

-- 2. Blog Post Comments
CREATE TABLE IF NOT EXISTS blog_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES blog_comments(id) ON DELETE CASCADE, -- For nested replies
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Blog Post Saves/Bookmarks
CREATE TABLE IF NOT EXISTS blog_saves (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);

-- 4. Blog Post Shares (track sharing activity)
CREATE TABLE IF NOT EXISTS blog_shares (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL, -- Can be null for anonymous shares
  share_type VARCHAR(20) NOT NULL, -- 'link', 'twitter', 'facebook', 'linkedin', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add reaction and comment counts to blog_posts for performance
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS reaction_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS save_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS share_count INTEGER DEFAULT 0;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_reactions_post_id ON blog_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_reactions_user_id ON blog_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_post_id ON blog_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_user_id ON blog_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_parent_id ON blog_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_blog_saves_post_id ON blog_saves(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_saves_user_id ON blog_saves(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_shares_post_id ON blog_shares(post_id);

-- Enable RLS on all tables
ALTER TABLE blog_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_shares ENABLE ROW LEVEL SECURITY;

-- RLS Policies for blog_reactions
CREATE POLICY "Anyone can view reactions" ON blog_reactions FOR SELECT USING (true);
CREATE POLICY "Users can manage their own reactions" ON blog_reactions 
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for blog_comments
CREATE POLICY "Anyone can view comments" ON blog_comments FOR SELECT USING (true);
CREATE POLICY "Users can create comments" ON blog_comments 
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own comments" ON blog_comments 
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own comments" ON blog_comments 
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for blog_saves
CREATE POLICY "Users can view their own saves" ON blog_saves 
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own saves" ON blog_saves 
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for blog_shares
CREATE POLICY "Anyone can create shares" ON blog_shares FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can view share counts" ON blog_shares FOR SELECT USING (true);

-- Functions to update counts automatically
CREATE OR REPLACE FUNCTION update_blog_post_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update reaction count
  UPDATE blog_posts 
  SET reaction_count = (
    SELECT COUNT(*) FROM blog_reactions WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update comment count
  UPDATE blog_posts 
  SET comment_count = (
    SELECT COUNT(*) FROM blog_comments WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update save count
  UPDATE blog_posts 
  SET save_count = (
    SELECT COUNT(*) FROM blog_saves WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  -- Update share count
  UPDATE blog_posts 
  SET share_count = (
    SELECT COUNT(*) FROM blog_shares WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
  )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update counts
CREATE TRIGGER update_counts_on_reaction_change
  AFTER INSERT OR DELETE ON blog_reactions
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_counts();

CREATE TRIGGER update_counts_on_comment_change
  AFTER INSERT OR DELETE ON blog_comments
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_counts();

CREATE TRIGGER update_counts_on_save_change
  AFTER INSERT OR DELETE ON blog_saves
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_counts();

CREATE TRIGGER update_counts_on_share_change
  AFTER INSERT ON blog_shares
  FOR EACH ROW EXECUTE FUNCTION update_blog_post_counts();
