-- Quick Organization Fix
-- Run this in Supabase SQL Editor

-- 1. Create organizations for users who don't have any
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
SELECT
  CASE 
    WHEN p.full_name IS NOT NULL AND p.full_name != '' 
    THEN p.full_name || '''s Workspace'
    ELSE SPLIT_PART(p.email, '@', 1) || '''s Workspace'
  END as name,
  'user-' || p.id as slug,
  'free' as subscription_plan,
  100 as workflow_credits_limit,
  50 as ai_credits_limit
FROM profiles p
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.user_id = p.id AND om.is_active = true
)
ON CONFLICT (slug) DO NOTHING;

-- 2. Add users as owners of their organizations
INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
SELECT 
  o.id as organization_id,
  p.id as user_id,
  'owner' as role,
  NOW() as joined_at,
  true as is_active
FROM profiles p
JOIN organizations o ON o.slug = 'user-' || p.id
WHERE NOT EXISTS (
  SELECT 1 FROM organization_members om 
  WHERE om.organization_id = o.id AND om.user_id = p.id
)
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- 3. Show results
SELECT 'Organization fix completed!' as status;

SELECT 
  p.email,
  o.name as organization_name,
  o.slug,
  o.subscription_plan,
  o.workflow_credits_limit,
  o.ai_credits_limit
FROM profiles p
JOIN organization_members om ON om.user_id = p.id AND om.is_active = true
JOIN organizations o ON o.id = om.organization_id
ORDER BY p.created_at;
