-- Add custom_name column to blog_posts table
-- Run this in Supabase Dashboard > SQL Editor

-- Step 1: Add custom_name column if it doesn't exist
ALTER TABLE blog_posts
ADD COLUMN IF NOT EXISTS custom_name TEXT;

-- Step 2: Find and drop ALL existing CHECK constraints on author_display
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find all check constraints on the author_display column
    FOR constraint_name IN
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_name = 'blog_posts'
        AND tc.constraint_type = 'CHECK'
        AND cc.check_clause LIKE '%author_display%'
    LOOP
        EXECUTE 'ALTER TABLE blog_posts DROP CONSTRAINT IF EXISTS ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END LOOP;
END $$;

-- Step 3: Add new CHECK constraint that includes custom_name
<PERSON><PERSON><PERSON> TABLE blog_posts
ADD CONSTRAINT blog_posts_author_display_check
CHECK (author_display IN ('real_name', 'anonymous', 'mbi_team', 'custom_name'));

-- Step 4: Add comments to document the columns
COMMENT ON COLUMN blog_posts.author_display IS 'Author display preference: real_name, anonymous, mbi_team, or custom_name';
COMMENT ON COLUMN blog_posts.custom_name IS 'Custom name to display when author_display is set to custom_name';

-- Step 5: Verify the changes
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'blog_posts'
AND column_name IN ('author_display', 'custom_name')
ORDER BY column_name;
