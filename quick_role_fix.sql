-- Quick Role Fix for Free Users
-- Run this in Supabase Dashboard > SQL Editor
-- This enables workflows and email integrations for free users

-- First, drop the existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Add the new constraint that includes 'user' role
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check
CHECK (role IN ('saas_owner', 'super_admin', 'admin', 'editor', 'user', 'viewer'));

-- Update all 'viewer' role users to 'user' role (except <PERSON> who should be saas_owner)
-- This gives free users access to workflows and email integrations with limited credits
UPDATE profiles
SET role = 'user'
WHERE role = 'viewer'
AND email != '<EMAIL>';

-- Verify the changes
SELECT 
  'User Role Update Summary' as summary,
  role,
  COUNT(*) as user_count
FROM profiles 
GROUP BY role
ORDER BY role;

-- Show updated users
SELECT 
  'Updated Users' as status,
  email,
  role,
  full_name
FROM profiles 
WHERE role = 'user'
ORDER BY email;

SELECT 'Role update completed! Users can now access workflows and email integrations.' as status;
