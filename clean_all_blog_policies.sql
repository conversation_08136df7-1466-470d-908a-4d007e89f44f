-- Clean All Blog Policies and Start Fresh
-- Run this in Supabase Dashboard > SQL Editor
-- This will remove ALL existing policies and create new clean ones

-- 1. First, let's see what policies currently exist
SELECT 
    'Current Policies Before Cleanup' as step,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 2. Drop ALL existing blog_posts policies (comprehensive list)
DROP POLICY IF EXISTS "Anyone can view published posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can view their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON blog_posts;
DROP POLICY IF EXISTS "Admins can view all posts" ON blog_posts;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all posts" ON blog_posts;
DROP POLICY IF EXISTS "temp_view_all_posts" ON blog_posts;
DROP POLICY IF EXISTS "public_view_mbi_posts" ON blog_posts;
DROP POLICY IF EXISTS "org_members_view_private" ON blog_posts;
DROP POLICY IF EXISTS "super_admin_view_all" ON blog_posts;
DROP POLICY IF EXISTS "org_members_create_posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update posts in their scope" ON blog_posts;
DROP POLICY IF EXISTS "delete_posts_policy" ON blog_posts;
DROP POLICY IF EXISTS "Blog posts access control" ON blog_posts;
DROP POLICY IF EXISTS "All org members can view private posts" ON blog_posts;
DROP POLICY IF EXISTS "All org members can create private posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete posts in their scope" ON blog_posts;
DROP POLICY IF EXISTS "public_view_published_mbi_posts" ON blog_posts;
DROP POLICY IF EXISTS "saas_owner_super_admin_view_all" ON blog_posts;
DROP POLICY IF EXISTS "org_members_view_private_posts" ON blog_posts;
DROP POLICY IF EXISTS "users_view_own_posts" ON blog_posts;
DROP POLICY IF EXISTS "authenticated_users_create_posts" ON blog_posts;
DROP POLICY IF EXISTS "users_update_posts" ON blog_posts;
DROP POLICY IF EXISTS "users_delete_posts" ON blog_posts;

-- 3. Verify all policies are removed
SELECT 
    'Policies After Cleanup' as step,
    COUNT(*) as remaining_policies
FROM pg_policies 
WHERE tablename = 'blog_posts';

-- 4. Create NEW comprehensive policies that include saas_owner

-- Policy 1: Anyone can view published MBI posts (public blog)
CREATE POLICY "public_view_published_mbi_posts" ON blog_posts
  FOR SELECT USING (
    status = 'published' AND submission_status = 'published_mbi'
  );

-- Policy 2: SaaS Owner and Super Admins can see EVERYTHING
CREATE POLICY "saas_owner_super_admin_view_all" ON blog_posts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('saas_owner', 'super_admin')
    )
  );

-- Policy 3: Organization members can view their private workspace posts
CREATE POLICY "org_members_view_private_posts" ON blog_posts
  FOR SELECT USING (
    submission_status = 'private' AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Policy 4: Users can view their own posts (regardless of status)
CREATE POLICY "users_view_own_posts" ON blog_posts
  FOR SELECT USING (auth.uid() = author_id);

-- Policy 5: ALL authenticated users can create posts (either private or for MBI submission)
CREATE POLICY "authenticated_users_create_posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    auth.uid() IS NOT NULL
  );

-- Policy 6: Users can update their own posts + Admins can update org posts + SaaS/Super admins can update all
CREATE POLICY "users_update_posts" ON blog_posts
  FOR UPDATE USING (
    -- Own posts
    (auth.uid() = author_id) OR
    -- Org admins/owners can update org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- SaaS Owner and Super admins can update everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('saas_owner', 'super_admin')
    )
  );

-- Policy 7: Users can delete their own posts + Admins can delete org posts + SaaS/Super admins can delete all
CREATE POLICY "users_delete_posts" ON blog_posts
  FOR DELETE USING (
    -- Own posts
    (auth.uid() = author_id) OR
    -- Org admins/owners can delete org posts
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() 
      AND is_active = true
      AND role IN ('admin', 'owner')
    )) OR
    -- SaaS Owner and Super admins can delete everything
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('saas_owner', 'super_admin')
    )
  );

-- 5. Verify new policies are created
SELECT 
    'New Policies Created' as step,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE tablename = 'blog_posts'
ORDER BY policyname;

-- 6. Check Stephen's profile
SELECT
    'Stephen Profile Check' as step,
    id,
    email,
    role,
    full_name
FROM profiles
WHERE email = '<EMAIL>';

-- 7. Test posts count (should work now)
SELECT
    'Posts Count Test' as step,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE status = 'published') as published_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts
FROM blog_posts;
