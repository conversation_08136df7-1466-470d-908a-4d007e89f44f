# Deploy Email Function to Supabase

## Prerequisites

1. **Supabase CLI installed**:
   ```bash
   npm install -g supabase
   ```

2. **Login to Supabase**:
   ```bash
   supabase login
   ```

3. **Link your project** (if not already linked):
   ```bash
   supabase link --project-ref YOUR_PROJECT_REF
   ```

## Deploy the Email Function

1. **Deploy the function**:
   ```bash
   supabase functions deploy send-email
   ```

2. **Verify deployment**:
   ```bash
   supabase functions list
   ```

## Test the Function

You can test the function directly:

```bash
curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-email' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "integrationId": "your-integration-id",
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "<h1>Test Email</h1><p>This is a test email from the server-side function.</p>",
    "testEmail": true
  }'
```

## Environment Variables

The function uses these environment variables (automatically available in Supabase):
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY` - Service role key for database access

## Function Features

✅ **Gmail SMTP Support**
- Uses Gmail's SMTP server (smtp.gmail.com:587)
- Requires app password (not regular password)
- TLS encryption enabled

✅ **Outlook SMTP Support**  
- Uses Outlook's SMTP server (smtp-mail.outlook.com:587)
- Works with regular password or app password
- TLS encryption enabled

✅ **Custom SMTP Support**
- Configurable SMTP server settings
- Supports any SMTP-compatible email provider
- Configurable TLS/SSL settings

✅ **Error Handling & Logging**
- Comprehensive error handling
- Execution logging to workflow_executions table
- Performance timing tracking

## Security Features

- CORS headers for web app access
- Input validation and sanitization
- Secure credential handling
- Row Level Security (RLS) integration

## Usage in Workflows

Once deployed, Gmail, Outlook, and Custom SMTP integrations will automatically use the server-side function instead of client-side simulation.

## Troubleshooting

### Common Issues:

1. **Function not found**: Make sure you've deployed with `supabase functions deploy send-email`

2. **Authentication errors**: Verify your Supabase project is linked correctly

3. **SMTP connection issues**: 
   - For Gmail: Ensure 2FA is enabled and you're using an app password
   - For Outlook: Try both regular password and app password
   - For Custom SMTP: Verify host, port, and TLS settings

4. **Network timeouts**: SMTP connections may take a few seconds, this is normal

### Logs and Debugging:

View function logs:
```bash
supabase functions logs send-email
```

Or check logs in the Supabase dashboard under Functions → send-email → Logs.
