-- Fix Existing Data and Constraints
-- This fixes existing data that violates constraints, then applies proper constraints

-- 1. Add subscription_plan column if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'free';

-- 2. First, let's see what data we have
SELECT 'Current profiles data:' as info;
SELECT id, email, role, subscription_plan FROM profiles;

-- 2. Fix any invalid role values in existing data
UPDATE profiles 
SET role = 'user' 
WHERE role IS NULL OR role NOT IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner');

-- 3. Fix any invalid subscription_plan values in existing data
UPDATE profiles 
SET subscription_plan = 'free' 
WHERE subscription_plan IS NULL OR subscription_plan NOT IN ('free', 'basic', 'pro', 'enterprise');

-- 4. Show what we fixed
SELECT 'After fixing data:' as info;
SELECT id, email, role, subscription_plan FROM profiles;

-- 5. Now drop the existing role constraint if it exists
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find the role constraint name
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'profiles'::regclass 
    AND pg_get_constraintdef(oid) LIKE '%role%';
    
    -- Drop it if found
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE profiles DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

-- 6. Drop existing subscription_plan constraint if it exists
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find any existing subscription_plan constraint
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'profiles'::regclass 
    AND pg_get_constraintdef(oid) LIKE '%subscription_plan%';
    
    -- Drop it if found
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE profiles DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

-- 7. Add the correct role constraint (now that data is clean)
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('user', 'admin', 'super_admin', 'owner', 'platform_owner'));

-- 8. Add subscription_plan constraint (now that data is clean)
ALTER TABLE profiles ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('free', 'basic', 'pro', 'enterprise'));

-- 9. Verify constraints are working
SELECT 'Current constraints:' as info;
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

-- 10. Show final data
SELECT 'Final profiles data:' as info;
SELECT id, email, role, subscription_plan FROM profiles;

SELECT 'Data and constraints fixed successfully!' as status;
