-- Clean up and fix blog policies (Run this to resolve conflicts)
-- This will remove all existing policies and recreate them properly

-- 1. Drop ALL existing blog_posts policies to start fresh
DROP POLICY IF EXISTS "Public can view MBI published posts" ON blog_posts;
DROP POLICY IF EXISTS "Organization members can view private posts" ON blog_posts;
DROP POLICY IF EXISTS "All org members can view private posts" ON blog_posts;
DROP POLICY IF EXISTS "Super admins can view all posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can create private posts" ON blog_posts;
DROP POLICY IF EXISTS "All org members can create private posts" ON blog_posts;
DROP POLICY IF EXISTS "Users can update posts in their scope" ON blog_posts;
DROP POLICY IF EXISTS "Users can delete posts in their scope" ON blog_posts;
DROP POLICY IF EXISTS "Temporary: Allow viewing all posts during migration" ON blog_posts;
DROP POLICY IF EXISTS "Migration temp policy - view all posts" ON blog_posts;
DROP POLICY IF EXISTS "Temporary: Public can view all published posts" ON blog_posts;

-- 2. Drop ALL existing blog_submissions policies
DROP POLICY IF EXISTS "Organization members can view submissions" ON blog_submissions;
DROP POLICY IF EXISTS "Organization admins can create submissions" ON blog_submissions;
DROP POLICY IF EXISTS "All org members can create submissions" ON blog_submissions;
DROP POLICY IF EXISTS "Super admins can update submissions" ON blog_submissions;

-- 3. Fix existing blog data first
UPDATE blog_posts 
SET 
  submission_status = COALESCE(submission_status, 'private'),
  organization_id = COALESCE(
    organization_id,
    (
      SELECT om.organization_id 
      FROM organization_members om 
      WHERE om.user_id = blog_posts.author_id 
      AND om.is_active = true 
      LIMIT 1
    )
  )
WHERE submission_status IS NULL OR organization_id IS NULL;

-- 4. Handle orphaned posts
DO $$
DECLARE
  default_org_id UUID;
  orphaned_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO orphaned_count FROM blog_posts WHERE organization_id IS NULL;
  
  IF orphaned_count > 0 THEN
    SELECT id INTO default_org_id FROM organizations ORDER BY created_at ASC LIMIT 1;
    
    IF default_org_id IS NULL THEN
      INSERT INTO organizations (name, slug, description, subscription_plan)
      VALUES ('Default Organization', 'default-org', 'Default organization for existing content', 'free')
      RETURNING id INTO default_org_id;
    END IF;
    
    UPDATE blog_posts SET organization_id = default_org_id WHERE organization_id IS NULL;
    RAISE NOTICE 'Fixed % orphaned posts', orphaned_count;
  END IF;
END $$;

-- 5. Create NEW, clean policies

-- TEMPORARY: Allow viewing all posts during transition (so you can see your content)
CREATE POLICY "temp_view_all_posts" ON blog_posts
  FOR SELECT USING (true);

-- Public can view MBI-approved posts
CREATE POLICY "public_view_mbi_posts" ON blog_posts
  FOR SELECT USING (
    status = 'published' AND 
    (submission_status = 'published_mbi' OR organization_id IS NULL)
  );

-- All organization members can view their private workspace posts
CREATE POLICY "org_members_view_private" ON blog_posts
  FOR SELECT USING (
    submission_status = 'private' AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Super admins can view everything
CREATE POLICY "super_admin_view_all" ON blog_posts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'super_admin'
    )
  );

-- All org members can create private posts
CREATE POLICY "org_members_create_posts" ON blog_posts
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    ) AND
    submission_status = 'private'
  );

-- Users can update their own posts, admins can update org posts
CREATE POLICY "update_posts_policy" ON blog_posts
  FOR UPDATE USING (
    (auth.uid() = author_id) OR
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true AND role IN ('admin', 'owner')
    )) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- Users can delete their own posts, admins can delete org posts
CREATE POLICY "delete_posts_policy" ON blog_posts
  FOR DELETE USING (
    (auth.uid() = author_id) OR
    (submission_status = 'private' AND organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true AND role IN ('admin', 'owner')
    )) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- 6. Blog submissions policies
CREATE POLICY "view_submissions_policy" ON blog_submissions
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    ) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

CREATE POLICY "create_submissions_policy" ON blog_submissions
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid() AND is_active = true
    ) AND
    submitted_by = auth.uid()
  );

CREATE POLICY "update_submissions_policy" ON blog_submissions
  FOR UPDATE USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'super_admin')
  );

-- 7. Show status
SELECT 
    'Blog System Fixed!' as status,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts,
    COUNT(*) FILTER (WHERE organization_id IS NOT NULL) as posts_with_org
FROM blog_posts;
