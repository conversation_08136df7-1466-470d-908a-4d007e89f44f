-- Step-by-Step Organization Fix
-- Run each query separately in Supabase SQL Editor

-- STEP 1: Check your user ID
SELECT id, email FROM auth.users WHERE email LIKE '%stephen%' OR email LIKE '%secia%';

-- STEP 2: Create organization (replace USER_ID_HERE with the actual ID from step 1)
-- INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit, workflow_credits_used, ai_credits_used)
-- VALUES (
--   'My Workspace',
--   'user-YOUR_USER_ID_HERE',
--   'free',
--   100,
--   50,
--   0,
--   0
-- );

-- STEP 3: Add yourself as owner (replace USER_ID_HERE and ORG_SLUG_HERE)
-- INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
-- SELECT 
--   o.id,
--   'YOUR_USER_ID_HERE'::uuid,
--   'owner',
--   NOW(),
--   true
-- FROM organizations o
-- WHERE o.slug = 'user-YOUR_USER_ID_HERE';

-- STEP 4: Verify the setup
-- SELECT 
--   p.email,
--   o.name as organization_name,
--   o.slug,
--   o.subscription_plan,
--   om.role
-- FROM profiles p
-- JOIN organization_members om ON om.user_id = p.id AND om.is_active = true
-- JOIN organizations o ON o.id = om.organization_id
-- WHERE p.id = 'YOUR_USER_ID_HERE'::uuid;
