-- Final Organization Fix - Simple Version
-- Copy and paste each section separately into Supabase SQL Editor

-- 1. First, check your user ID
SELECT id, email FROM auth.users;

-- 2. Create organization (replace YOUR_USER_ID with the actual ID from step 1)
INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit, workflow_credits_used, ai_credits_used)
VALUES (
  'My Workspace',
  'user-YOUR_USER_ID',
  'free',
  100,
  50,
  0,
  0
);

-- 3. Add yourself as owner (replace YOUR_USER_ID with the actual ID)
INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
SELECT 
  o.id,
  'YOUR_USER_ID'::uuid,
  'owner',
  NOW(),
  true
FROM organizations o
WHERE o.slug = 'user-YOUR_USER_ID';

-- 4. Verify it worked
SELECT 
  u.email,
  o.name as organization_name,
  o.slug,
  o.subscription_plan,
  om.role
FROM auth.users u
JOIN organization_members om ON om.user_id = u.id AND om.is_active = true
JOIN organizations o ON o.id = om.organization_id;
